-- Merging decision tree log ---
provider#org.apache.cordova.camera.FileProvider
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:11:9-13:20
	android:grantUriPermissions
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:11:114-148
	android:authorities
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:11:19-88
	android:exported
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:11:89-113
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:11:149-202
provider#io.github.pwlin.cordova.plugins.fileopener2.FileProvider
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:14:9-16:20
	android:grantUriPermissions
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:14:104-138
	android:authorities
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:14:19-78
	android:exported
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:14:79-103
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:14:139-210
manifest
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:1-39:12
MERGED from [:CordovaLib] C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\CordovaLib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:1-27:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4e779dac15964172e8281fcc340e5e4b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\79e8455b0b361421d45210661fab4253\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fa35d5508191c4f9f8ca09de1273d1b\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:17:1-37:12
MERGED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:17:1-65:12
MERGED from [com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.google.firebase:firebase-iid-interop:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9edded56c4f70e37dc52300d53db3c33\transformed\jetified-firebase-iid-interop-16.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:17:1-29:12
MERGED from [com.google.android.gms:play-services-tasks:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb5fea80982cf83d0f80adf02d2f7d9c\transformed\jetified-play-services-tasks-15.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4da8b71586307e168772af381461efac\transformed\jetified-firebase-measurement-connector-16.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-stats:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\20415a692f96404717f0d848812ebb8e\transformed\jetified-play-services-stats-15.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dbbbb036454436c7fdc3308785354d28\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\c205b40a1479e4b0967483dec7be224b\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\f58ddbb8fb877512725173fe50d31273\transformed\jetified-activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ddf8095f656ebcb5c3da656ad06b60\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98f857278bccaee7aaba6358f03d7cf3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aec2ef1a96378ed476ec94c2d632cb42\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ececa3a4d8b2daf838786a60c297330\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\4e0aeda3bdbea9b5d48d3c9b60688002\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\241d3085adf6d552726da229f3d5ae51\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\88a2f72a9e0ee145c249ff54c36531bb\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.webkit:webkit:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c28507ae0e662b1a929e088108a05e64\transformed\webkit-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f226682f752fe1ac397ee685b2d38f3\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0c4e1133211c99b9ad69ca21b433785\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea4e08c230ecce6ecea1dfb7aa34c49f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e28834f633e6340363f006c31ae87ee\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cacc886a61654635f3512cb757ce1454\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\70e8707fe3cb198c94a79c6e961b8616\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ded7bebc8d48acfa60e5fc2cb1b3713a\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c4bc68127d99b4e39af67336518aadd\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8725dcda9cb91b95aef4e29512a1743\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f38b42aa304cb840b218a139a89cfba9\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\94f129b684f3eb030d7d18d60243dd88\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\26e9e119b92be403cce12cd6ba6671cc\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cee36957b9557e350678f093b59561ef\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\e25abfe3307d9636ff461bf75ba8c05f\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4940f44d46dafb822c047b4dc7e048d4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2294c126cbbeb2dd2fa1d09deabd6c4\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1c2a72c3c545e9328854324075c145b\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b5fa10e5defebdbbb4d6bfbcbb14c6f4\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\156401e28e9e6f70583887070c218915\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3aa7aedca59ea40658379599dcfbd986\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e2daccee4fe627d3fcd127d51be6d0e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9262866c28c9035a06f5c3842290030b\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b7deaaa74fb1a29425e5f7356aee583\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\809cb4d8026121424014779e2dedb5b7\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:1-39:12
	package
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:1-39:12
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
	android:versionName
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:74-101
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
	android:hardwareAccelerated
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:11-45
	xmlns:android
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:102-160
	android:versionCode
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:2:46-73
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
supports-screens
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:5-191
	android:largeScreens
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:49-76
	android:smallScreens
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:132-159
	android:normalScreens
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:77-105
	android:xlargeScreens
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:160-188
	android:resizeable
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:106-131
	android:anyDensity
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:23-48
application
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:5-17:19
MERGED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:22:5-35:19
MERGED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:22:5-35:19
MERGED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:34:5-63:19
MERGED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:34:5-63:19
MERGED from [com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:8:5-14:19
MERGED from [com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:8:5-14:19
MERGED from [com.google.firebase:firebase-iid-interop:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9edded56c4f70e37dc52300d53db3c33\transformed\jetified-firebase-iid-interop-16.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9edded56c4f70e37dc52300d53db3c33\transformed\jetified-firebase-iid-interop-16.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:22:5-27:19
MERGED from [com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:22:5-27:19
MERGED from [com.google.android.gms:play-services-tasks:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb5fea80982cf83d0f80adf02d2f7d9c\transformed\jetified-play-services-tasks-15.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb5fea80982cf83d0f80adf02d2f7d9c\transformed\jetified-play-services-tasks-15.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4da8b71586307e168772af381461efac\transformed\jetified-firebase-measurement-connector-16.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4da8b71586307e168772af381461efac\transformed\jetified-firebase-measurement-connector-16.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\20415a692f96404717f0d848812ebb8e\transformed\jetified-play-services-stats-15.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\20415a692f96404717f0d848812ebb8e\transformed\jetified-play-services-stats-15.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4940f44d46dafb822c047b4dc7e048d4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4940f44d46dafb822c047b4dc7e048d4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2294c126cbbeb2dd2fa1d09deabd6c4\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2294c126cbbeb2dd2fa1d09deabd6c4\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:182-208
	android:label
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:88-120
	android:hardwareAccelerated
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:18-52
	android:icon
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:53-87
	android:networkSecurityConfig
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:121-181
	android:usesCleartextTraffic
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:209-244
activity#com.floriconstructionltd.admin.MainActivity
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:9-10:20
	android:label
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:160-197
	android:launchMode
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:198-228
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:303-345
	android:exported
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:136-159
	android:configChanges
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:19-135
	android:theme
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:257-302
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:229-256
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:6:13-9:29
	android:label
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:6:28-65
action#android.intent.action.MAIN
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:7:17-69
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:7:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:8:17-77
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:8:27-74
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:18:5-81
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:18:22-78
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:19:5-71
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:19:22-68
uses-permission#android.permission.RECORD_VIDEO
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:20:5-71
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:20:22-68
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:21:5-80
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:21:22-77
uses-permission#android.permission.VIBRATE
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:22:5-66
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:22:22-63
queries
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:23:5-37:15
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:24:9-26:18
action#android.media.action.IMAGE_CAPTURE
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:25:13-73
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:25:21-70
intent#action:name:android.intent.action.GET_CONTENT
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:27:9-29:18
action#android.intent.action.GET_CONTENT
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:28:13-72
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:28:21-69
intent#action:name:android.intent.action.PICK
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:30:9-32:18
action#android.intent.action.PICK
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:31:13-65
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:31:21-62
intent#action:name:com.android.camera.action.CROP+data:mimeType:image/*+data:scheme:content
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:33:9-36:18
action#com.android.camera.action.CROP
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:34:13-69
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:34:21-66
data
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:35:13-73
	android:scheme
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:35:46-70
	android:mimeType
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:35:19-45
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:38:5-79
MERGED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:38:22-76
uses-sdk
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
MERGED from [:CordovaLib] C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\CordovaLib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:5-44
MERGED from [:CordovaLib] C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\CordovaLib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4e779dac15964172e8281fcc340e5e4b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4e779dac15964172e8281fcc340e5e4b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\79e8455b0b361421d45210661fab4253\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\79e8455b0b361421d45210661fab4253\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fa35d5508191c4f9f8ca09de1273d1b\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fa35d5508191c4f9f8ca09de1273d1b\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:5:5-6:37
MERGED from [com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:5:5-6:37
MERGED from [com.google.firebase:firebase-iid-interop:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9edded56c4f70e37dc52300d53db3c33\transformed\jetified-firebase-iid-interop-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9edded56c4f70e37dc52300d53db3c33\transformed\jetified-firebase-iid-interop-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb5fea80982cf83d0f80adf02d2f7d9c\transformed\jetified-play-services-tasks-15.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb5fea80982cf83d0f80adf02d2f7d9c\transformed\jetified-play-services-tasks-15.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4da8b71586307e168772af381461efac\transformed\jetified-firebase-measurement-connector-16.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4da8b71586307e168772af381461efac\transformed\jetified-firebase-measurement-connector-16.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\20415a692f96404717f0d848812ebb8e\transformed\jetified-play-services-stats-15.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\20415a692f96404717f0d848812ebb8e\transformed\jetified-play-services-stats-15.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dbbbb036454436c7fdc3308785354d28\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dbbbb036454436c7fdc3308785354d28\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\c205b40a1479e4b0967483dec7be224b\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\c205b40a1479e4b0967483dec7be224b\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\f58ddbb8fb877512725173fe50d31273\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\f58ddbb8fb877512725173fe50d31273\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ddf8095f656ebcb5c3da656ad06b60\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ddf8095f656ebcb5c3da656ad06b60\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98f857278bccaee7aaba6358f03d7cf3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98f857278bccaee7aaba6358f03d7cf3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aec2ef1a96378ed476ec94c2d632cb42\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aec2ef1a96378ed476ec94c2d632cb42\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ececa3a4d8b2daf838786a60c297330\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ececa3a4d8b2daf838786a60c297330\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\4e0aeda3bdbea9b5d48d3c9b60688002\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\4e0aeda3bdbea9b5d48d3c9b60688002\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\241d3085adf6d552726da229f3d5ae51\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\241d3085adf6d552726da229f3d5ae51\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\88a2f72a9e0ee145c249ff54c36531bb\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\88a2f72a9e0ee145c249ff54c36531bb\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.webkit:webkit:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c28507ae0e662b1a929e088108a05e64\transformed\webkit-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c28507ae0e662b1a929e088108a05e64\transformed\webkit-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f226682f752fe1ac397ee685b2d38f3\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f226682f752fe1ac397ee685b2d38f3\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0c4e1133211c99b9ad69ca21b433785\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0c4e1133211c99b9ad69ca21b433785\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea4e08c230ecce6ecea1dfb7aa34c49f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea4e08c230ecce6ecea1dfb7aa34c49f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e28834f633e6340363f006c31ae87ee\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e28834f633e6340363f006c31ae87ee\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cacc886a61654635f3512cb757ce1454\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cacc886a61654635f3512cb757ce1454\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\70e8707fe3cb198c94a79c6e961b8616\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\70e8707fe3cb198c94a79c6e961b8616\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ded7bebc8d48acfa60e5fc2cb1b3713a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ded7bebc8d48acfa60e5fc2cb1b3713a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c4bc68127d99b4e39af67336518aadd\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c4bc68127d99b4e39af67336518aadd\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8725dcda9cb91b95aef4e29512a1743\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8725dcda9cb91b95aef4e29512a1743\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f38b42aa304cb840b218a139a89cfba9\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f38b42aa304cb840b218a139a89cfba9\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\94f129b684f3eb030d7d18d60243dd88\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\94f129b684f3eb030d7d18d60243dd88\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\26e9e119b92be403cce12cd6ba6671cc\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\26e9e119b92be403cce12cd6ba6671cc\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cee36957b9557e350678f093b59561ef\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cee36957b9557e350678f093b59561ef\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\e25abfe3307d9636ff461bf75ba8c05f\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\e25abfe3307d9636ff461bf75ba8c05f\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4940f44d46dafb822c047b4dc7e048d4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4940f44d46dafb822c047b4dc7e048d4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2294c126cbbeb2dd2fa1d09deabd6c4\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2294c126cbbeb2dd2fa1d09deabd6c4\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1c2a72c3c545e9328854324075c145b\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1c2a72c3c545e9328854324075c145b\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b5fa10e5defebdbbb4d6bfbcbb14c6f4\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b5fa10e5defebdbbb4d6bfbcbb14c6f4\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\156401e28e9e6f70583887070c218915\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\156401e28e9e6f70583887070c218915\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3aa7aedca59ea40658379599dcfbd986\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3aa7aedca59ea40658379599dcfbd986\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e2daccee4fe627d3fcd127d51be6d0e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e2daccee4fe627d3fcd127d51be6d0e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9262866c28c9035a06f5c3842290030b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9262866c28c9035a06f5c3842290030b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b7deaaa74fb1a29425e5f7356aee583\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b7deaaa74fb1a29425e5f7356aee583\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\809cb4d8026121424014779e2dedb5b7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\809cb4d8026121424014779e2dedb5b7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
		INJECTED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:12:13-123
	android:resource
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:12:75-120
	android:name
		ADDED from C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:12:24-74
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:28:9-34:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:30:13-36
	android:name
		ADDED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:29:13-82
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:31:13-33:29
	android:priority
		ADDED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:31:28-51
action#com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:32:17-78
	android:name
		ADDED from [com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:32:25-75
uses-permission#android.permission.INTERNET
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:23:22-64
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:24:5-68
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:26:5-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:26:22-79
permission#${applicationId}.permission.C2D_MESSAGE
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:28:5-30:47
	android:protectionLevel
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:30:9-44
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:29:9-63
permission#com.floriconstructionltd.admin.permission.C2D_MESSAGE
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:28:5-30:47
	android:protectionLevel
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:30:9-44
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:29:9-63
uses-permission#${applicationId}.permission.C2D_MESSAGE
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:32:5-79
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:32:22-76
uses-permission#com.floriconstructionltd.admin.permission.C2D_MESSAGE
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:32:5-79
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:32:22-76
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:35:9-39:19
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:35:18-89
meta-data#com.google.firebase.components:com.google.firebase.iid.Registrar
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:38:17-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:37:17-96
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:41:9-50:20
	android:exported
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:43:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:44:13-73
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:42:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE+category:name:${applicationId}
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:45:13-49:29
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE+category:name:com.floriconstructionltd.admin
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:45:13-49:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:46:17-81
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:46:25-78
category#${applicationId}
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:48:17-61
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:48:27-58
category#com.floriconstructionltd.admin
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:48:17-61
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:48:27-58
service#com.google.firebase.iid.FirebaseInstanceIdService
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:56:9-62:19
	android:exported
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:58:13-36
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:57:13-77
intent-filter#action:name:com.google.firebase.INSTANCE_ID_EVENT
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:59:13-61:29
	android:priority
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:59:28-51
action#com.google.firebase.INSTANCE_ID_EVENT
ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:60:17-80
	android:name
		ADDED from [com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:60:25-77
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:9:9-13:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:11:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:13:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:10:13-77
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:23:9-26:75
	android:exported
		ADDED from [com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:26:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:24:13-79
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:23:9-25:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:25:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:24:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2294c126cbbeb2dd2fa1d09deabd6c4\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2294c126cbbeb2dd2fa1d09deabd6c4\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.floriconstructionltd.admin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.floriconstructionltd.admin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
