/**
 * Enhanced Toast Notification System for Android App
 * Provides native-style notifications with better UX
 */

class ToastSystem {
    constructor() {
        this.container = null;
        this.activeToasts = [];
        this.maxToasts = 3;
        this.init();
    }

    init() {
        // Create toast container if it doesn't exist
        this.container = document.getElementById('toast-container');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'toast-container';
            this.container.className = 'toast-container';
            document.body.appendChild(this.container);
        }
    }

    show(message, type = 'info', duration = 3000) {
        // Remove oldest toast if we have too many
        if (this.activeToasts.length >= this.maxToasts) {
            this.remove(this.activeToasts[0]);
        }

        const toast = this.createToast(message, type, duration);
        this.container.appendChild(toast);
        this.activeToasts.push(toast);

        // Trigger animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // Auto remove after duration
        if (duration > 0) {
            setTimeout(() => {
                this.remove(toast);
            }, duration);
        }

        // Add click to dismiss
        toast.addEventListener('click', () => {
            this.remove(toast);
        });

        return toast;
    }

    createToast(message, type, duration) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        // Add native styling for Android
        if (window.cordovaIntegration && window.cordovaIntegration.isReady()) {
            toast.classList.add('native');
        }

        const icon = this.getIcon(type);
        const progressBar = duration > 0 ? this.createProgressBar(duration) : '';

        toast.innerHTML = `
            <div class="toast-content">
                <div class="toast-icon">${icon}</div>
                <div class="toast-message">${message}</div>
                <button class="toast-close" aria-label="Close">×</button>
            </div>
            ${progressBar}
        `;

        // Close button functionality
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.remove(toast);
        });

        return toast;
    }

    createProgressBar(duration) {
        return `
            <div class="toast-progress">
                <div class="toast-progress-bar" style="animation-duration: ${duration}ms;"></div>
            </div>
        `;
    }

    getIcon(type) {
        const icons = {
            success: '<i class="fas fa-check-circle"></i>',
            error: '<i class="fas fa-exclamation-circle"></i>',
            warning: '<i class="fas fa-exclamation-triangle"></i>',
            info: '<i class="fas fa-info-circle"></i>',
            loading: '<i class="fas fa-spinner fa-spin"></i>'
        };
        return icons[type] || icons.info;
    }

    remove(toast) {
        if (!toast || !toast.parentNode) return;

        toast.classList.add('removing');
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
            
            const index = this.activeToasts.indexOf(toast);
            if (index > -1) {
                this.activeToasts.splice(index, 1);
            }
        }, 300);
    }

    clear() {
        this.activeToasts.forEach(toast => this.remove(toast));
        this.activeToasts = [];
    }

    // Convenience methods
    success(message, duration = 3000) {
        return this.show(message, 'success', duration);
    }

    error(message, duration = 5000) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration = 4000) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration = 3000) {
        return this.show(message, 'info', duration);
    }

    loading(message) {
        return this.show(message, 'loading', 0); // No auto-dismiss for loading
    }

    // Native Android toast (if available)
    nativeToast(message, duration = 'short') {
        if (window.plugins && window.plugins.toast) {
            const toastDuration = duration === 'long' ? 'long' : 'short';
            window.plugins.toast.show(message, toastDuration, 'bottom');
        } else {
            // Fallback to web toast
            this.info(message);
        }
    }
}

// Enhanced CSS for toast system
const toastCSS = `
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    pointer-events: none;
}

.toast {
    background: #333;
    color: white;
    border-radius: 8px;
    margin-bottom: 10px;
    min-width: 300px;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.removing {
    transform: translateX(100%);
    opacity: 0;
}

.toast-content {
    display: flex;
    align-items: center;
    padding: 12px 16px;
}

.toast-icon {
    margin-right: 12px;
    font-size: 18px;
    flex-shrink: 0;
}

.toast-message {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    margin-left: 12px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.toast-close:hover {
    opacity: 1;
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.2);
}

.toast-progress-bar {
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    animation: toast-progress linear forwards;
}

@keyframes toast-progress {
    from { width: 100%; }
    to { width: 0%; }
}

/* Toast Types */
.toast-success {
    background: #27ae60;
}

.toast-error {
    background: #e74c3c;
}

.toast-warning {
    background: #f39c12;
}

.toast-info {
    background: #3498db;
}

.toast-loading {
    background: #9b59b6;
}

/* Native Android styling */
.toast.native {
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.toast.native.toast-success {
    background: #4caf50;
}

.toast.native.toast-error {
    background: #f44336;
}

.toast.native.toast-warning {
    background: #ff9800;
}

.toast.native.toast-info {
    background: #2196f3;
}

/* Mobile responsive */
@media (max-width: 480px) {
    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
    }
    
    .toast {
        min-width: auto;
        max-width: none;
    }
}
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = toastCSS;
document.head.appendChild(style);

// Initialize global toast system
window.ToastSystem = ToastSystem;
window.toast = new ToastSystem();

// Make it available to Cordova integration
if (window.cordovaIntegration) {
    window.cordovaIntegration.toast = window.toast;
}
