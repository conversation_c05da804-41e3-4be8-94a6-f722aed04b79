{"logs": [{"outputFile": "com.floriconstructionltd.admin.app-mergeDebugResources-27:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00fa6d42e7a8abf6c20b774bd480ebc9\\transformed\\core-1.9.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5791", "endColumns": "100", "endOffsets": "5887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\56a603f4ce392bc972e3af0135819ac2\\transformed\\jetified-firebase-messaging-17.0.0\\res\\values-ta\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "205", "endColumns": "100", "endOffsets": "305"}, "to": {"startLines": "47", "startColumns": "4", "startOffsets": "5604", "endColumns": "104", "endOffsets": "5704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60c284a716caeffc9a2cb1c843b92804\\transformed\\jetified-play-services-basement-15.0.1\\res\\values-ta\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "224", "endOffsets": "471"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "4013", "endColumns": "224", "endOffsets": "4233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d86008410acda0874d72ffb71d6307a\\transformed\\jetified-play-services-base-15.0.1\\res\\values-ta\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,348,532,657,761,972,1098,1237,1363,1605,1710,1894,2027,2240,2422,2510,2599", "endColumns": "102,183,124,103,210,125,138,125,241,104,183,132,212,181,87,88,105", "endOffsets": "347,531,656,760,971,1097,1236,1362,1604,1709,1893,2026,2239,2421,2509,2598,2704"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2871,2978,3162,3291,3399,3610,3740,3883,4238,4480,4589,4773,4910,5123,5309,5401,5494", "endColumns": "106,183,128,107,210,129,142,129,241,108,183,136,212,185,91,92,109", "endOffsets": "2973,3157,3286,3394,3605,3735,3878,4008,4475,4584,4768,4905,5118,5304,5396,5489,5599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\79e8455b0b361421d45210661fab4253\\transformed\\appcompat-1.6.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,5709", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,5786"}}]}]}