/**
 * Android Native Styles for Flori Construction Admin
 * Additional styles for native Android app features
 */

/* Cordova Status */
.cordova-status {
    font-size: 12px;
    color: #666;
    margin-top: 10px;
    text-align: center;
}

/* Native App Info */
.native-info {
    margin-top: 20px;
    padding: 15px;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 8px;
    text-align: center;
}

.native-info p {
    margin: 5px 0;
    font-size: 14px;
    color: #e74c3c;
}

/* Network Status Indicator */
.network-status {
    margin-right: 10px;
    display: flex;
    align-items: center;
}

.network-status i {
    font-size: 18px;
    color: #27ae60;
    transition: color 0.3s ease;
}

.network-status.offline i {
    color: #e74c3c;
}

/* Native Features Section in Sidebar */
.native-features {
    margin-top: 20px;
    padding: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.native-features h4 {
    color: #fff;
    font-size: 14px;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.native-btn {
    display: block;
    width: 100%;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.native-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.native-btn i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* Native Status Panel */
.native-status-panel {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.native-status-panel h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 18px;
}

.status-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #e74c3c;
}

.status-item span:first-child {
    font-weight: 600;
    color: #2c3e50;
}

.status-item span:last-child {
    color: #666;
    font-family: monospace;
    font-size: 12px;
}

/* Android Material Design Enhancements */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

/* Enhanced Touch Targets for Android */
.menu-item,
.btn,
.native-btn {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-item {
    justify-content: flex-start;
    padding: 12px 20px;
}

/* Android Status Bar Compensation */
body {
    padding-top: env(safe-area-inset-top);
}

.app-header {
    padding-top: env(safe-area-inset-top);
}

/* Android Navigation Bar Compensation */
.main-content {
    padding-bottom: env(safe-area-inset-bottom);
}

/* Splash Screen Enhancements */
.loading-screen {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
}

.loading-spinner {
    border-color: rgba(255, 255, 255, 0.3);
    border-top-color: white;
}

/* Android Keyboard Adjustments */
.form-group input:focus {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

/* Native Camera Integration */
.camera-preview {
    width: 100%;
    max-width: 300px;
    height: 200px;
    background: #f0f0f0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px auto;
    border: 2px dashed #ddd;
}

.camera-preview img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 6px;
}

.camera-preview .placeholder {
    color: #999;
    text-align: center;
}

.camera-preview .placeholder i {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
}

/* File Upload Enhancements */
.file-upload-area {
    border: 2px dashed #e74c3c;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background: rgba(231, 76, 60, 0.05);
    transition: all 0.3s ease;
}

.file-upload-area.dragover {
    border-color: #c0392b;
    background: rgba(231, 76, 60, 0.1);
}

.file-upload-area i {
    font-size: 48px;
    color: #e74c3c;
    margin-bottom: 10px;
}

/* Offline Indicator */
.offline-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #f39c12;
    color: white;
    text-align: center;
    padding: 8px;
    font-size: 14px;
    z-index: 9999;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.offline-indicator.show {
    transform: translateY(0);
}

/* Android Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .native-status-panel {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    .native-status-panel h3 {
        color: #ecf0f1;
    }
    
    .status-item {
        background: #34495e;
    }
    
    .status-item span:first-child {
        color: #ecf0f1;
    }
    
    .status-item span:last-child {
        color: #bdc3c7;
    }
}

/* Responsive Adjustments for Android */
@media (max-width: 768px) {
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .native-status-panel {
        margin: 10px;
        padding: 15px;
    }
    
    .native-features {
        padding: 10px;
    }
    
    .native-btn {
        padding: 10px 12px;
        font-size: 14px;
    }
}

/* Android Specific Animations */
@keyframes androidSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes androidFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.page.active {
    animation: androidFadeIn 0.3s ease-out;
}

.sidebar.active {
    animation: androidSlideIn 0.3s ease-out;
}

/* Android Hardware Back Button Support */
.back-button-highlight {
    background: rgba(231, 76, 60, 0.1) !important;
    transition: background 0.2s ease;
}

/* Native Toast Enhancements */
.toast {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.toast.native {
    background: #2c3e50;
    color: white;
    border: none;
}

.toast.native.success {
    background: #27ae60;
}

.toast.native.error {
    background: #e74c3c;
}

.toast.native.warning {
    background: #f39c12;
}

.toast.native.info {
    background: #3498db;
}
