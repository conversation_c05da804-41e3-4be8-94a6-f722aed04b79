// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		30486FEB1A40DC350065C233 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 30486FEA1A40DC350065C233 /* UIKit.framework */; };
		30486FED1A40DC3B0065C233 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 30486FEC1A40DC3A0065C233 /* Foundation.framework */; };
		30486FF91A40DCC70065C233 /* CDVCamera.m in Sources */ = {isa = PBXBuildFile; fileRef = 30486FF31A40DCC70065C233 /* CDVCamera.m */; };
		30486FFA1A40DCC70065C233 /* CDVJpegHeaderWriter.m in Sources */ = {isa = PBXBuildFile; fileRef = 30486FF61A40DCC70065C233 /* CDVJpegHeaderWriter.m */; };
		30486FFB1A40DCC70065C233 /* UIImage+CropScaleOrientation.m in Sources */ = {isa = PBXBuildFile; fileRef = 30486FF81A40DCC70065C233 /* UIImage+CropScaleOrientation.m */; };
		304870011A40DD620065C233 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 30486FFE1A40DD180065C233 /* CoreGraphics.framework */; };
		304870021A40DD860065C233 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 30486FFE1A40DD180065C233 /* CoreGraphics.framework */; };
		304870031A40DD8C0065C233 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 30486FEA1A40DC350065C233 /* UIKit.framework */; };
		304870051A40DD9A0065C233 /* MobileCoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 304870041A40DD9A0065C233 /* MobileCoreServices.framework */; };
		304870071A40DDAC0065C233 /* AssetsLibrary.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 304870061A40DDAC0065C233 /* AssetsLibrary.framework */; };
		304870091A40DDB90065C233 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 304870081A40DDB90065C233 /* CoreLocation.framework */; };
		3048700B1A40DDF30065C233 /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3048700A1A40DDF30065C233 /* ImageIO.framework */; };
		308F59B11A4228730031A4D4 /* libCordova.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7E9F519019DA0F8300DA31AC /* libCordova.a */; };
		7E9F51B119DA114400DA31AC /* CameraTest.m in Sources */ = {isa = PBXBuildFile; fileRef = 7E9F51B019DA114400DA31AC /* CameraTest.m */; };
		7E9F51B919DA1B1600DA31AC /* libCDVCameraLib.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7E9F519519DA102000DA31AC /* libCDVCameraLib.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		30486FFC1A40DCE80065C233 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7E9F518B19DA0F8300DA31AC /* CordovaLib.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = D2AAC07D0554694100DB518D;
			remoteInfo = CordovaLib;
		};
		7E9F518F19DA0F8300DA31AC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7E9F518B19DA0F8300DA31AC /* CordovaLib.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 68A32D7114102E1C006B237C;
			remoteInfo = CordovaLib;
		};
		7E9F51AC19DA10DE00DA31AC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7E9F517219DA09CE00DA31AC /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7E9F519419DA102000DA31AC;
			remoteInfo = CDVCameraLib;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		7E9F519319DA102000DA31AC /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		30486FEA1A40DC350065C233 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.1.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		30486FEC1A40DC3A0065C233 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.1.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		30486FF21A40DCC70065C233 /* CDVCamera.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CDVCamera.h; sourceTree = "<group>"; };
		30486FF31A40DCC70065C233 /* CDVCamera.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CDVCamera.m; sourceTree = "<group>"; };
		30486FF41A40DCC70065C233 /* CDVExif.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CDVExif.h; sourceTree = "<group>"; };
		30486FF51A40DCC70065C233 /* CDVJpegHeaderWriter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CDVJpegHeaderWriter.h; sourceTree = "<group>"; };
		30486FF61A40DCC70065C233 /* CDVJpegHeaderWriter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CDVJpegHeaderWriter.m; sourceTree = "<group>"; };
		30486FF71A40DCC70065C233 /* UIImage+CropScaleOrientation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+CropScaleOrientation.h"; sourceTree = "<group>"; };
		30486FF81A40DCC70065C233 /* UIImage+CropScaleOrientation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+CropScaleOrientation.m"; sourceTree = "<group>"; };
		30486FFE1A40DD180065C233 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.1.sdk/System/Library/Frameworks/CoreGraphics.framework; sourceTree = DEVELOPER_DIR; };
		304870041A40DD9A0065C233 /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.1.sdk/System/Library/Frameworks/MobileCoreServices.framework; sourceTree = DEVELOPER_DIR; };
		304870061A40DDAC0065C233 /* AssetsLibrary.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AssetsLibrary.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.1.sdk/System/Library/Frameworks/AssetsLibrary.framework; sourceTree = DEVELOPER_DIR; };
		304870081A40DDB90065C233 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.1.sdk/System/Library/Frameworks/CoreLocation.framework; sourceTree = DEVELOPER_DIR; };
		3048700A1A40DDF30065C233 /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.1.sdk/System/Library/Frameworks/ImageIO.framework; sourceTree = DEVELOPER_DIR; };
		7E9F518B19DA0F8300DA31AC /* CordovaLib.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = CordovaLib.xcodeproj; path = "../node_modules/cordova-ios/CordovaLib/CordovaLib.xcodeproj"; sourceTree = "<group>"; };
		7E9F519519DA102000DA31AC /* libCDVCameraLib.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libCDVCameraLib.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7E9F519F19DA102000DA31AC /* CDVCameraLibTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CDVCameraLibTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7E9F51A219DA102000DA31AC /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		7E9F51B019DA114400DA31AC /* CameraTest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CameraTest.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7E9F519219DA102000DA31AC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				308F59B11A4228730031A4D4 /* libCordova.a in Frameworks */,
				304870011A40DD620065C233 /* CoreGraphics.framework in Frameworks */,
				30486FED1A40DC3B0065C233 /* Foundation.framework in Frameworks */,
				30486FEB1A40DC350065C233 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7E9F519C19DA102000DA31AC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3048700B1A40DDF30065C233 /* ImageIO.framework in Frameworks */,
				304870091A40DDB90065C233 /* CoreLocation.framework in Frameworks */,
				304870071A40DDAC0065C233 /* AssetsLibrary.framework in Frameworks */,
				304870051A40DD9A0065C233 /* MobileCoreServices.framework in Frameworks */,
				304870031A40DD8C0065C233 /* UIKit.framework in Frameworks */,
				304870021A40DD860065C233 /* CoreGraphics.framework in Frameworks */,
				7E9F51B919DA1B1600DA31AC /* libCDVCameraLib.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		30486FF11A40DCC70065C233 /* CDVCameraLib */ = {
			isa = PBXGroup;
			children = (
				30486FF21A40DCC70065C233 /* CDVCamera.h */,
				30486FF31A40DCC70065C233 /* CDVCamera.m */,
				30486FF41A40DCC70065C233 /* CDVExif.h */,
				30486FF51A40DCC70065C233 /* CDVJpegHeaderWriter.h */,
				30486FF61A40DCC70065C233 /* CDVJpegHeaderWriter.m */,
				30486FF71A40DCC70065C233 /* UIImage+CropScaleOrientation.h */,
				30486FF81A40DCC70065C233 /* UIImage+CropScaleOrientation.m */,
			);
			name = CDVCameraLib;
			path = ../../../src/ios;
			sourceTree = "<group>";
		};
		308F59B01A4227A60031A4D4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				3048700A1A40DDF30065C233 /* ImageIO.framework */,
				304870081A40DDB90065C233 /* CoreLocation.framework */,
				304870061A40DDAC0065C233 /* AssetsLibrary.framework */,
				304870041A40DD9A0065C233 /* MobileCoreServices.framework */,
				30486FFE1A40DD180065C233 /* CoreGraphics.framework */,
				30486FEC1A40DC3A0065C233 /* Foundation.framework */,
				30486FEA1A40DC350065C233 /* UIKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		7E9F517119DA09CE00DA31AC = {
			isa = PBXGroup;
			children = (
				7E9F518B19DA0F8300DA31AC /* CordovaLib.xcodeproj */,
				308F59B01A4227A60031A4D4 /* Frameworks */,
				30486FF11A40DCC70065C233 /* CDVCameraLib */,
				7E9F51A019DA102000DA31AC /* CDVCameraLibTests */,
				7E9F517D19DA0A0A00DA31AC /* Products */,
			);
			sourceTree = "<group>";
		};
		7E9F517D19DA0A0A00DA31AC /* Products */ = {
			isa = PBXGroup;
			children = (
				7E9F519519DA102000DA31AC /* libCDVCameraLib.a */,
				7E9F519F19DA102000DA31AC /* CDVCameraLibTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7E9F518C19DA0F8300DA31AC /* Products */ = {
			isa = PBXGroup;
			children = (
				7E9F519019DA0F8300DA31AC /* libCordova.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7E9F51A019DA102000DA31AC /* CDVCameraLibTests */ = {
			isa = PBXGroup;
			children = (
				7E9F51A119DA102000DA31AC /* Supporting Files */,
				7E9F51B019DA114400DA31AC /* CameraTest.m */,
			);
			path = CDVCameraLibTests;
			sourceTree = "<group>";
		};
		7E9F51A119DA102000DA31AC /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				7E9F51A219DA102000DA31AC /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7E9F519419DA102000DA31AC /* CDVCameraLib */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7E9F51A319DA102000DA31AC /* Build configuration list for PBXNativeTarget "CDVCameraLib" */;
			buildPhases = (
				7E9F519119DA102000DA31AC /* Sources */,
				7E9F519219DA102000DA31AC /* Frameworks */,
				7E9F519319DA102000DA31AC /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
				30486FFD1A40DCE80065C233 /* PBXTargetDependency */,
			);
			name = CDVCameraLib;
			productName = CDVCameraLib;
			productReference = 7E9F519519DA102000DA31AC /* libCDVCameraLib.a */;
			productType = "com.apple.product-type.library.static";
		};
		7E9F519E19DA102000DA31AC /* CDVCameraLibTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7E9F51A619DA102000DA31AC /* Build configuration list for PBXNativeTarget "CDVCameraLibTests" */;
			buildPhases = (
				7E9F519B19DA102000DA31AC /* Sources */,
				7E9F519C19DA102000DA31AC /* Frameworks */,
				7E9F519D19DA102000DA31AC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7E9F51AD19DA10DE00DA31AC /* PBXTargetDependency */,
			);
			name = CDVCameraLibTests;
			productName = CDVCameraLibTests;
			productReference = 7E9F519F19DA102000DA31AC /* CDVCameraLibTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7E9F517219DA09CE00DA31AC /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0610;
				TargetAttributes = {
					7E9F519419DA102000DA31AC = {
						CreatedOnToolsVersion = 6.0;
					};
					7E9F519E19DA102000DA31AC = {
						CreatedOnToolsVersion = 6.0;
					};
				};
			};
			buildConfigurationList = 7E9F517519DA09CE00DA31AC /* Build configuration list for PBXProject "CDVCameraTest" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = 7E9F517119DA09CE00DA31AC;
			productRefGroup = 7E9F517D19DA0A0A00DA31AC /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 7E9F518C19DA0F8300DA31AC /* Products */;
					ProjectRef = 7E9F518B19DA0F8300DA31AC /* CordovaLib.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				7E9F519419DA102000DA31AC /* CDVCameraLib */,
				7E9F519E19DA102000DA31AC /* CDVCameraLibTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		7E9F519019DA0F8300DA31AC /* libCordova.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libCordova.a;
			remoteRef = 7E9F518F19DA0F8300DA31AC /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		7E9F519D19DA102000DA31AC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7E9F519119DA102000DA31AC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				30486FF91A40DCC70065C233 /* CDVCamera.m in Sources */,
				30486FFB1A40DCC70065C233 /* UIImage+CropScaleOrientation.m in Sources */,
				30486FFA1A40DCC70065C233 /* CDVJpegHeaderWriter.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7E9F519B19DA102000DA31AC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7E9F51B119DA114400DA31AC /* CameraTest.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		30486FFD1A40DCE80065C233 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = CordovaLib;
			targetProxy = 30486FFC1A40DCE80065C233 /* PBXContainerItemProxy */;
		};
		7E9F51AD19DA10DE00DA31AC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7E9F519419DA102000DA31AC /* CDVCameraLib */;
			targetProxy = 7E9F51AC19DA10DE00DA31AC /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		7E9F517619DA09CE00DA31AC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
			};
			name = Debug;
		};
		7E9F517719DA09CE00DA31AC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
			};
			name = Release;
		};
		7E9F51A419DA102000DA31AC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(TARGET_BUILD_DIR)/usr/local/lib/include\"",
					"\"$(OBJROOT)/UninstalledProducts/include\"",
					"\"$(BUILT_PRODUCTS_DIR)\"",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		7E9F51A519DA102000DA31AC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(TARGET_BUILD_DIR)/usr/local/lib/include\"",
					"\n\"$(OBJROOT)/UninstalledProducts/include\"\n\"$(BUILT_PRODUCTS_DIR)\"",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7E9F51A719DA102000DA31AC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(inherited)",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = CDVCameraLibTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					XCTest,
					"-all_load",
					"-ObjC",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		7E9F51A819DA102000DA31AC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(inherited)",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = CDVCameraLibTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					XCTest,
					"-all_load",
					"-ObjC",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7E9F517519DA09CE00DA31AC /* Build configuration list for PBXProject "CDVCameraTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7E9F517619DA09CE00DA31AC /* Debug */,
				7E9F517719DA09CE00DA31AC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7E9F51A319DA102000DA31AC /* Build configuration list for PBXNativeTarget "CDVCameraLib" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7E9F51A419DA102000DA31AC /* Debug */,
				7E9F51A519DA102000DA31AC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7E9F51A619DA102000DA31AC /* Build configuration list for PBXNativeTarget "CDVCameraLibTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7E9F51A719DA102000DA31AC /* Debug */,
				7E9F51A819DA102000DA31AC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7E9F517219DA09CE00DA31AC /* Project object */;
}
