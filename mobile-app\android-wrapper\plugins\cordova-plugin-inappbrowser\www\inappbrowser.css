﻿/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.inAppBrowserWrap {
    margin: 0;
    padding: 0;
    outline: 0;
    font-size: 100%;
    vertical-align: baseline;
    background: 0 0;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999999;
    box-sizing: border-box;
    border: 40px solid #bfbfbf;
    border: 40px solid rgba(0, 0, 0, 0.25);
}

.inAppBrowserWrapFullscreen {
    border: 0;
}

.inappbrowser-app-bar {
    height: 70px;
    background-color: #404040;
    z-index: 9999999;
}

.inappbrowser-app-bar-inner {
    padding-top: 10px;
    height: 60px;
    width: 155px;
    margin: 0 auto;
    background-color: #404040;
    z-index: 9999999;
}

.app-bar-action {
    width: auto;
    height: 40px;
    margin-left: 20px;
    font-family: "Segoe UI Symbol";
    float: left;
    color: white;
    font-size: 12px;
    text-transform: lowercase;
    text-align: center;
    cursor: default;
}

.app-bar-action[disabled] {
    color: gray;
    /*disable click*/
    pointer-events: none;
}

.app-bar-action::before {
    font-size: 28px;
    display: block;
    height: 36px;
}

/* Back */
.action-back { 
    margin-left: 0px;
}

.action-back::before {
    content: "\E0BA";
}

.action-back:not([disabled]):hover::before {
    content: "\E0B3";
}

/* Forward */
.action-forward::before {
    content: "\E0AC";
}

.action-forward:not([disabled]):hover::before {
    content: "\E0AF";
}

/* Close */
.action-close::before {
    content: "\E0C7";
    /* close icon is larger so we re-size it to fit other icons */
    font-size: 20px;
    line-height: 40px;
}

.action-close:not([disabled]):hover::before {
    content: "\E0CA";
}
