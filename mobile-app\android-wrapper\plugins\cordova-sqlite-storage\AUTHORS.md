# Authors

## Common Javascript

- Extracted from DroidGap by @brodybits (<PERSON> aka <PERSON>)
- Fail-safe nested transaction support by @ef4 (<PERSON>)
- Translated to <PERSON>-<PERSON><PERSON><PERSON> using js2coffee tool by @brodybits (<PERSON>)
- API changes by @brodybits (<PERSON>)
- Transaction timing fixes to support PouchDB by @nolan<PERSON>on

## browser platform

- Using sql-asm-memory-growth.js built from sql.js by @kripken (Alon Zakai)

## Android version

- Extracted from DroidGap by @brodybits (<PERSON>)
- Transaction batch processing of Android version by @marcucio
- Maintained by @brodybits (<PERSON>)
- Fixes to support old Android versions by @nolan<PERSON>on
- Thanks to <PERSON> <<EMAIL>> for fixes to open/close callbacks and repeated open/close/delete operations

## iOS/macOS version

- Original authors: @davibe (<PERSON><PERSON> <<EMAIL>>) and @joenoon (<PERSON> <<EMAIL>>)
- Cordova 2.7+ port with background processing by @j3k0 (<PERSON><PERSON><PERSON> <ho<PERSON><EMAIL>>)
- Maintained by @brodybits (<PERSON>)

## Windows version

- SQLiteProxy.js by @vldmrrr (<PERSON>) and @brodybits (<PERSON>)
- Using SQLite3-WinRT C++ classes and SQLite3JS (Javascript part) by @doo (doo GmbH)
- Visual C++ build files for Windows 8.1, Windows Phone 8.1, and Windows 10 UWP by @brodybits (with some help from Visual Studio Express 2013)
- Thanks to @AllJoyn-Cordova for idea how to integrate Windows 8.1/Windows Phone 8.1 Visual C++ projects in plugin.xml
