{"name": "cordova-support-google-services", "version": "1.3.2", "description": "Cordova plugin to add google service support", "scripts": {"version": "replace -s 'version=\"(.+)(?=\">)' 'version=\"'$npm_package_version plugin.xml && git add plugin.xml", "postversion": "git push && git push --tags"}, "cordova": {"id": "cordova-support-google-services", "platforms": ["android"]}, "repository": {"type": "git", "url": "git+https://github.com/chemerisuk/cordova-support-google-services.git"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/chemerisuk)", "license": "MIT", "bugs": {"url": "https://github.com/chemerisuk/cordova-support-google-services/issues"}, "homepage": "https://github.com/chemerisuk/cordova-support-google-services#readme", "keywords": ["<PERSON><PERSON>", "google services", "firebase", "ecosystem:cordova", "cordova-android"]}