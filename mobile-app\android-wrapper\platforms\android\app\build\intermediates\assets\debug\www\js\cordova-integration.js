/**
 * Cordova Integration for Flori Construction Admin
 * Handles native Android features and device integration
 */

class CordovaIntegration {
    constructor() {
        this.isDeviceReady = false;
        this.deviceInfo = {};
        this.networkInfo = {};
        this.pushNotification = null;
        
        // Bind methods
        this.onDeviceReady = this.onDeviceReady.bind(this);
        this.onPause = this.onPause.bind(this);
        this.onResume = this.onResume.bind(this);
        this.onOnline = this.onOnline.bind(this);
        this.onOffline = this.onOffline.bind(this);
        
        this.init();
    }

    init() {
        console.log('CordovaIntegration: Initializing...');
        
        // Wait for Cordova to be ready
        document.addEventListener('deviceready', this.onDeviceReady, false);
        document.addEventListener('pause', this.onPause, false);
        document.addEventListener('resume', this.onResume, false);
        document.addEventListener('online', this.onOnline, false);
        document.addEventListener('offline', this.onOffline, false);
        
        // Update status
        this.updateCordovaStatus('Waiting for device ready...');
    }

    onDeviceReady() {
        console.log('CordovaIntegration: Device ready!');
        this.isDeviceReady = true;
        
        // Initialize device info
        this.initDeviceInfo();
        
        // Initialize network monitoring
        this.initNetworkMonitoring();
        
        // Initialize push notifications
        this.initPushNotifications();
        
        // Initialize native UI elements
        this.initNativeUI();
        
        // Initialize camera functionality
        this.initCamera();
        
        // Initialize file system
        this.initFileSystem();
        
        // Update status
        this.updateCordovaStatus('Native features ready');
        
        // Hide Cordova status after delay
        setTimeout(() => {
            const statusEl = document.getElementById('cordova-status');
            if (statusEl) statusEl.style.display = 'none';
        }, 2000);
        
        console.log('CordovaIntegration: All native features initialized');
    }

    initDeviceInfo() {
        if (window.device) {
            this.deviceInfo = {
                platform: device.platform,
                version: device.version,
                uuid: device.uuid,
                model: device.model,
                manufacturer: device.manufacturer
            };
            
            console.log('Device Info:', this.deviceInfo);
            this.updateDeviceDisplay();
            
            // Get app version
            if (window.cordova && window.cordova.getAppVersion) {
                cordova.getAppVersion.getVersionNumber().then((version) => {
                    document.getElementById('app-version').textContent = `Version: ${version}`;
                });
            }
        }
    }

    initNetworkMonitoring() {
        if (window.Connection && navigator.connection) {
            this.networkInfo = {
                type: navigator.connection.type,
                isOnline: navigator.onLine
            };
            
            this.updateNetworkDisplay();
            console.log('Network Info:', this.networkInfo);
        }
    }

    initPushNotifications() {
        if (window.PushNotification) {
            this.pushNotification = PushNotification.init({
                android: {
                    senderID: "YOUR_SENDER_ID", // Replace with your Firebase sender ID
                    icon: 'icon',
                    iconColor: '#e74c3c'
                }
            });

            this.pushNotification.on('registration', (data) => {
                console.log('Push registration:', data.registrationId);
                // Send registration ID to your server
                this.sendRegistrationToServer(data.registrationId);
            });

            this.pushNotification.on('notification', (data) => {
                console.log('Push notification received:', data);
                this.handlePushNotification(data);
            });

            this.pushNotification.on('error', (e) => {
                console.error('Push notification error:', e);
            });
        }
    }

    initNativeUI() {
        // Status bar configuration
        if (window.StatusBar) {
            StatusBar.styleDefault();
            StatusBar.backgroundColorByHexString('#e74c3c');
        }

        // Setup native button handlers
        this.setupNativeButtons();
    }

    setupNativeButtons() {
        // Camera button
        const cameraBtn = document.getElementById('camera-btn');
        if (cameraBtn) {
            cameraBtn.addEventListener('click', () => this.openCamera());
        }

        // Sync button
        const syncBtn = document.getElementById('sync-btn');
        if (syncBtn) {
            syncBtn.addEventListener('click', () => this.syncData());
        }
    }

    initCamera() {
        // Camera functionality will be initialized here
        console.log('CordovaIntegration: Camera initialized');
    }

    initFileSystem() {
        // File system access initialization
        if (window.requestFileSystem) {
            window.requestFileSystem(LocalFileSystem.PERSISTENT, 0, 
                (fs) => {
                    console.log('File system initialized:', fs.name);
                    this.fileSystem = fs;
                },
                (error) => {
                    console.error('File system error:', error);
                }
            );
        }
    }

    // Camera Methods
    openCamera() {
        if (!window.Camera) {
            this.showToast('Camera not available', 'error');
            return;
        }

        const options = {
            quality: 75,
            destinationType: Camera.DestinationType.FILE_URI,
            sourceType: Camera.PictureSourceType.CAMERA,
            encodingType: Camera.EncodingType.JPEG,
            targetWidth: 1024,
            targetHeight: 1024,
            correctOrientation: true
        };

        navigator.camera.getPicture(
            (imageURI) => {
                console.log('Camera success:', imageURI);
                this.handleCameraSuccess(imageURI);
            },
            (error) => {
                console.error('Camera error:', error);
                this.showToast('Camera error: ' + error, 'error');
            },
            options
        );
    }

    handleCameraSuccess(imageURI) {
        // Handle successful camera capture
        console.log('Image captured:', imageURI);
        
        // Show preview or upload image
        this.showToast('Photo captured successfully!', 'success');
        
        // You can integrate this with your media upload functionality
        if (window.MediaManager && window.MediaManager.uploadFromCamera) {
            window.MediaManager.uploadFromCamera(imageURI);
        }
    }

    // Data Sync Methods
    syncData() {
        this.showToast('Syncing data...', 'info');
        
        // Implement data synchronization logic
        if (window.FloriAdmin && window.FloriAdmin.syncOfflineData) {
            window.FloriAdmin.syncOfflineData()
                .then(() => {
                    this.showToast('Data synced successfully!', 'success');
                })
                .catch((error) => {
                    console.error('Sync error:', error);
                    this.showToast('Sync failed: ' + error.message, 'error');
                });
        }
    }

    // Push Notification Methods
    sendRegistrationToServer(registrationId) {
        // Send the registration ID to your PHP backend
        if (window.FloriAdmin && window.FloriAdmin.apiBase) {
            fetch(`${window.FloriAdmin.apiBase}/push-registration.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${window.FloriAdmin.token}`
                },
                body: JSON.stringify({
                    registration_id: registrationId,
                    platform: 'android'
                })
            }).catch(error => {
                console.error('Failed to register for push notifications:', error);
            });
        }
    }

    handlePushNotification(data) {
        // Handle incoming push notification
        console.log('Handling push notification:', data);
        
        if (data.additionalData && data.additionalData.foreground) {
            // App is in foreground, show custom notification
            this.showToast(data.message, 'info');
        }
        
        // Mark notification as handled
        this.pushNotification.finish(() => {
            console.log('Push notification processed');
        });
    }

    // Event Handlers
    onPause() {
        console.log('App paused');
        // Save any pending data
    }

    onResume() {
        console.log('App resumed');
        // Refresh data if needed
        this.updateNetworkDisplay();
    }

    onOnline() {
        console.log('Device online');
        this.networkInfo.isOnline = true;
        this.updateNetworkDisplay();
        this.showToast('Connection restored', 'success');
    }

    onOffline() {
        console.log('Device offline');
        this.networkInfo.isOnline = false;
        this.updateNetworkDisplay();
        this.showToast('Connection lost', 'warning');
    }

    // UI Update Methods
    updateCordovaStatus(message) {
        const statusEl = document.getElementById('cordova-status');
        if (statusEl) {
            statusEl.textContent = message;
        }
    }

    updateDeviceDisplay() {
        const deviceInfoEl = document.getElementById('device-info');
        if (deviceInfoEl && this.deviceInfo.model) {
            deviceInfoEl.textContent = `${this.deviceInfo.manufacturer} ${this.deviceInfo.model}`;
        }
    }

    updateNetworkDisplay() {
        const networkStatusEl = document.getElementById('network-status');
        const networkInfoEl = document.getElementById('network-info');
        
        if (networkStatusEl) {
            const icon = networkStatusEl.querySelector('i');
            if (this.networkInfo.isOnline) {
                icon.className = 'fas fa-wifi';
                icon.title = 'Online';
                networkStatusEl.classList.remove('offline');
            } else {
                icon.className = 'fas fa-wifi-slash';
                icon.title = 'Offline';
                networkStatusEl.classList.add('offline');
            }
        }
        
        if (networkInfoEl) {
            const connectionType = this.networkInfo.type || 'Unknown';
            const status = this.networkInfo.isOnline ? 'Online' : 'Offline';
            networkInfoEl.textContent = `${status} (${connectionType})`;
        }
    }

    // Utility Methods
    showToast(message, type = 'info') {
        // Use the existing toast system from the main app
        if (window.FloriAdmin && window.FloriAdmin.showToast) {
            window.FloriAdmin.showToast(message, type);
        } else {
            console.log(`Toast [${type}]: ${message}`);
        }
    }

    // Public API
    isReady() {
        return this.isDeviceReady;
    }

    getDeviceInfo() {
        return this.deviceInfo;
    }

    getNetworkInfo() {
        return this.networkInfo;
    }
}

// Initialize Cordova integration
window.CordovaIntegration = new CordovaIntegration();

// Make it available globally
window.cordovaIntegration = window.CordovaIntegration;
