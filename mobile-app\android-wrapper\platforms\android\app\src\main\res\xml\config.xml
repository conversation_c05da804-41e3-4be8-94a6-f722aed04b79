<?xml version='1.0' encoding='utf-8'?>
<widget id="com.floriconstructionltd.admin" version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <feature name="Device">
        <param name="android-package" value="org.apache.cordova.device.Device" />
    </feature>
    <feature name="SplashScreen">
        <param name="android-package" value="org.apache.cordova.splashscreen.SplashScreen" />
        <param name="onload" value="true" />
    </feature>
    <feature name="NetworkStatus">
        <param name="android-package" value="org.apache.cordova.networkinformation.NetworkManager" />
    </feature>
    <feature name="File">
        <param name="android-package" value="org.apache.cordova.file.FileUtils" />
        <param name="onload" value="true" />
    </feature>
    <allow-navigation href="cdvfile:*" />
    <feature name="FileTransfer">
        <param name="android-package" value="org.apache.cordova.filetransfer.FileTransfer" />
    </feature>
    <feature name="SQLitePlugin">
        <param name="android-package" value="io.sqlc.SQLitePlugin" />
    </feature>
    <feature name="Camera">
        <param name="android-package" value="org.apache.cordova.camera.CameraLauncher" />
    </feature>
    <feature name="Capture">
        <param name="android-package" value="org.apache.cordova.mediacapture.Capture" />
    </feature>
    <feature name="PushNotification">
        <param name="android-package" value="com.adobe.phonegap.push.PushPlugin" />
    </feature>
    <feature name="Notification">
        <param name="android-package" value="org.apache.cordova.dialogs.Notification" />
    </feature>
    <feature name="InAppBrowser">
        <param name="android-package" value="org.apache.cordova.inappbrowser.InAppBrowser" />
    </feature>
    <feature name="AppVersion">
        <param name="android-package" value="uk.co.whiteoctober.cordova.AppVersion" />
    </feature>
    <feature name="StatusBar">
        <param name="android-package" value="org.apache.cordova.statusbar.StatusBar" />
        <param name="onload" value="true" />
    </feature>
    <name>Flori Construction Admin</name>
    <description>
        Native Android admin app for Flori Construction Ltd website management
    </description>
    <author email="<EMAIL>" href="https://floriconstructionltd.com">
        Flori Construction Ltd
    </author>
    <content src="index.html" />
    <allow-navigation href="*" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <meta content="default-src 'self' data: gap: https://ssl.gstatic.com 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; media-src *; img-src 'self' data: content:;" http-equiv="Content-Security-Policy" />
    <icon density="ldpi" src="res/android/icon/drawable-ldpi-icon.png" />
    <icon density="mdpi" src="res/android/icon/drawable-mdpi-icon.png" />
    <icon density="hdpi" src="res/android/icon/drawable-hdpi-icon.png" />
    <icon density="xhdpi" src="res/android/icon/drawable-xhdpi-icon.png" />
    <icon density="xxhdpi" src="res/android/icon/drawable-xxhdpi-icon.png" />
    <icon density="xxxhdpi" src="res/android/icon/drawable-xxxhdpi-icon.png" />
    <splash density="land-ldpi" src="res/android/splash/drawable-land-ldpi-screen.png" />
    <splash density="land-mdpi" src="res/android/splash/drawable-land-mdpi-screen.png" />
    <splash density="land-hdpi" src="res/android/splash/drawable-land-hdpi-screen.png" />
    <splash density="land-xhdpi" src="res/android/splash/drawable-land-xhdpi-screen.png" />
    <splash density="land-xxhdpi" src="res/android/splash/drawable-land-xxhdpi-screen.png" />
    <splash density="land-xxxhdpi" src="res/android/splash/drawable-land-xxxhdpi-screen.png" />
    <splash density="port-ldpi" src="res/android/splash/drawable-port-ldpi-screen.png" />
    <splash density="port-mdpi" src="res/android/splash/drawable-port-mdpi-screen.png" />
    <splash density="port-hdpi" src="res/android/splash/drawable-port-hdpi-screen.png" />
    <splash density="port-xhdpi" src="res/android/splash/drawable-port-xhdpi-screen.png" />
    <splash density="port-xxhdpi" src="res/android/splash/drawable-port-xxhdpi-screen.png" />
    <splash density="port-xxxhdpi" src="res/android/splash/drawable-port-xxxhdpi-screen.png" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application">
        <application android:networkSecurityConfig="@xml/network_security_config" />
        <application android:usesCleartextTraffic="true" />
    </edit-config>
    <preference name="loglevel" value="DEBUG" />
    <preference name="DisallowOverscroll" value="true" />
    <preference name="android-minSdkVersion" value="24" />
    <preference name="android-targetSdkVersion" value="33" />
    <preference name="android-compileSdkVersion" value="33" />
    <preference name="BackupWebStorage" value="local" />
    <preference name="SplashMaintainAspectRatio" value="true" />
    <preference name="FadeSplashScreenDuration" value="300" />
    <preference name="SplashShowOnlyFirstTime" value="false" />
    <preference name="SplashScreen" value="screen" />
    <preference name="SplashScreenDelay" value="3000" />
    <preference name="AutoHideSplashScreen" value="false" />
    <preference name="ShowSplashScreenSpinner" value="false" />
    <preference name="scheme" value="https" />
    <preference name="hostname" value="floriconstructionltd.com" />
    <preference name="android-build-tool" value="gradle" />
    <preference name="GradlePluginGoogleServicesEnabled" value="true" />
    <preference name="GradlePluginGoogleServicesVersion" value="4.3.10" />
</widget>
