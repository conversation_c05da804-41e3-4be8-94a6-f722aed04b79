{"name": "cordova-plugin-camera", "version": "4.1.0", "description": "Cordova Camera Plugin", "types": "./types/index.d.ts", "cordova": {"id": "cordova-plugin-camera", "platforms": ["android", "ios", "browser", "windows", "osx"]}, "repository": {"type": "git", "url": "https://github.com/apache/cordova-plugin-camera"}, "bugs": {"url": "https://github.com/apache/cordova-plugin-camera/issues"}, "keywords": ["<PERSON><PERSON>", "camera", "ecosystem:cordova", "cordova-android", "cordova-ios", "cordova-browser", "cordova-windows", "cordova-osx"], "scripts": {"test": "npm run eslint", "eslint": "node node_modules/eslint/bin/eslint www && node node_modules/eslint/bin/eslint src && node node_modules/eslint/bin/eslint tests"}, "author": "Apache Software Foundation", "license": "Apache-2.0", "engines": {"cordovaDependencies": {"3.0.0": {"cordova-android": ">=6.3.0"}, "4.1.0": {"cordova-android": ">=6.3.0", "cordova": ">=7.1.0"}, "5.0.0": {"cordova": ">100"}}}, "devDependencies": {"eslint": "^4.3.0", "eslint-config-semistandard": "^11.0.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.3.0", "eslint-plugin-node": "^5.0.0", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1"}}