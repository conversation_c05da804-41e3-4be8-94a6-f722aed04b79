﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="Globals">
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
    <HasSharedItems>true</HasSharedItems>
    <ItemsProjectGuid>{db84ae51-4b93-44f5-be22-1eae1833ecec}</ItemsProjectGuid>
    <ItemsRootNamespace>SQLite3</ItemsRootNamespace>
    <ItemsProjectName>SQLite3.Shared</ItemsProjectName>
    <CodeSharingProject>248F659F-DAC5-46E8-AC09-60EC9FC95053</CodeSharingProject>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories);$(MSBuildThisFileDirectory);$(MSBuildThisFileDirectory)..\..\..\..\node_modules\cordova-sqlite-storage-dependencies</AdditionalIncludeDirectories>
      <AdditionalOptions>/DSQLITE_THREADSAFE=1 /DSQLITE_DEFAULT_SYNCHRONOUS=3 /DSQLITE_DEFAULT_MEMSTATUS=0 /DSQLITE_OMIT_DECLTYPE /DSQLITE_OMIT_DEPRECATED /DSQLITE_OMIT_PROGRESS_CALLBACK /DSQLITE_OMIT_SHARED_CACHE /DSQLITE_TEMP_STORE=2 /DSQLITE_OMIT_LOAD_EXTENSION /DSQLITE_ENABLE_FTS3 /DSQLITE_ENABLE_FTS3_PARENTHESIS /DSQLITE_ENABLE_FTS4 /DSQLITE_ENABLE_RTREE /DSQLITE_DEFAULT_PAGE_SIZE=4096 /DSQLITE_OS_WINRT %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="$(MSBuildThisFileDirectory)..\..\..\..\node_modules\cordova-sqlite-storage-dependencies\sqlite3.h" />
    <ClInclude Include="$(MSBuildThisFileDirectory)Constants.h" />
    <ClInclude Include="$(MSBuildThisFileDirectory)Database.h" />
    <ClInclude Include="$(MSBuildThisFileDirectory)Statement.h" />
    <ClInclude Include="$(MSBuildThisFileDirectory)pch.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="$(MSBuildThisFileDirectory)Constants.cpp" />
    <ClCompile Include="$(MSBuildThisFileDirectory)Database.cpp" />
    <ClCompile Include="$(MSBuildThisFileDirectory)Statement.cpp" />
    <ClCompile Include="$(MSBuildThisFileDirectory)..\..\..\..\node_modules\cordova-sqlite-storage-dependencies\sqlite3.c">
      <CompileAsWinRT>false</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="$(MSBuildThisFileDirectory)pch.cpp">
      <PrecompiledHeader>Create</PrecompiledHeader>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ProjectCapability Include="SourceItemsFromImports" />
  </ItemGroup>
</Project>
