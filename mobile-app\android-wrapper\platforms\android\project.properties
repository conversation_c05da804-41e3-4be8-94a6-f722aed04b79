# This file was originally created by the Android Tools, but is now
# used by cordova-android to manage the state of the various third party
# libraries used in your application

# This is the Library Module that contains the Cordova Library, this is not
# required when using an AAR

# This is the application project.  This is only required for Android Studio Gradle projects

# Project target.
target=android-34
android.library.reference.1=CordovaLib
android.library.reference.2=app
cordova.system.library.1=com.google.firebase:firebase-messaging:17.0.+
