1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.floriconstructionltd.admin"
4    android:hardwareAccelerated="true"
5    android:versionCode="10000"
6    android:versionName="1.0.0" >
7
8    <uses-sdk
9        android:minSdkVersion="24"
9-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
10        android:targetSdkVersion="34" />
10-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
11
12    <supports-screens
12-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:5-191
13        android:anyDensity="true"
13-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:23-48
14        android:largeScreens="true"
14-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:49-76
15        android:normalScreens="true"
15-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:77-105
16        android:resizeable="true"
16-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:106-131
17        android:smallScreens="true"
17-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:132-159
18        android:xlargeScreens="true" />
18-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:160-188
19
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:12:5-81
20-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:12:22-78
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:13:5-71
21-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:13:22-68
22    <uses-permission android:name="android.permission.RECORD_VIDEO" />
22-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:14:5-71
22-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:14:22-68
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:15:5-80
23-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:15:22-77
24    <uses-permission android:name="android.permission.VIBRATE" />
24-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:16:5-66
24-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:16:22-63
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:17:5-79
25-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:17:22-76
26    <uses-permission android:name="android.permission.INTERNET" />
26-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:23:5-67
26-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:23:22-64
27    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
27-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:24:5-68
27-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:24:22-65
28    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
28-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:26:5-82
28-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:26:22-79
29
30    <permission
30-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:28:5-30:47
31        android:name="com.floriconstructionltd.admin.permission.C2D_MESSAGE"
31-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:29:9-63
32        android:protectionLevel="signature" />
32-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:30:9-44
33
34    <uses-permission android:name="com.floriconstructionltd.admin.permission.C2D_MESSAGE" />
34-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:32:5-79
34-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:32:22-76
35
36    <permission
36-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
37        android:name="com.floriconstructionltd.admin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.floriconstructionltd.admin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
41
42    <application
42-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:5-11:19
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:hardwareAccelerated="true"
46-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:18-52
47        android:icon="@mipmap/ic_launcher"
47-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:53-87
48        android:label="@string/app_name"
48-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:88-120
49        android:networkSecurityConfig="@xml/network_security_config"
49-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:121-181
50        android:supportsRtl="true"
50-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:182-208
51        android:usesCleartextTraffic="true" >
51-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:209-244
52        <activity
52-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:9-10:20
53            android:name="com.floriconstructionltd.admin.MainActivity"
53-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:229-256
54            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode"
54-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:19-135
55            android:exported="true"
55-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:136-159
56            android:label="@string/activity_name"
56-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:160-197
57            android:launchMode="singleTop"
57-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:198-228
58            android:theme="@style/Theme.App.SplashScreen"
58-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:257-302
59            android:windowSoftInputMode="adjustResize" >
59-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:303-345
60            <intent-filter android:label="@string/launcher_name" >
60-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:6:13-9:29
60-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:6:28-65
61                <action android:name="android.intent.action.MAIN" />
61-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:7:17-69
61-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:7:25-66
62
63                <category android:name="android.intent.category.LAUNCHER" />
63-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:8:17-77
63-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:8:27-74
64            </intent-filter>
65        </activity>
66        <!--
67             FirebaseMessagingService performs security checks at runtime,
68             no need for explicit permissions despite exported="true"
69        -->
70        <service
70-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:28:9-34:19
71            android:name="com.google.firebase.messaging.FirebaseMessagingService"
71-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:29:13-82
72            android:exported="true" >
72-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:30:13-36
73            <intent-filter android:priority="-500" >
73-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:31:13-33:29
73-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:31:28-51
74                <action android:name="com.google.firebase.MESSAGING_EVENT" />
74-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:32:17-78
74-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:32:25-75
75            </intent-filter>
76        </service>
77        <service android:name="com.google.firebase.components.ComponentDiscoveryService" >
77-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:35:9-39:19
77-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:35:18-89
78            <meta-data
78-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:36:13-38:85
79                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
79-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:37:17-96
80                android:value="com.google.firebase.components.ComponentRegistrar" />
80-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:38:17-82
81        </service>
82
83        <receiver
83-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:41:9-50:20
84            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
84-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:42:13-78
85            android:exported="true"
85-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:43:13-36
86            android:permission="com.google.android.c2dm.permission.SEND" >
86-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:44:13-73
87            <intent-filter>
87-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:45:13-49:29
88                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
88-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:46:17-81
88-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:46:25-78
89
90                <category android:name="com.floriconstructionltd.admin" />
90-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:48:17-61
90-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:48:27-58
91            </intent-filter>
92        </receiver>
93        <!--
94             FirebaseInstanceIdService performs security checks at runtime,
95             no need for explicit permissions despite exported="true"
96        -->
97        <service
97-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:56:9-62:19
98            android:name="com.google.firebase.iid.FirebaseInstanceIdService"
98-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:57:13-77
99            android:exported="true" >
99-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:58:13-36
100            <intent-filter android:priority="-500" >
100-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:59:13-61:29
100-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:59:28-51
101                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
101-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:60:17-80
101-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:60:25-77
102            </intent-filter>
103        </service>
104
105        <provider
105-->[com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:9:9-13:39
106            android:name="com.google.firebase.provider.FirebaseInitProvider"
106-->[com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:10:13-77
107            android:authorities="com.floriconstructionltd.admin.firebaseinitprovider"
107-->[com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:11:13-72
108            android:exported="false"
108-->[com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:12:13-37
109            android:initOrder="100" />
109-->[com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:13:13-36
110
111        <activity
111-->[com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:23:9-26:75
112            android:name="com.google.android.gms.common.api.GoogleApiActivity"
112-->[com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:24:13-79
113            android:exported="false"
113-->[com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:25:13-37
114            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
114-->[com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:26:13-72
115
116        <meta-data
116-->[com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:23:9-25:69
117            android:name="com.google.android.gms.version"
117-->[com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:24:13-58
118            android:value="@integer/google_play_services_version" />
118-->[com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:25:13-66
119
120        <provider
120-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
121            android:name="androidx.startup.InitializationProvider"
121-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
122            android:authorities="com.floriconstructionltd.admin.androidx-startup"
122-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
123            android:exported="false" >
123-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
124            <meta-data
124-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.emoji2.text.EmojiCompatInitializer"
125-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
126                android:value="androidx.startup" />
126-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
127            <meta-data
127-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
128                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
128-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
129                android:value="androidx.startup" />
129-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
130        </provider>
131    </application>
132
133</manifest>
