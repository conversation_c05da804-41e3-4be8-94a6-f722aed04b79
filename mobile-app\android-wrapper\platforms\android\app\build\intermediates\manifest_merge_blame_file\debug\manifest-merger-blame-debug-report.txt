1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.floriconstructionltd.admin"
4    android:hardwareAccelerated="true"
5    android:versionCode="10000"
6    android:versionName="1.0.0" >
7
8    <uses-sdk
9        android:minSdkVersion="24"
9-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
10        android:targetSdkVersion="34" />
10-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml
11
12    <supports-screens
12-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:5-191
13        android:anyDensity="true"
13-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:23-48
14        android:largeScreens="true"
14-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:49-76
15        android:normalScreens="true"
15-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:77-105
16        android:resizeable="true"
16-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:106-131
17        android:smallScreens="true"
17-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:132-159
18        android:xlargeScreens="true" />
18-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:3:160-188
19
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:18:5-81
20-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:18:22-78
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:19:5-71
21-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:19:22-68
22    <uses-permission android:name="android.permission.RECORD_VIDEO" />
22-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:20:5-71
22-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:20:22-68
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:21:5-80
23-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:21:22-77
24    <uses-permission android:name="android.permission.VIBRATE" />
24-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:22:5-66
24-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:22:22-63
25
26    <queries>
26-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:23:5-37:15
27        <intent>
27-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:24:9-26:18
28            <action android:name="android.media.action.IMAGE_CAPTURE" />
28-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:25:13-73
28-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:25:21-70
29        </intent>
30        <intent>
30-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:27:9-29:18
31            <action android:name="android.intent.action.GET_CONTENT" />
31-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:28:13-72
31-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:28:21-69
32        </intent>
33        <intent>
33-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:30:9-32:18
34            <action android:name="android.intent.action.PICK" />
34-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:31:13-65
34-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:31:21-62
35        </intent>
36        <intent>
36-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:33:9-36:18
37            <action android:name="com.android.camera.action.CROP" />
37-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:34:13-69
37-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:34:21-66
38
39            <data
39-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:35:13-73
40                android:mimeType="image/*"
40-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:35:19-45
41                android:scheme="content" />
41-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:35:46-70
42        </intent>
43    </queries>
44
45    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
45-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:38:5-79
45-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:38:22-76
46    <uses-permission android:name="android.permission.INTERNET" />
46-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:23:5-67
46-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:23:22-64
47    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
47-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:24:5-68
47-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:24:22-65
48    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
48-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:26:5-82
48-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:26:22-79
49
50    <permission
50-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:28:5-30:47
51        android:name="com.floriconstructionltd.admin.permission.C2D_MESSAGE"
51-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:29:9-63
52        android:protectionLevel="signature" />
52-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:30:9-44
53
54    <uses-permission android:name="com.floriconstructionltd.admin.permission.C2D_MESSAGE" />
54-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:32:5-79
54-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:32:22-76
55
56    <permission
56-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
57        android:name="com.floriconstructionltd.admin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
57-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
58        android:protectionLevel="signature" />
58-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
59
60    <uses-permission android:name="com.floriconstructionltd.admin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
60-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
60-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
61
62    <application
62-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:5-17:19
63        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
63-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\00fa6d42e7a8abf6c20b774bd480ebc9\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
64        android:debuggable="true"
65        android:extractNativeLibs="false"
66        android:hardwareAccelerated="true"
66-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:18-52
67        android:icon="@mipmap/ic_launcher"
67-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:53-87
68        android:label="@string/app_name"
68-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:88-120
69        android:networkSecurityConfig="@xml/network_security_config"
69-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:121-181
70        android:supportsRtl="true"
70-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:182-208
71        android:usesCleartextTraffic="true" >
71-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:4:209-244
72        <activity
72-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:9-10:20
73            android:name="com.floriconstructionltd.admin.MainActivity"
73-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:229-256
74            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode"
74-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:19-135
75            android:exported="true"
75-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:136-159
76            android:label="@string/activity_name"
76-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:160-197
77            android:launchMode="singleTop"
77-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:198-228
78            android:theme="@style/Theme.App.SplashScreen"
78-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:257-302
79            android:windowSoftInputMode="adjustResize" >
79-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:5:303-345
80            <intent-filter android:label="@string/launcher_name" >
80-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:6:13-9:29
80-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:6:28-65
81                <action android:name="android.intent.action.MAIN" />
81-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:7:17-69
81-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:7:25-66
82
83                <category android:name="android.intent.category.LAUNCHER" />
83-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:8:17-77
83-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:8:27-74
84            </intent-filter>
85        </activity>
86
87        <provider
88            android:name="org.apache.cordova.camera.FileProvider"
88-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:11:149-202
89            android:authorities="com.floriconstructionltd.admin.cordova.plugin.camera.provider"
89-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:11:19-88
90            android:exported="false"
90-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:11:89-113
91            android:grantUriPermissions="true" >
91-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:11:114-148
92            <meta-data
92-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:12:13-123
93                android:name="android.support.FILE_PROVIDER_PATHS"
93-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:12:24-74
94                android:resource="@xml/camera_provider_paths" />
94-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:12:75-120
95        </provider>
96        <provider
97            android:name="io.github.pwlin.cordova.plugins.fileopener2.FileProvider"
97-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:14:139-210
98            android:authorities="com.floriconstructionltd.admin.fileOpener2.provider"
98-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:14:19-78
99            android:exported="false"
99-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:14:79-103
100            android:grantUriPermissions="true" >
100-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:14:104-138
101            <meta-data
101-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:12:13-123
102                android:name="android.support.FILE_PROVIDER_PATHS"
102-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:12:24-74
103                android:resource="@xml/opener_paths" />
103-->C:\xampp\htdocs\erdevwe\mobile-app\android-wrapper\platforms\android\app\src\main\AndroidManifest.xml:12:75-120
104        </provider>
105        <!--
106             FirebaseMessagingService performs security checks at runtime,
107             no need for explicit permissions despite exported="true"
108        -->
109        <service
109-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:28:9-34:19
110            android:name="com.google.firebase.messaging.FirebaseMessagingService"
110-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:29:13-82
111            android:exported="true" >
111-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:30:13-36
112            <intent-filter android:priority="-500" >
112-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:31:13-33:29
112-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:31:28-51
113                <action android:name="com.google.firebase.MESSAGING_EVENT" />
113-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:32:17-78
113-->[com.google.firebase:firebase-messaging:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56a603f4ce392bc972e3af0135819ac2\transformed\jetified-firebase-messaging-17.0.0\AndroidManifest.xml:32:25-75
114            </intent-filter>
115        </service>
116        <service android:name="com.google.firebase.components.ComponentDiscoveryService" >
116-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:35:9-39:19
116-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:35:18-89
117            <meta-data
117-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:36:13-38:85
118                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
118-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:37:17-96
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:38:17-82
120        </service>
121
122        <receiver
122-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:41:9-50:20
123            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
123-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:42:13-78
124            android:exported="true"
124-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:43:13-36
125            android:permission="com.google.android.c2dm.permission.SEND" >
125-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:44:13-73
126            <intent-filter>
126-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:45:13-49:29
127                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
127-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:46:17-81
127-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:46:25-78
128
129                <category android:name="com.floriconstructionltd.admin" />
129-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:48:17-61
129-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:48:27-58
130            </intent-filter>
131        </receiver>
132        <!--
133             FirebaseInstanceIdService performs security checks at runtime,
134             no need for explicit permissions despite exported="true"
135        -->
136        <service
136-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:56:9-62:19
137            android:name="com.google.firebase.iid.FirebaseInstanceIdService"
137-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:57:13-77
138            android:exported="true" >
138-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:58:13-36
139            <intent-filter android:priority="-500" >
139-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:59:13-61:29
139-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:59:28-51
140                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
140-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:60:17-80
140-->[com.google.firebase:firebase-iid:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd4dd8b9d708fc9d710f00e703b68d9a\transformed\jetified-firebase-iid-16.0.0\AndroidManifest.xml:60:25-77
141            </intent-filter>
142        </service>
143
144        <provider
144-->[com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:9:9-13:39
145            android:name="com.google.firebase.provider.FirebaseInitProvider"
145-->[com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:10:13-77
146            android:authorities="com.floriconstructionltd.admin.firebaseinitprovider"
146-->[com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:11:13-72
147            android:exported="false"
147-->[com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:12:13-37
148            android:initOrder="100" />
148-->[com.google.firebase:firebase-common:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba42a7281f7261258c7ad85b39131a9\transformed\jetified-firebase-common-16.0.0\AndroidManifest.xml:13:13-36
149
150        <activity
150-->[com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:23:9-26:75
151            android:name="com.google.android.gms.common.api.GoogleApiActivity"
151-->[com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:24:13-79
152            android:exported="false"
152-->[com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:25:13-37
153            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
153-->[com.google.android.gms:play-services-base:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d86008410acda0874d72ffb71d6307a\transformed\jetified-play-services-base-15.0.1\AndroidManifest.xml:26:13-72
154
155        <meta-data
155-->[com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:23:9-25:69
156            android:name="com.google.android.gms.version"
156-->[com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:24:13-58
157            android:value="@integer/google_play_services_version" />
157-->[com.google.android.gms:play-services-basement:15.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c284a716caeffc9a2cb1c843b92804\transformed\jetified-play-services-basement-15.0.1\AndroidManifest.xml:25:13-66
158
159        <provider
159-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
160            android:name="androidx.startup.InitializationProvider"
160-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
161            android:authorities="com.floriconstructionltd.admin.androidx-startup"
161-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
162            android:exported="false" >
162-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
163            <meta-data
163-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
164                android:name="androidx.emoji2.text.EmojiCompatInitializer"
164-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
165                android:value="androidx.startup" />
165-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdc55f959bbee2d273a043c0d1ba2a7\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
166            <meta-data
166-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
167                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
167-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
168                android:value="androidx.startup" />
168-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\6716cec235b31a478226e96836c42c67\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
169        </provider>
170    </application>
171
172</manifest>
