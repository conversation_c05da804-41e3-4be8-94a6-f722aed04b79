<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

<plugin xmlns="http://apache.org/cordova/ns/plugins/1.0"
    xmlns:android="http://schemas.android.com/apk/res/android"
    id="cordova-plugin-camera"
    version="4.1.0">
    <name>Camera</name>
    <description>Cordova Camera Plugin</description>
    <license>Apache 2.0</license>
    <keywords>cordova,camera</keywords>
    <repo>https://github.com/apache/cordova-plugin-camera</repo>
    <issue>https://github.com/apache/cordova-plugin-camera/issues</issue>

    <engines>
        <engine name="cordova" version=">=7.1.0"/>
        <engine name="cordova-android" version=">=6.3.0" />
    </engines>

    <js-module src="www/CameraConstants.js" name="Camera">
        <clobbers target="Camera" />
    </js-module>

    <js-module src="www/CameraPopoverOptions.js" name="CameraPopoverOptions">
        <clobbers target="CameraPopoverOptions" />
    </js-module>

    <js-module src="www/Camera.js" name="camera">
        <clobbers target="navigator.camera" />
    </js-module>

    <!-- android -->
    <platform name="android">
        <config-file target="res/xml/config.xml" parent="/*">
            <feature name="Camera">
                <param name="android-package" value="org.apache.cordova.camera.CameraLauncher"/>
            </feature>
        </config-file>
        <config-file target="AndroidManifest.xml" parent="/*">
            <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
        </config-file>
        <config-file target="AndroidManifest.xml" parent="application">
          <provider
              android:name="org.apache.cordova.camera.FileProvider"
              android:authorities="${applicationId}.provider"
              android:exported="false"
              android:grantUriPermissions="true" >
              <meta-data
                  android:name="android.support.FILE_PROVIDER_PATHS"
                  android:resource="@xml/camera_provider_paths"/>
          </provider>
        </config-file>

        <source-file src="src/android/CameraLauncher.java" target-dir="src/org/apache/cordova/camera" />
        <source-file src="src/android/CordovaUri.java" target-dir="src/org/apache/cordova/camera" />
        <source-file src="src/android/FileHelper.java" target-dir="src/org/apache/cordova/camera" />
        <source-file src="src/android/ExifHelper.java" target-dir="src/org/apache/cordova/camera" />
        <source-file src="src/android/FileProvider.java" target-dir="src/org/apache/cordova/camera" />
        <source-file src="src/android/xml/camera_provider_paths.xml" target-dir="res/xml" />

        <js-module src="www/CameraPopoverHandle.js" name="CameraPopoverHandle">
            <clobbers target="CameraPopoverHandle" />
          </js-module>

        <preference name="ANDROID_SUPPORT_V4_VERSION" default="27.+"/>
        <framework src="com.android.support:support-v4:$ANDROID_SUPPORT_V4_VERSION"/>

      </platform>

     <!-- ios -->
     <platform name="ios">
         <config-file target="config.xml" parent="/*">
             <feature name="Camera">
                 <param name="ios-package" value="CDVCamera" />
             </feature>
             <preference name="CameraUsesGeolocation" value="false" />
         </config-file>

         <js-module src="www/ios/CameraPopoverHandle.js" name="CameraPopoverHandle">
            <clobbers target="CameraPopoverHandle" />
         </js-module>

         <header-file src="src/ios/UIImage+CropScaleOrientation.h" />
         <source-file src="src/ios/UIImage+CropScaleOrientation.m" />
         <header-file src="src/ios/CDVCamera.h" />
         <source-file src="src/ios/CDVCamera.m" />
         <header-file src="src/ios/CDVJpegHeaderWriter.h" />
         <source-file src="src/ios/CDVJpegHeaderWriter.m" />
         <header-file src="src/ios/CDVExif.h" />
         <framework src="ImageIO.framework" weak="true" />
         <framework src="CoreLocation.framework" />
         <framework src="CoreGraphics.framework" />
         <framework src="AssetsLibrary.framework" />
         <framework src="MobileCoreServices.framework" />
         <framework src="CoreGraphics.framework" />
         <framework src="AVFoundation.framework" />

     </platform>

    <!-- browser -->
    <platform name="browser">
        <config-file target="config.xml" parent="/*">
            <feature name="Camera">
                <param name="browser-package" value="Camera" />
            </feature>
        </config-file>

        <js-module src="src/browser/CameraProxy.js" name="CameraProxy">
            <runs />
        </js-module>
    </platform>

    <!-- windows -->
    <platform name="windows">
        <config-file target="package.appxmanifest" parent="/Package/Capabilities">
            <DeviceCapability Name="webcam" />
        </config-file>
        <js-module src="www/CameraPopoverHandle.js" name="CameraPopoverHandle">
            <clobbers target="CameraPopoverHandle" />
        </js-module>
        <js-module src="src/windows/CameraProxy.js" name="CameraProxy">
            <runs />
        </js-module>
    </platform>

    <!-- osx -->
    <platform name="osx">
        <config-file target="config.xml" parent="/*">
            <feature name="Camera">
                <param name="osx-package" value="CDVCamera"/>
            </feature>
        </config-file>

        <js-module src="www/CameraPopoverHandle.js" name="CameraPopoverHandle">
            <clobbers target="CameraPopoverHandle" />
        </js-module>
        
        <header-file src="src/osx/CDVCamera.h" />
        <source-file src="src/osx/CDVCamera.m" />
                
        <framework src="Quartz.framework" />
        <framework src="AppKit.framework" />
    </platform>
    
</plugin>
