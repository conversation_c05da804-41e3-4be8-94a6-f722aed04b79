---
name: 🚀 Feature Request
about: A suggestion for a new functionality

---

# Feature Request

## Motivation Behind Feature
<!-- Why should this feature be implemented? What problem does it solve? -->



## Feature Description
<!-- 
Describe your feature request in detail
Please provide any code examples or screenshots of what this feature would look like
Are there any drawbacks? Will this break anything for existing users? 
-->



## Alternatives or Workarounds
<!-- 
Describe alternatives or workarounds you are currently using 
Are there ways to do this with existing functionality?
-->


