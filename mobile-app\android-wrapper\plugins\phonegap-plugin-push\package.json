{"name": "phonegap-plugin-push", "description": "Register and receive push notifications.", "types": "./types/index.d.ts", "version": "2.3.0", "homepage": "http://github.com/phonegap/phonegap-plugin-push#readme", "repository": {"type": "git", "url": "git://github.com/phonegap/phonegap-plugin-push.git"}, "bugs": {"url": "https://github.com/phonegap/phonegap-plugin-push/issues"}, "cordova": {"id": "phonegap-plugin-push", "platforms": ["ios", "android", "windows", "browser"]}, "keywords": ["ecosystem:cordova", "ecosystem:phonegap", "cordova-ios", "cordova-android", "cordova-windows8", "cordova-windows", "cordova-wp8", "cordova-browser"], "engines": {"cordovaDependencies": {"1.8.2": {"cordova": ">=3.6.3", "cordova-android": ">=4.0.0", "cordova-ios": ">=4.1.0"}, "<2.0.0": {"cordova-ios": ">=4.3.0", "cordova-android": ">=6.0.0", "cordova": ">=6.4.0"}, "2.0.0": {"cordova-ios": ">=4.4.0", "cordova-android": ">=6.2.1", "cordova": ">=7.0.0"}, "<2.1.2": {"cordova-ios": ">=4.4.0", "cordova-android": ">=6.3.0", "cordova": ">=7.1.0"}, "<2.2.0": {"cordova-ios": ">=4.5.0", "cordova-android": ">=6.3.0", "cordova": ">=7.1.0"}, "2.2.0": {"cordova-ios": ">=4.5.0", "cordova-android": ">=7.1.0", "cordova": ">=7.1.0"}}}, "author": "Adobe PhoneGap Team", "license": "MIT", "scripts": {"build": "babel src/js --out-dir www", "build:watch": "nodemon -w ./src/js -e js -x npm run build", "eslint": "eslint src/js", "jasmine": "jasmine --config=spec/unit.json", "precommit-msg": "echo 'Pre-commit checks...' && exit 0", "test": "npm run build && npm run eslint && npm run jasmine"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "babel-eslint": "^10.0.3", "babel-plugin-add-header-comment": "^1.0.3", "eslint": "^6.2.2", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.18.2", "jasmine": "^3.4.0", "nodemon": "^1.19.1", "pluginpub": "^0.0.9"}, "dependencies": {"install": "^0.8.2"}}