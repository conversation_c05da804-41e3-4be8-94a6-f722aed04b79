<?xml version='1.0' encoding='utf-8'?>
<widget id="com.floriconstructionltd.admin" version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>Flori Construction Admin</name>
    <description>
        Native Android admin app for Flori Construction Ltd website management
    </description>
    <author email="<EMAIL>" href="https://floriconstructionltd.com">
        Flori Construction Ltd
    </author>
    <content src="index.html" />
    <preference name="DisallowOverscroll" value="true" />
    <preference name="android-minSdkVersion" value="24" />
    <preference name="android-targetSdkVersion" value="34" />
    <preference name="android-compileSdkVersion" value="34" />
    <preference name="GradlePluginKotlinEnabled" value="true" />
    <preference name="GradlePluginKotlinCodeStyle" value="official" />
    <preference name="BackupWebStorage" value="local" />
    <preference name="SplashMaintainAspectRatio" value="true" />
    <preference name="FadeSplashScreenDuration" value="300" />
    <preference name="SplashShowOnlyFirstTime" value="false" />
    <preference name="AndroidWindowSplashScreenAnimatedIcon" value="res/android/icon/drawable-xxxhdpi-icon.png" />
    <preference name="SplashScreenDelay" value="3000" />
    <preference name="AutoHideSplashScreen" value="false" />
    <preference name="ShowSplashScreenSpinner" value="false" />
    <preference name="scheme" value="https" />
    <preference name="hostname" value="floriconstructionltd.com" />
    <allow-navigation href="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <platform name="android">
        <icon density="ldpi" src="res/android/icon/drawable-ldpi-icon.png" />
        <icon density="mdpi" src="res/android/icon/drawable-mdpi-icon.png" />
        <icon density="hdpi" src="res/android/icon/drawable-hdpi-icon.png" />
        <icon density="xhdpi" src="res/android/icon/drawable-xhdpi-icon.png" />
        <icon density="xxhdpi" src="res/android/icon/drawable-xxhdpi-icon.png" />
        <icon density="xxxhdpi" src="res/android/icon/drawable-xxxhdpi-icon.png" />
        <preference name="android-build-tool" value="gradle" />
    </platform>
    <plugin name="cordova-plugin-whitelist" spec="^1.3.5" />
    <plugin name="cordova-plugin-statusbar" spec="^2.4.2" />
    <plugin name="cordova-plugin-device" spec="^2.0.3" />
    <plugin name="cordova-plugin-splashscreen" spec="^5.0.4" />
    <plugin name="cordova-plugin-network-information" spec="^2.0.2" />
    <plugin name="cordova-plugin-file" spec="^6.0.2" />
    <plugin name="cordova-sqlite-storage" spec="^5.1.0" />
    <plugin name="cordova-plugin-media-capture" spec="^3.0.3" />
    <plugin name="cordova-plugin-dialogs" spec="^2.0.2" />
    <plugin name="cordova-plugin-vibration" spec="^3.1.1" />
    <plugin name="cordova-plugin-inappbrowser" spec="^4.1.0" />
    <plugin name="cordova-plugin-app-version" spec="^0.1.12" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <meta content="default-src 'self' data: gap: https://ssl.gstatic.com 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; media-src *; img-src 'self' data: content:;" http-equiv="Content-Security-Policy" />
</widget>
