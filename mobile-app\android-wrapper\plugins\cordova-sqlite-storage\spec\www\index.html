<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="user-scalable=no" />

  <title>SQLitePlugin Jasmine Spec Runner</title>

  <link rel="shortcut icon" type="image/png" href="lib/jasmine-2.5.2/jasmine_favicon.png">
  <link rel="stylesheet" href="lib/jasmine-2.5.2/jasmine.css">

  <script src="lib/jasmine-2.5.2/jasmine.js"></script>
  <script src="lib/jasmine-2.5.2/jasmine-html.js"></script>
  <script src="lib/jasmine-2.5.2/boot.js"></script>

  <!-- [Cordova] source file(s): -->
  <script src="cordova.js"></script>

  <!-- browser startup test: -->
  <script src="spec/browser-check-startup.js"></script>

  <!-- other spec file(s): -->
  <script src="spec/self-test.js"></script>
  <script src="spec/sqlite-version-test.js"></script>
  <script src="spec/db-tx-string-test.js"></script>
  <script src="spec/db-tx-sql-select-value-test.js"></script>
  <script src="spec/basic-db-tx-sql-storage-results.js"></script>
  <script src="spec/db-sql-operations-test.js"></script>
  <script src="spec/sql-batch-test.js"></script>
  <script src="spec/db-tx-sql-features-test.js"></script>
  <script src="spec/regexp-test.js"></script>
  <script src="spec/db-simultaneous-tx-access-test.js"></script>
  <script src="spec/db-tx-multiple-update-test.js"></script>
  <script src="spec/tx-semantics-test.js"></script>
  <script src="spec/db-tx-error-handling-test.js"></script>
  <script src="spec/db-tx-value-bindings-test.js"></script>
  <script src="spec/db-tx-error-mapping-test.js"></script>
  <script src="spec/db-open-close-delete-test.js"></script>
  <script src="spec/ext-tx-blob-test.js"></script>

</head>

<body>
</body>
</html> <!-- vim: set expandtab : -->
