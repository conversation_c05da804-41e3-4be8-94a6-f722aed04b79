/*
 *
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 *
*/

.dlgWrap {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.25);
    z-index: 100000;
    top: 0;
}

.dlgContainer {
    width: 100%;
    min-height: 180px;
    height: auto;
    overflow: auto;
    background-color: white;
    position: relative;
    line-height: 2;
    top: 50%;
    transform: translateY(-50%);
    padding: 0 30%;
}

.dlgContainer #lbl-title {
    font-size: 24pt;
}

.dlgContainer #prompt-input {
    width: 100%;
}

.dlgButton {
    margin: 8px 0 0 16px;
    float: right;
    font-size: 11pt;
    background-color: #cccccc;
    border: none;
    font-weight: 600;
    font-family: "Segoe UI", Arial, sans-serif;
    padding: 0 22px;
}

.dlgButton.dlgButtonFirst {
    color: white;
    background-color: #464646;
}

.dlgContainer.dlgContainer-windows {
    width: 50%;
    max-width: 680px;
    padding: 0 5%;
    top: 50%;
    left: 50%;
    position: fixed;
    transform: translate(-50%, -50%);
    border: 1px solid rgb(24, 160, 191);
    border-image: none;
    box-shadow: 0 0 14px 6px rgba(0,0,0,0.16);
    text-transform: none;
}

.dlgContainer.dlgContainer-phone {
    padding: 0 5%;
}
