/**
 * Main JavaScript for Flori Construction Ltd Website
 * Handles mobile menu, smooth scrolling, and interactive features
 */

document.addEventListener('DOMContentLoaded', function () {

    // Mobile Menu Toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function () {
            this.classList.toggle('active');
            navMenu.classList.toggle('active');
            document.body.classList.toggle('menu-open');
        });

        // Close menu when clicking on a link
        const navLinks = navMenu.querySelectorAll('a');
        navLinks.forEach(link => {
            link.addEventListener('click', function () {
                mobileMenuToggle.classList.remove('active');
                navMenu.classList.remove('active');
                document.body.classList.remove('menu-open');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function (e) {
            if (!mobileMenuToggle.contains(e.target) && !navMenu.contains(e.target)) {
                mobileMenuToggle.classList.remove('active');
                navMenu.classList.remove('active');
                document.body.classList.remove('menu-open');
            }
        });
    }

    // Enhanced Smooth Scrolling System
    window.initSmoothScrolling = function () {
        const header = document.querySelector('.modern-header') || document.querySelector('.header');
        const headerHeight = header ? header.offsetHeight : 80;

        // Enhanced smooth scroll function with easing
        window.smoothScrollTo = function (target, duration = 1000, offset = 0) {
            const targetElement = typeof target === 'string' ? document.querySelector(target) : target;
            if (!targetElement) return;

            const startPosition = window.pageYOffset;
            const targetPosition = targetElement.offsetTop - headerHeight - offset;
            const distance = targetPosition - startPosition;
            let startTime = null;

            // Easing function for smooth animation
            function easeInOutCubic(t) {
                return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
            }

            function animation(currentTime) {
                if (startTime === null) startTime = currentTime;
                const timeElapsed = currentTime - startTime;
                const progress = Math.min(timeElapsed / duration, 1);
                const ease = easeInOutCubic(progress);

                window.scrollTo(0, startPosition + distance * ease);

                if (timeElapsed < duration) {
                    requestAnimationFrame(animation);
                }
            }

            requestAnimationFrame(animation);
        };

        // Enhanced anchor link handling
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach(link => {
            link.addEventListener('click', function (e) {
                const href = this.getAttribute('href');
                if (href === '#' || href === '#top') {
                    e.preventDefault();
                    window.smoothScrollTo(document.body, 800);
                    return;
                }

                const target = document.querySelector(href);
                if (target) {
                    e.preventDefault();
                    window.smoothScrollTo(target, 1000);

                    // Add visual feedback
                    this.classList.add('scrolling');
                    setTimeout(() => this.classList.remove('scrolling'), 1000);
                }
            });
        });
    };

    // Initialize smooth scrolling
    window.initSmoothScrolling();

    // Header Scroll Effect - Support both header classes
    const header = document.querySelector('.modern-header') || document.querySelector('.header');
    let lastScrollTop = 0;

    if (header) {
        window.addEventListener('scroll', function () {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }

            // Hide/show header on scroll
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }

            lastScrollTop = scrollTop;
        });
    }

    // Hero Slider (if multiple slides exist)
    const heroSlides = document.querySelectorAll('.hero-slide');
    if (heroSlides.length > 1) {
        let currentSlide = 0;

        function showSlide(index) {
            heroSlides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % heroSlides.length;
            showSlide(currentSlide);
        }

        // Auto-advance slides every 5 seconds
        setInterval(nextSlide, 5000);
    }

    // Lazy Loading for Images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));

    // Enhanced Navigation System with Intersection Observer
    window.initEnhancedNavigation = function () {
        const navDots = document.querySelectorAll('.nav-dot');
        const sections = document.querySelectorAll('section[id]');
        const headerNavLinks = document.querySelectorAll('.nav-link');

        // Enhanced intersection observer for active section detection
        const sectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const sectionId = entry.target.id;

                    // Update navigation dots
                    navDots.forEach(dot => {
                        dot.classList.remove('active');
                        if (dot.dataset.section === sectionId) {
                            dot.classList.add('active');
                            // Add pulse effect for active dot
                            dot.style.animation = 'pulse 0.6s ease-in-out';
                            setTimeout(() => dot.style.animation = '', 600);
                        }
                    });

                    // Update header navigation for home page sections
                    if (window.location.pathname.includes('index.php') || window.location.pathname === '/') {
                        headerNavLinks.forEach(link => {
                            link.classList.remove('active');
                            if (sectionId === 'modern-hero' && link.getAttribute('href') === 'index.php') {
                                link.classList.add('active');
                            }
                        });
                    }
                }
            });
        }, {
            threshold: 0.3,
            rootMargin: '-20% 0px -20% 0px'
        });

        // Observe all sections
        sections.forEach(section => sectionObserver.observe(section));

        // Enhanced navigation dot clicks
        navDots.forEach(dot => {
            dot.addEventListener('click', function () {
                const targetSection = document.getElementById(this.dataset.section);
                if (targetSection) {
                    window.smoothScrollTo(targetSection, 1200);

                    // Add click feedback
                    this.style.transform = 'scale(1.3)';
                    setTimeout(() => this.style.transform = '', 200);
                }
            });

            // Add hover tooltips
            dot.addEventListener('mouseenter', function () {
                const tooltip = document.createElement('div');
                tooltip.className = 'nav-dot-tooltip';
                tooltip.textContent = this.getAttribute('title');
                tooltip.style.cssText = `
                    position: absolute;
                    right: 25px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 6px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    white-space: nowrap;
                    pointer-events: none;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                `;
                this.appendChild(tooltip);
                setTimeout(() => tooltip.style.opacity = '1', 10);
            });

            dot.addEventListener('mouseleave', function () {
                const tooltip = this.querySelector('.nav-dot-tooltip');
                if (tooltip) {
                    tooltip.style.opacity = '0';
                    setTimeout(() => tooltip.remove(), 300);
                }
            });
        });
    };

    // Initialize enhanced navigation
    window.initEnhancedNavigation();

    // Enhanced Section Reveal Animations
    window.initSectionRevealAnimations = function () {
        // Define animation types and their configurations
        const animationTypes = {
            'fade-up': {
                initial: { opacity: 0, transform: 'translateY(60px)' },
                animate: { opacity: 1, transform: 'translateY(0)' },
                duration: '0.8s',
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            },
            'fade-down': {
                initial: { opacity: 0, transform: 'translateY(-60px)' },
                animate: { opacity: 1, transform: 'translateY(0)' },
                duration: '0.8s',
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            },
            'fade-left': {
                initial: { opacity: 0, transform: 'translateX(-60px)' },
                animate: { opacity: 1, transform: 'translateX(0)' },
                duration: '0.8s',
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            },
            'fade-right': {
                initial: { opacity: 0, transform: 'translateX(60px)' },
                animate: { opacity: 1, transform: 'translateX(0)' },
                duration: '0.8s',
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            },
            'scale-up': {
                initial: { opacity: 0, transform: 'scale(0.8)' },
                animate: { opacity: 1, transform: 'scale(1)' },
                duration: '0.6s',
                easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)'
            },
            'rotate-in': {
                initial: { opacity: 0, transform: 'rotate(-10deg) scale(0.9)' },
                animate: { opacity: 1, transform: 'rotate(0deg) scale(1)' },
                duration: '0.8s',
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            }
        };

        // Enhanced intersection observer for section reveals
        const revealObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const animationType = element.dataset.animation || 'fade-up';
                    const delay = parseInt(element.dataset.delay) || 0;
                    const stagger = parseInt(element.dataset.stagger) || 0;

                    // Apply animation with delay
                    setTimeout(() => {
                        element.classList.add('animate-revealed');

                        // Handle staggered children animations
                        if (stagger > 0) {
                            const children = element.querySelectorAll('.stagger-child');
                            children.forEach((child, index) => {
                                setTimeout(() => {
                                    child.classList.add('animate-revealed');
                                }, index * stagger);
                            });
                        }

                        // Add completion callback
                        element.addEventListener('transitionend', function onComplete() {
                            element.classList.add('animation-complete');
                            element.removeEventListener('transitionend', onComplete);
                        });

                    }, delay);

                    // Unobserve after animation to improve performance
                    revealObserver.unobserve(element);
                }
            });
        }, {
            threshold: 0.15,
            rootMargin: '0px 0px -10% 0px'
        });

        // Auto-detect and setup reveal elements
        const setupRevealElements = () => {
            // Section headers
            document.querySelectorAll('.section-header').forEach(header => {
                if (!header.classList.contains('reveal-setup')) {
                    header.classList.add('reveal-element', 'reveal-setup');
                    header.dataset.animation = 'fade-up';
                    header.dataset.delay = '100';
                }
            });

            // Feature items with stagger
            document.querySelectorAll('.feature-item, .service-card, .project-card, .testimonial-card').forEach((item, index) => {
                if (!item.classList.contains('reveal-setup')) {
                    item.classList.add('reveal-element', 'reveal-setup');
                    item.dataset.animation = 'fade-up';
                    item.dataset.delay = (index * 100).toString();
                }
            });

            // About visual elements
            document.querySelectorAll('.about-visual, .floating-card').forEach(visual => {
                if (!visual.classList.contains('reveal-setup')) {
                    visual.classList.add('reveal-element', 'reveal-setup');
                    visual.dataset.animation = 'fade-left';
                    visual.dataset.delay = '200';
                }
            });

            // Statistics and metrics
            document.querySelectorAll('.stat-item, .summary-card').forEach(stat => {
                if (!stat.classList.contains('reveal-setup')) {
                    stat.classList.add('reveal-element', 'reveal-setup');
                    stat.dataset.animation = 'scale-up';
                    stat.dataset.delay = '300';
                }
            });

            // Counter elements
            document.querySelectorAll('.counter-element').forEach(counter => {
                if (!counter.classList.contains('counter-setup')) {
                    counter.classList.add('counter-setup');
                    const target = parseInt(counter.dataset.target) || parseInt(counter.textContent);
                    const suffix = counter.dataset.suffix || '';

                    // Setup intersection observer for counter animation
                    const counterObserver = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting && !entry.target.classList.contains('counter-animated')) {
                                entry.target.classList.add('counter-animated');
                                window.animateCounter(entry.target, target, 2000, suffix);
                                counterObserver.unobserve(entry.target);
                            }
                        });
                    }, { threshold: 0.5 });

                    counterObserver.observe(counter);
                }
            });
        };

        // Setup reveal elements
        setupRevealElements();

        // Observe all reveal elements
        document.querySelectorAll('.reveal-element').forEach(el => {
            revealObserver.observe(el);
        });

        // Re-setup on dynamic content changes
        const mutationObserver = new MutationObserver(() => {
            setupRevealElements();
            document.querySelectorAll('.reveal-element:not(.reveal-observed)').forEach(el => {
                el.classList.add('reveal-observed');
                revealObserver.observe(el);
            });
        });

        mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    };

    // Initialize section reveal animations
    window.initSectionRevealAnimations();

    // Legacy animate on scroll for backward compatibility
    const animateElements = document.querySelectorAll('.animate-on-scroll');
    const animateObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    animateElements.forEach(el => animateObserver.observe(el));

    // Contact Form Handling
    const contactForm = document.querySelector('#contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function (e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');

            if (!submitBtn) {
                console.error('Submit button not found in contact form');
                return;
            }

            const originalText = submitBtn.textContent;

            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;

            fetch('api/contact.php', {
                method: 'POST',
                body: formData
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showNotification('Message sent successfully!', 'success');
                        contactForm.reset();
                    } else {
                        showNotification(data.message || 'Error sending message', 'error');
                    }
                })
                .catch(error => {
                    console.error('Contact form error:', error);
                    showNotification('Error sending message. Please try again.', 'error');
                })
                .finally(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
        });
    }

    // Notification System
    window.showNotification = function (message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Trigger animation
        setTimeout(() => notification.classList.add('show'), 100);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    };

    // Gallery Lightbox
    const galleryImages = document.querySelectorAll('.gallery-image');
    galleryImages.forEach(img => {
        img.addEventListener('click', function () {
            openLightbox(this.src, this.alt);
        });
    });

    function openLightbox(src, alt) {
        const lightbox = document.createElement('div');
        lightbox.className = 'lightbox';
        lightbox.innerHTML = `
            <div class="lightbox-content">
                <img src="${src}" alt="${alt}">
                <button class="lightbox-close">&times;</button>
            </div>
        `;

        document.body.appendChild(lightbox);
        document.body.style.overflow = 'hidden';

        // Close lightbox
        const closeBtn = lightbox.querySelector('.lightbox-close');
        closeBtn.addEventListener('click', closeLightbox);
        lightbox.addEventListener('click', function (e) {
            if (e.target === this) closeLightbox();
        });

        function closeLightbox() {
            lightbox.remove();
            document.body.style.overflow = '';
        }

        // Keyboard navigation
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape') closeLightbox();
        });
    }

    // Search Functionality
    const searchInput = document.querySelector('#search-input');
    if (searchInput) {
        let searchTimeout;

        searchInput.addEventListener('input', function () {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length >= 3) {
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            }
        });
    }

    function performSearch(query) {
        fetch(`api/search.php?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data.results);
            })
            .catch(error => {
                console.error('Search error:', error);
            });
    }

    function displaySearchResults(results) {
        const resultsContainer = document.querySelector('#search-results');
        if (!resultsContainer) return;

        if (results.length === 0) {
            resultsContainer.innerHTML = '<p>No results found.</p>';
            return;
        }

        const html = results.map(result => `
            <div class="search-result">
                <h3><a href="${result.url}">${result.title}</a></h3>
                <p>${result.excerpt}</p>
            </div>
        `).join('');

        resultsContainer.innerHTML = html;
    }



    // Performance: Preload critical resources
    function preloadCriticalResources() {
        const criticalImages = [
            'assets/images/hero-bg-1.jpg',
            'assets/images/logo.png'
        ];

        criticalImages.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = src;
            document.head.appendChild(link);
        });
    }

    preloadCriticalResources();

    // Back to Top Button and Scroll Progress
    window.initScrollEnhancements = function () {
        // Create back to top button
        const backToTopBtn = document.createElement('button');
        backToTopBtn.className = 'back-to-top-btn';
        backToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
        backToTopBtn.setAttribute('aria-label', 'Back to top');
        backToTopBtn.style.cssText = `
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #d4a574, #c19660);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(212, 165, 116, 0.3);
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: all 0.3s ease;
            z-index: 1000;
        `;
        document.body.appendChild(backToTopBtn);

        // Create scroll progress indicator
        const scrollProgress = document.createElement('div');
        scrollProgress.className = 'scroll-progress';
        scrollProgress.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #d4a574, #c19660);
            z-index: 1001;
            transition: width 0.1s ease;
        `;
        document.body.appendChild(scrollProgress);

        // Scroll event handler
        let ticking = false;
        function updateScrollElements() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;

            // Update progress bar
            scrollProgress.style.width = scrollPercent + '%';

            // Show/hide back to top button
            if (scrollTop > 300) {
                backToTopBtn.style.opacity = '1';
                backToTopBtn.style.visibility = 'visible';
                backToTopBtn.style.transform = 'translateY(0)';
            } else {
                backToTopBtn.style.opacity = '0';
                backToTopBtn.style.visibility = 'hidden';
                backToTopBtn.style.transform = 'translateY(20px)';
            }

            ticking = false;
        }

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollElements);
                ticking = true;
            }
        });

        // Back to top button click
        backToTopBtn.addEventListener('click', () => {
            window.smoothScrollTo(document.body, 800);
            backToTopBtn.style.transform = 'scale(0.9)';
            setTimeout(() => backToTopBtn.style.transform = 'translateY(0)', 150);
        });

        // Add hover effects
        backToTopBtn.addEventListener('mouseenter', () => {
            backToTopBtn.style.transform = 'translateY(-3px) scale(1.05)';
            backToTopBtn.style.boxShadow = '0 6px 20px rgba(212, 165, 116, 0.4)';
        });

        backToTopBtn.addEventListener('mouseleave', () => {
            backToTopBtn.style.transform = 'translateY(0) scale(1)';
            backToTopBtn.style.boxShadow = '0 4px 15px rgba(212, 165, 116, 0.3)';
        });
    };

    // Initialize scroll enhancements
    window.initScrollEnhancements();

    // Advanced Animation Effects
    window.initAdvancedAnimations = function () {
        // Animated Counter Function
        window.animateCounter = function (element, target, duration = 2000, suffix = '') {
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                // Format number based on value
                let displayValue;
                if (target >= 1000) {
                    displayValue = Math.floor(current).toLocaleString();
                } else if (target % 1 !== 0) {
                    displayValue = current.toFixed(1);
                } else {
                    displayValue = Math.floor(current);
                }

                element.textContent = displayValue + suffix;
            }, 16);
        };

        // Typewriter Effect
        window.typewriterEffect = function (element, text, speed = 50) {
            element.textContent = '';
            let i = 0;

            const timer = setInterval(() => {
                if (i < text.length) {
                    element.textContent += text.charAt(i);
                    i++;
                } else {
                    clearInterval(timer);
                    element.classList.add('typewriter-complete');
                }
            }, speed);
        };

        // Parallax Elements
        window.initParallaxElements = function () {
            const parallaxElements = document.querySelectorAll('.parallax-element');

            if (parallaxElements.length === 0) return;

            let ticking = false;

            function updateParallax() {
                const scrolled = window.pageYOffset;

                parallaxElements.forEach(element => {
                    const speed = parseFloat(element.dataset.speed) || 0.5;
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });

                ticking = false;
            }

            window.addEventListener('scroll', () => {
                if (!ticking) {
                    requestAnimationFrame(updateParallax);
                    ticking = true;
                }
            });
        };

        // Floating Animation
        window.initFloatingAnimations = function () {
            const floatingElements = document.querySelectorAll('.floating-element');

            floatingElements.forEach((element, index) => {
                const duration = 3000 + (index * 500); // Stagger durations
                const delay = index * 200; // Stagger start times

                element.style.animation = `floating ${duration}ms ease-in-out ${delay}ms infinite`;
            });
        };

        // Magnetic Effect for Interactive Elements
        window.initMagneticEffect = function () {
            const magneticElements = document.querySelectorAll('.magnetic-element');

            magneticElements.forEach(element => {
                element.addEventListener('mousemove', (e) => {
                    const rect = element.getBoundingClientRect();
                    const x = e.clientX - rect.left - rect.width / 2;
                    const y = e.clientY - rect.top - rect.height / 2;

                    const strength = 0.3;
                    element.style.transform = `translate(${x * strength}px, ${y * strength}px)`;
                });

                element.addEventListener('mouseleave', () => {
                    element.style.transform = 'translate(0, 0)';
                });
            });
        };

        // Initialize all advanced animations
        window.initParallaxElements();
        window.initFloatingAnimations();
        window.initMagneticEffect();
    };

    // Initialize advanced animations
    window.initAdvancedAnimations();

    // Service Worker Registration for PWA
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function () {
            navigator.serviceWorker.register('/sw.js')
                .then(function (registration) {
                    console.log('ServiceWorker registration successful');
                })
                .catch(function (err) {
                    console.log('ServiceWorker registration failed');
                });
        });
    }
});

// Utility Functions
window.debounce = function (func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

window.throttle = function (func, limit) {
    let inThrottle;
    return function () {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};
