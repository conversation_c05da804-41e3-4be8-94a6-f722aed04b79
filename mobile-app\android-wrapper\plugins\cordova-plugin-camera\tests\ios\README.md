<!---
 license: Licensed to the Apache Software Foundation (ASF) under one
         or more contributor license agreements.  See the NOTICE file
         distributed with this work for additional information
         regarding copyright ownership.  The ASF licenses this file
         to you under the Apache License, Version 2.0 (the
         "License"); you may not use this file except in compliance
         with the License.  You may obtain a copy of the License at

           http://www.apache.org/licenses/LICENSE-2.0

         Unless required by applicable law or agreed to in writing,
         software distributed under the License is distributed on an
         "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
         KIND, either express or implied.  See the License for the
         specific language governing permissions and limitations
         under the License.
-->

# iOS Tests for CDVCamera

You need to install `node.js` to pull in `cordova-ios`.

First install cordova-ios:

    npm install

... in the current folder.


# Testing from Xcode

1. Launch the `CDVCameraTest.xcworkspace` file.
2. Choose "CDVCameraLibTests" from the scheme drop-down menu
3. Click and hold on the `Play` button, and choose the `Wrench` icon to run the tests


# Testing from the command line

    npm test
