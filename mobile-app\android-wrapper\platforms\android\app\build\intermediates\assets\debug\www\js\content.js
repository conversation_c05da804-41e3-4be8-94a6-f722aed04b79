/**
 * Content Manager for Flori Construction Mobile App
 * Handles content management, editing, and site content updates
 */

class ContentManager {
    constructor() {
        this.apiBase = '../api';
        this.content = {};
        this.siteContent = {};
        this.siteSettings = {};

        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Content form submissions will be handled dynamically
    }

    async load() {
        try {
            const response = await window.floriAdmin.apiRequest('mobile.php?action=content');

            if (response && response.success) {
                this.content = response.data.content || {};
                this.siteContent = response.data.site_content || {};
                this.siteSettings = response.data.site_settings || {};
                this.renderContentSections();
            } else {
                throw new Error(response?.error || 'Failed to load content');
            }

        } catch (error) {
            console.error('Failed to load content:', error);
            window.floriAdmin.showToast('Failed to load content', 'error');
        }
    }

    renderContentSections() {
        const container = document.getElementById('content-sections');
        if (!container) return;

        const sections = [
            {
                title: 'Homepage Content',
                items: [
                    { key: 'hero_title', label: 'Hero Title', type: 'text' },
                    { key: 'hero_subtitle', label: 'Hero Subtitle', type: 'text' },
                    { key: 'about_section', label: 'About Section', type: 'textarea' },
                    { key: 'services_intro', label: 'Services Introduction', type: 'textarea' }
                ]
            },
            {
                title: 'About Page',
                items: [
                    { key: 'about_title', label: 'About Title', type: 'text' },
                    { key: 'about_content', label: 'About Content', type: 'textarea' },
                    { key: 'mission_statement', label: 'Mission Statement', type: 'textarea' },
                    { key: 'vision_statement', label: 'Vision Statement', type: 'textarea' }
                ]
            },
            {
                title: 'Contact Information',
                items: [
                    { key: 'contact_phone', label: 'Phone Number', type: 'text' },
                    { key: 'contact_email', label: 'Email Address', type: 'email' },
                    { key: 'contact_address', label: 'Address', type: 'textarea' },
                    { key: 'business_hours', label: 'Business Hours', type: 'textarea' }
                ]
            },
            {
                title: 'SEO Settings',
                items: [
                    { key: 'site_title', label: 'Site Title', type: 'text' },
                    { key: 'site_description', label: 'Site Description', type: 'textarea' },
                    { key: 'site_keywords', label: 'Site Keywords', type: 'text' },
                    { key: 'google_analytics', label: 'Google Analytics ID', type: 'text' }
                ]
            }
        ];

        const html = sections.map(section => `
            <div class="content-section">
                <h3 class="section-title">${section.title}</h3>
                <div class="section-items">
                    ${section.items.map(item => this.renderContentItem(item)).join('')}
                </div>
            </div>
        `).join('');

        container.innerHTML = html;

        // Setup form submissions
        this.setupFormHandlers();
    }

    renderContentItem(item) {
        const value = this.getContentValue(item.key);
        const inputId = `content-${item.key}`;

        let inputHtml = '';

        switch (item.type) {
            case 'textarea':
                inputHtml = `<textarea id="${inputId}" name="${item.key}" rows="4">${window.floriAdmin.escapeHtml(value)}</textarea>`;
                break;
            case 'email':
                inputHtml = `<input type="email" id="${inputId}" name="${item.key}" value="${window.floriAdmin.escapeHtml(value)}">`;
                break;
            default:
                inputHtml = `<input type="text" id="${inputId}" name="${item.key}" value="${window.floriAdmin.escapeHtml(value)}">`;
        }

        return `
            <div class="content-item">
                <form class="content-form" data-key="${item.key}">
                    <div class="form-group">
                        <label for="${inputId}">${item.label}</label>
                        ${inputHtml}
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-save"></i> Save
                        </button>
                    </div>
                </form>
            </div>
        `;
    }

    getContentValue(key) {
        // Check site content first, then content, then settings
        if (this.siteContent[key]) {
            return this.siteContent[key].value || '';
        }
        if (this.content[key]) {
            return this.content[key].content || '';
        }
        if (this.siteSettings[key]) {
            return this.siteSettings[key].value || '';
        }
        return '';
    }

    setupFormHandlers() {
        const forms = document.querySelectorAll('.content-form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleContentUpdate(form);
            });
        });
    }

    async handleContentUpdate(form) {
        const key = form.dataset.key;
        const formData = new FormData(form);
        const value = formData.get(key);
        const submitBtn = form.querySelector('button[type="submit"]');

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        submitBtn.disabled = true;

        try {
            // Determine content type based on existing data
            let contentType = 'site_content'; // default

            if (this.content[key]) {
                contentType = 'content';
            } else if (this.siteSettings[key]) {
                contentType = 'site_settings';
            }

            const updateData = {
                type: contentType,
                key: key,
                value: value
            };

            // Add title for content type
            if (contentType === 'content') {
                updateData.title = this.getContentTitle(key);
            }

            const response = await window.floriAdmin.apiRequest('mobile.php?action=content', {
                method: 'PUT',
                body: JSON.stringify(updateData)
            });

            if (response && response.success) {
                window.floriAdmin.showToast('Content updated successfully!', 'success');

                // Update local data
                this.updateLocalContent(contentType, key, value);

                // Add success indicator
                submitBtn.classList.add('btn-success');
                setTimeout(() => {
                    submitBtn.classList.remove('btn-success');
                }, 2000);

            } else {
                throw new Error(response?.error || 'Failed to update content');
            }

        } catch (error) {
            console.error('Failed to update content:', error);
            window.floriAdmin.showToast('Failed to update content: ' + error.message, 'error');
        } finally {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    updateLocalContent(type, key, value) {
        switch (type) {
            case 'content':
                if (!this.content[key]) this.content[key] = {};
                this.content[key].content = value;
                break;
            case 'site_content':
                if (!this.siteContent[key]) this.siteContent[key] = {};
                this.siteContent[key].value = value;
                break;
            case 'site_settings':
                if (!this.siteSettings[key]) this.siteSettings[key] = {};
                this.siteSettings[key].value = value;
                break;
        }
    }

    getContentTitle(key) {
        const titles = {
            'hero_title': 'Hero Title',
            'hero_subtitle': 'Hero Subtitle',
            'about_section': 'About Section',
            'services_intro': 'Services Introduction',
            'about_title': 'About Title',
            'about_content': 'About Content',
            'mission_statement': 'Mission Statement',
            'vision_statement': 'Vision Statement'
        };

        return titles[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    // Bulk content operations
    async saveAllContent() {
        const forms = document.querySelectorAll('.content-form');
        const updates = [];

        forms.forEach(form => {
            const key = form.dataset.key;
            const formData = new FormData(form);
            const value = formData.get(key);

            // Determine content type
            let contentType = 'site_content';
            if (this.content[key]) {
                contentType = 'content';
            } else if (this.siteSettings[key]) {
                contentType = 'site_settings';
            }

            updates.push({
                type: contentType,
                key: key,
                value: value,
                title: contentType === 'content' ? this.getContentTitle(key) : undefined
            });
        });

        try {
            const promises = updates.map(update =>
                window.floriAdmin.apiRequest('content.php', {
                    method: 'PUT',
                    body: JSON.stringify(update)
                })
            );

            const results = await Promise.allSettled(promises);
            const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
            const failed = results.length - successful;

            if (successful > 0) {
                window.floriAdmin.showToast(`${successful} content item(s) updated successfully!`, 'success');
            }

            if (failed > 0) {
                window.floriAdmin.showToast(`${failed} content item(s) failed to update`, 'warning');
            }

        } catch (error) {
            console.error('Bulk content update failed:', error);
            window.floriAdmin.showToast('Bulk update failed: ' + error.message, 'error');
        }
    }

    // Content preview functionality
    previewContent(key) {
        const value = this.getContentValue(key);

        const modalContent = `
            <div class="modal-header">
                <h2>Content Preview: ${this.getContentTitle(key)}</h2>
                <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="content-preview">
                <div class="preview-content">
                    ${this.formatContentForPreview(value)}
                </div>
            </div>
        `;

        window.floriAdmin.showModal(modalContent);
    }

    formatContentForPreview(content) {
        // Basic formatting for preview
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    // Export content for backup
    exportContent() {
        const exportData = {
            content: this.content,
            site_content: this.siteContent,
            site_settings: this.siteSettings,
            exported_at: new Date().toISOString()
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `flori-content-backup-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        window.floriAdmin.showToast('Content exported successfully!', 'success');
    }

    // Search content
    searchContent(query) {
        if (!query) {
            this.renderContentSections();
            return;
        }

        const container = document.getElementById('content-sections');
        if (!container) return;

        const allItems = container.querySelectorAll('.content-item');
        let visibleCount = 0;

        allItems.forEach(item => {
            const label = item.querySelector('label').textContent.toLowerCase();
            const input = item.querySelector('input, textarea');
            const value = input ? input.value.toLowerCase() : '';

            if (label.includes(query.toLowerCase()) || value.includes(query.toLowerCase())) {
                item.style.display = 'block';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });

        // Show/hide sections based on visible items
        const sections = container.querySelectorAll('.content-section');
        sections.forEach(section => {
            const visibleItems = section.querySelectorAll('.content-item[style="display: block"], .content-item:not([style])');
            section.style.display = visibleItems.length > 0 ? 'block' : 'none';
        });

        if (visibleCount === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-search fa-3x"></i>
                    <h3>No content found</h3>
                    <p>No content matches your search query: "${query}"</p>
                </div>
            `;
        }
    }
}

// Initialize content manager
document.addEventListener('DOMContentLoaded', () => {
    window.ContentManager = new ContentManager();
});
