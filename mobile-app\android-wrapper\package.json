{"name": "flori-construction-admin-android", "displayName": "Flori Construction Admin", "version": "1.0.0", "description": "Native Android admin app for Flori Construction Ltd website management", "main": "index.js", "scripts": {"build": "<PERSON>ova build android", "build-release": "cordova build android --release", "run": "cordova run android", "run-device": "cordova run android --device", "emulate": "<PERSON>ova emulate android", "prepare": "cordova prepare android", "clean": "cordova clean android", "requirements": "cordova requirements android", "platform-add": "cordova platform add android", "platform-remove": "cordova platform remove android", "plugin-list": "cordova plugin list", "serve": "cordova serve", "copy-www": "npm run copy-pwa && npm run copy-assets", "copy-pwa": "cp -r ../index.html ../css ../js ../icons ../manifest.json www/", "copy-assets": "cp -r ../../assets www/", "setup": "npm run platform-add && npm run copy-www && npm run prepare", "dev": "npm run copy-www && npm run run", "release": "npm run copy-www && npm run build-release"}, "keywords": ["<PERSON><PERSON>", "android", "mobile", "construction", "admin", "flori"], "author": "Flori Construction Ltd", "license": "MIT", "devDependencies": {"cordova": "^11.0.0", "cordova-android": "^12.0.1"}, "cordova": {"platforms": ["android"], "plugins": {"cordova-plugin-whitelist": {}, "cordova-plugin-statusbar": {}, "cordova-plugin-device": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-network-information": {}, "cordova-plugin-file": {}, "cordova-sqlite-storage": {}, "cordova-plugin-media-capture": {}, "cordova-plugin-dialogs": {}, "cordova-plugin-vibration": {}, "cordova-plugin-inappbrowser": {}, "cordova-plugin-app-version": {}}}, "repository": {"type": "git", "url": "https://github.com/floriconstructionltd/admin-app.git"}, "bugs": {"url": "https://github.com/floriconstructionltd/admin-app/issues"}, "homepage": "https://floriconstructionltd.com", "dependencies": {"cordova-plugin-statusbar": "2.4.3"}}