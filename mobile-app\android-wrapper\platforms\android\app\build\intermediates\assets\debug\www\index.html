<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flori Construction Admin</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        .test-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 10000;
        }
    </style>
</head>

<body>
    <div class="test-screen">
        <h1 style="font-size: 48px; margin: 20px; text-align: center;">
            🎉 FLORI CONSTRUCTION
        </h1>
        <h2 style="font-size: 32px; margin: 20px; text-align: center;">
            Admin App Working!
        </h2>
        <p style="font-size: 24px; margin: 20px; text-align: center;">
            Native Android App Successfully Loaded
        </p>
        <p style="font-size: 18px; margin: 20px; text-align: center;">
            If you see this, the app is working correctly!
        </p>
    </div>

    <!-- Cordova JavaScript -->
    <script type="text/javascript" src="cordova.js"></script>
    
    <script>
        console.log('Simple test app loaded');
        
        // Hide splash screen when ready
        document.addEventListener('deviceready', function() {
            console.log('Device ready');
            if (navigator.splashscreen) {
                navigator.splashscreen.hide();
            }
        }, false);
    </script>
</body>

</html>
