<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flori Construction - Admin App</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#e74c3c">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Flori Admin">

    <!-- Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="icons/icon-192x192.png">
    <link rel="apple-touch-icon" href="icons/icon-192x192.png">

    <!-- CSS -->
    <link rel="stylesheet" href="css/app.css">
    <link rel="stylesheet" href="css/android-native.css">
    <link rel="stylesheet" href="css/mobile-navigation.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>Loading Flori Admin...</p>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="screen" style="display: none;">
        <div class="login-container">
            <div class="logo">
                <img src="../assets/images/logo.png" alt="Flori Construction"
                    onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display: none; text-align: center; font-size: 24px; font-weight: bold; color: #e74c3c;">
                    FLORI CONSTRUCTION
                </div>
            </div>
            <h1>Admin Login</h1>

            <form id="login-form" class="login-form">
                <div class="form-group">
                    <label for="username">Username or Email</label>
                    <input type="text" id="username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
            </form>

            <div id="login-error" class="error-message" style="display: none;"></div>
        </div>
    </div>

    <!-- Main App -->
    <div id="main-app" class="screen" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <div class="app-logo">
                    <img src="../assets/images/logo.png" alt="Flori Construction"
                        onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <i class="fas fa-hammer" style="display: none; color: #e74c3c; font-size: 24px;"></i>
                </div>
                <h1 id="page-title">Dashboard</h1>
            </div>
            <div class="header-right">
                <div id="network-status" class="network-status">
                    <i class="fas fa-wifi"></i>
                </div>
                <button id="logout-btn" class="btn btn-ghost">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </header>

        <!-- Bottom Navigation -->
        <nav class="bottom-navigation">
            <button class="nav-item active" data-page="dashboard">
                <i class="fas fa-tachometer-alt"></i>
                <span>Dashboard</span>
            </button>
            <button class="nav-item" data-page="projects">
                <i class="fas fa-building"></i>
                <span>Projects</span>
            </button>
            <button class="nav-item" data-page="media">
                <i class="fas fa-images"></i>
                <span>Media</span>
            </button>
            <button class="nav-item" data-page="services">
                <i class="fas fa-tools"></i>
                <span>Services</span>
            </button>
        </nav>

        <!-- Floating Action Button for Native Features -->
        <div class="fab-container">
            <button id="fab-main" class="fab">
                <i class="fas fa-plus"></i>
            </button>
            <div id="fab-menu" class="fab-menu">
                <button id="camera-btn" class="fab-option" data-tooltip="Camera">
                    <i class="fas fa-camera"></i>
                </button>
                <button id="gallery-btn" class="fab-option" data-tooltip="Gallery">
                    <i class="fas fa-images"></i>
                </button>
                <button id="device-info-btn" class="fab-option" data-tooltip="Device Info">
                    <i class="fas fa-info-circle"></i>
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page active">
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-projects">0</h3>
                            <p>Total Projects</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="completed-projects">0</h3>
                            <p>Completed</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="ongoing-projects">0</h3>
                            <p>Ongoing</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-media">0</h3>
                            <p>Media Files</p>
                        </div>
                    </div>
                </div>

                <div class="recent-activity">
                    <h2>Recent Activity</h2>
                    <div id="recent-projects" class="activity-list">
                        <p>Welcome to Flori Construction Admin App!</p>
                        <p>Native Android features are available via the floating action button (+ icon).</p>
                        <p>Use the bottom navigation to switch between sections.</p>
                    </div>
                </div>
            </div>

            <!-- Projects Page -->
            <div id="projects-page" class="page">
                <div class="page-header">
                    <h2>Projects Management</h2>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Project
                    </button>
                </div>
                <div class="projects-grid">
                    <div class="project-card">
                        <h3>Sample Project</h3>
                        <p>This is where your projects will be displayed.</p>
                        <span class="status ongoing">Ongoing</span>
                    </div>
                </div>
            </div>

            <!-- Media Page -->
            <div id="media-page" class="page">
                <div class="page-header">
                    <h2>Media Library</h2>
                    <button class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload Media
                    </button>
                </div>
                <div class="media-grid">
                    <div class="media-item">
                        <div class="media-placeholder">
                            <i class="fas fa-image"></i>
                        </div>
                        <p>Your media files will appear here.</p>
                    </div>
                </div>
            </div>

            <!-- Services Page -->
            <div id="services-page" class="page">
                <div class="page-header">
                    <h2>Services Management</h2>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Service
                    </button>
                </div>
                <div class="services-list">
                    <div class="service-item">
                        <h3>Sample Service</h3>
                        <p>This is where your services will be managed.</p>
                        <span class="status active">Active</span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Cordova JavaScript -->
    <script type="text/javascript" src="cordova.js"></script>

    <!-- App JavaScript -->
    <script src="js/toast-system.js"></script>
    <script src="js/cordova-integration.js"></script>

    <!-- App Initialization -->
    <script>
        console.log('Flori Construction Admin App loaded');

        // App initialization
        function initializeApp() {
            console.log('Initializing app...');

            // Hide splash screen
            if (navigator.splashscreen) {
                navigator.splashscreen.hide();
            }

            // Hide loading screen and show login
            const loadingScreen = document.getElementById('loading-screen');
            const loginScreen = document.getElementById('login-screen');

            if (loadingScreen) {
                loadingScreen.style.display = 'none';
            }

            if (loginScreen) {
                loginScreen.style.display = 'block';
            }

            // Initialize Cordova features
            if (window.cordovaIntegration) {
                window.cordovaIntegration.init();
            }

            // Setup login form
            setupLoginForm();

            console.log('App initialized successfully');
        }

        // Login form setup
        function setupLoginForm() {
            const loginForm = document.getElementById('login-form');
            if (loginForm) {
                loginForm.addEventListener('submit', function (e) {
                    e.preventDefault();

                    // Simple demo login (replace with real authentication)
                    const username = document.getElementById('username').value;
                    const password = document.getElementById('password').value;

                    if (username && password) {
                        // Hide login screen and show main app
                        document.getElementById('login-screen').style.display = 'none';
                        document.getElementById('main-app').style.display = 'block';

                        // Show success toast
                        if (window.toast) {
                            window.toast.success('Welcome to Flori Construction Admin!');
                        }
                    } else {
                        // Show error
                        const errorDiv = document.getElementById('login-error');
                        if (errorDiv) {
                            errorDiv.textContent = 'Please enter username and password';
                            errorDiv.style.display = 'block';
                        }
                    }
                });
            }
        }

        // Setup bottom navigation and FAB
        function setupNavigation() {
            setupBottomNavigation();
            setupFAB();
        }

        // Setup bottom navigation
        function setupBottomNavigation() {
            const navItems = document.querySelectorAll('.nav-item');

            navItems.forEach(item => {
                item.addEventListener('click', function () {
                    // Remove active class from all items
                    navItems.forEach(nav => nav.classList.remove('active'));

                    // Add active class to clicked item
                    this.classList.add('active');

                    // Update page title
                    const page = this.dataset.page;
                    const pageTitle = document.getElementById('page-title');
                    if (pageTitle) {
                        const titles = {
                            'dashboard': 'Dashboard',
                            'projects': 'Projects',
                            'media': 'Media',
                            'services': 'Services'
                        };
                        pageTitle.textContent = titles[page] || 'Dashboard';
                    }

                    // Show corresponding page (if you have multiple pages)
                    showPage(page);
                });
            });
        }

        // Setup floating action button
        function setupFAB() {
            const fabMain = document.getElementById('fab-main');
            const fabMenu = document.getElementById('fab-menu');

            if (fabMain && fabMenu) {
                fabMain.addEventListener('click', function () {
                    this.classList.toggle('active');
                    fabMenu.classList.toggle('active');
                });

                // Close FAB menu when clicking outside
                document.addEventListener('click', function (e) {
                    if (!e.target.closest('.fab-container')) {
                        fabMain.classList.remove('active');
                        fabMenu.classList.remove('active');
                    }
                });
            }
        }

        // Show page function
        function showPage(page) {
            // Hide all pages
            const pages = document.querySelectorAll('.page');
            pages.forEach(p => p.classList.remove('active'));

            // Show selected page
            const targetPage = document.getElementById(page + '-page');
            if (targetPage) {
                targetPage.classList.add('active');
            }
        }

        // Initialize when Cordova is ready
        document.addEventListener('deviceready', function () {
            console.log('Device ready');
            initializeApp();
            setupNavigation();
        }, false);

        // Fallback for web testing
        document.addEventListener('DOMContentLoaded', function () {
            console.log('DOM ready');
            setTimeout(function () {
                initializeApp();
                setupNavigation();
            }, 1000);
        });
    </script>
</body>

</html>