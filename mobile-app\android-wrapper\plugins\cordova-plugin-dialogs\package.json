{"name": "cordova-plugin-dialogs", "version": "2.0.2", "description": "Cordova Notification Plugin", "types": "./types/index.d.ts", "cordova": {"id": "cordova-plugin-dialogs", "platforms": ["android", "browser", "ios", "windows"]}, "repository": {"type": "git", "url": "https://github.com/apache/cordova-plugin-dialogs"}, "bugs": {"url": "https://github.com/apache/cordova-plugin-dialogs/issues"}, "keywords": ["<PERSON><PERSON>", "notification", "ecosystem:cordova", "cordova-android", "cordova-browser", "cordova-ios", "cordova-windows"], "scripts": {"test": "npm run eslint", "eslint": "node node_modules/eslint/bin/eslint www && node node_modules/eslint/bin/eslint src && node node_modules/eslint/bin/eslint tests"}, "author": "Apache Software Foundation", "license": "Apache-2.0", "engines": {"cordovaDependencies": {"3.0.0": {"cordova": ">100"}}}, "devDependencies": {"eslint": "^3.19.0", "eslint-config-semistandard": "^11.0.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.3.0", "eslint-plugin-node": "^5.0.0", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1"}}