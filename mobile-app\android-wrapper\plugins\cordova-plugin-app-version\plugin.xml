<?xml version="1.0" encoding="UTF-8"?>
<plugin xmlns="http://www.phonegap.com/ns/plugins/1.0"
    xmlns:android="http://schemas.android.com/apk/res/android"
    id="cordova-plugin-app-version"
    version="0.1.14">

    <name>AppVersion</name>
    <description>
        This plugin will return the version of your App that you have set in
        packaging it. I.e. it will always match the version in the app store.
    </description>
    <license>MIT</license>

    <engines>
        <!--
            Cordova 2.8.0 is all I have tested on - it should work fine with earlier versions.
            Please modify the below line, test, and submit a PR if it works for you.
        -->
        <engine name="cordova" version=">=3.0.0" />
    </engines>

    <js-module src="www/AppVersionPlugin.js">
       <clobbers target="cordova.getAppVersion" />
    </js-module>

    <!-- android -->
    <platform name="android">
        <config-file target="res/xml/config.xml" parent="/*">
            <feature name="AppVersion">
                <param name="android-package" value="uk.co.whiteoctober.cordova.AppVersion"/>
            </feature>
        </config-file>
        <source-file src="src/android/AppVersion.java" target-dir="src/uk/co/whiteoctober/cordova" />
    </platform>

    <!-- blackberry10 -->
    <platform name="blackberry10">
        <dependency id="cordova-plugin-bb-app" />

        <config-file target="www/config.xml" parent="/widget">
            <feature name="AppVersion" value="AppVersion" />
        </config-file>
        <js-module src="www/blackberry10/AppVersionProxy.js" name="AppVersionProxy.js" >
            <runs />
        </js-module>
    </platform>

    <!-- ios -->
    <platform name="ios">
        <plugins-plist key="AppVersion" string="AppVersion" />

        <config-file target="config.xml" parent="/*">
            <feature name="AppVersion">
                <param name="ios-package" value="AppVersion" />
            </feature>
        </config-file>

        <header-file src="src/ios/AppVersion.h" />
        <source-file src="src/ios/AppVersion.m" />
    </platform>

    <!-- windows8 -->
    <platform name="windows">
        <js-module src="src/windows/AppVersionProxy.js" name="AppVersionProxy">
            <merges target=""/>
        </js-module>
    </platform>

    <!-- wp8 -->
    <platform name="wp8">
        <config-file target="config.xml" parent="/*">
            <feature name="AppVersion">
                <param name="wp-package" value="AppVersion"/>
            </feature>
        </config-file>

        <source-file src="src/wp8/AppVersion.cs" />
    </platform>

    <!-- browser -->
    <platform name="browser">
        <js-module src="src/browser/AppVersionProxy.js" name="AppVersionProxy">
            <runs />
        </js-module>
    </platform>
</plugin>
