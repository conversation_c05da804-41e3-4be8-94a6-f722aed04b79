{"name": "cordova-plugin-vibration", "version": "3.1.1", "description": "Cordova Vibration Plugin", "types": "./types/index.d.ts", "cordova": {"id": "cordova-plugin-vibration", "platforms": ["android", "ios", "windows"]}, "repository": {"type": "git", "url": "https://github.com/apache/cordova-plugin-vibration"}, "bugs": {"url": "https://issues.apache.org/jira/browse/CB"}, "keywords": ["<PERSON><PERSON>", "vibration", "ecosystem:cordova", "cordova-android", "cordova-ios", "cordova-windows"], "scripts": {"test": "npm run eslint", "eslint": "eslint www && eslint src && eslint tests"}, "author": "Apache Software Foundation", "license": "Apache-2.0", "engines": {"cordovaDependencies": {"4.0.0": {"cordova": ">100"}}}, "devDependencies": {"eslint": "^4.0.0", "eslint-config-semistandard": "^11.0.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.3.0", "eslint-plugin-node": "^5.0.0", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1"}}