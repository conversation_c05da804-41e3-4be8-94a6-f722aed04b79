language: objective-c

env:
  global:
    - SPEC_WWW_DIR=${TRAVIS_BUILD_DIR}/spec/www
    - TESTS_PATH=${TRAVIS_BUILD_DIR}/tests/tests.js
    - CORDOVA_PARAMEDIC_CMD="cordova-paramedic --platform ios --plugin ${TRAVIS_BUILD_DIR} --timeout 3600000"

git:
  depth: 2

node_js:
  - "4.6"

install:
  - echo -e "Host github.com\n\tStrictHostKeyChecking no\n" >> ~/.ssh/config
  - npm install -g cordova-paramedic
  - npm install -g cordova
  - npm install -g ios-sim

script:
  - date && cordova-paramedic --platform ios --plugin ${TRAVIS_BUILD_DIR} || echo error-ignored
  - date && cp ${SPEC_WWW_DIR}/spec/self-test.js ${TESTS_PATH} && ${CORDOVA_PARAMEDIC_CMD}
  - date && cp ${SPEC_WWW_DIR}/spec/db-tx-string-test.js ${TESTS_PATH} && ${CORDOVA_PARAMEDIC_CMD}
  - date && cp ${SPEC_WWW_DIR}/spec/db-tx-sql-select-value-test.js ${TESTS_PATH} && ${CORDOVA_PARAMEDIC_CMD}
  - date && cp ${SPEC_WWW_DIR}/spec/basic-db-tx-sql-storage-results.js ${TESTS_PATH} && ${CORDOVA_PARAMEDIC_CMD}
  - date && cp ${SPEC_WWW_DIR}/spec/db-sql-operations-test.js ${TESTS_PATH} && ${CORDOVA_PARAMEDIC_CMD}
  - date && cp ${SPEC_WWW_DIR}/spec/sql-batch-test.js ${TESTS_PATH} && ${CORDOVA_PARAMEDIC_CMD}
  - date
