### 0.1.14

* Fixed browser platform check for config.xml [Issue#128](https://github.com/sampart/cordova-plugin-app-version/issues/128)  
* Fix browser platform xhr error handling missing parameter [Issue#118](https://github.com/sampart/cordova-plugin-app-version/issues/118)

### 0.1.13

* [iOS: getAppName to return localized value (like Android)](https://github.com/sampart/cordova-plugin-app-version/pull/127)

### 0.1.12

* Fixed version mismatch
* Fixed browser platform install warning by removing unneeded feature tag

### 0.1.11

* Switched to new repo location

### 0.1.10

* Added browser platform

### 0.1.9

* Renamed Windows8 platform to Windows

### 0.1.7

* Add getPackageName feature (thanks to @gprasanth)
* Add getAppName feature (thanks to @mirko77)
* Fix for windows 8 (thanks to @deliriousrhino)
* Fix version number in plugin.xml file

### 0.1.6

* Split into two functions getAppVersion.getVersionNumber() and getAppVersion.getVersionCode() to return build number
* Fix a deprecation warning in iOS version

### 0.1.5

* iOS: Return version number but log and fall back to build number if it is nil (thanks to [Eddy Verbruggen](https://github.com/EddyVerbruggen))

### 0.1.4

* Return version number, not build number on iOS (thanks to http://www.humancopy.net)
* Support for Windows phone 8 (thanks to Cristi Badila / Gediminas Šaltenis)
* Support for AngularJS as well as jQuery (thanks to Matias Singers, [Red Ape Solutions](http://www.redapesolutions.com/))

### 0.1.3

* Fixes to Android for Corova 3 and above (thanks to AxoInsanit)

### 0.1.2

* Updated for Cordova 3 and above (thanks to Russell Keith-Magee [freakboy3742](https://github.com/freakboy3742)

### 0.1.1

* Improved README
* Bug fix for non-jQuery use
* Tidy plugin.xml

### 0.1.0

* First release
