/* Admin Panel Styles for Flori Construction Ltd */

/* CSS Custom Properties for Theming */
:root {
    /* Colors */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --secondary-dark: #1a252f;
    --success-color: #27ae60;
    --success-dark: #229954;
    --warning-color: #f39c12;
    --warning-dark: #e67e22;
    --danger-color: #e74c3c;
    --danger-dark: #c0392b;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;

    /* Typography */
    --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-monospace: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --line-height-tight: 1.25;
    --line-height-base: 1.5;
    --line-height-relaxed: 1.625;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Layout */
    --sidebar-width: 280px;
    --header-height: 70px;
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-base);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-base);
    background-color: var(--gray-100);
    color: var(--gray-800);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Admin Container */
.admin-container {
    display: flex;
    min-height: 100vh;
    background-color: var(--gray-100);
}

.admin-wrapper {
    display: flex;
    min-height: 100vh;
}

/* Modern Sidebar Styles - Hirelly Design */
.admin-sidebar {
    width: 280px;
    background: #1a1d29;
    color: var(--white);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 1000;
    transition: transform var(--transition-base);
    display: flex;
    flex-direction: column;
    border-right: 1px solid #2a2d3a;
}

/* Sidebar Brand */
.sidebar-brand {
    padding: 32px 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid #2a2d3a;
}

.brand-logo {
    width: 32px;
    height: 32px;
    background: #4f46e5;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.brand-text {
    font-size: 18px;
    font-weight: 600;
    color: white;
    letter-spacing: -0.025em;
}

/* Sidebar Menu Container */
.sidebar-menu-container {
    flex: 1;
    padding: 24px 0;
}

/* Sidebar Menu */
.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu-item {
    margin-bottom: 4px;
}

.sidebar-menu-link {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    color: #9ca3af;
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 0;
    font-weight: 500;
    font-size: 14px;
    position: relative;
}

.sidebar-menu-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.05);
}

.sidebar-menu-item.active .sidebar-menu-link {
    color: white;
    background: #4f46e5;
    border-radius: 0 24px 24px 0;
    margin-right: 16px;
}

.menu-icon {
    width: 20px;
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-icon i {
    font-size: 16px;
}

.menu-text {
    font-size: 14px;
    font-weight: 500;
}

.sidebar-menu-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: rgba(255, 255, 255, 0.1);
    transition: width var(--transition-base);
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.sidebar-menu-link:hover,
.sidebar-menu-link:focus {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-left-color: var(--primary-color);
    transform: translateX(2px);
    outline: none;
}

.sidebar-menu-link:hover::before,
.sidebar-menu-link:focus::before {
    width: 100%;
}

.sidebar-menu-item.active .sidebar-menu-link {
    background-color: rgba(255, 255, 255, 0.15);
    color: var(--white);
    border-left-color: var(--primary-color);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.sidebar-menu-icon {
    width: 24px;
    margin-right: var(--spacing-md);
    text-align: center;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-menu-icon i {
    font-size: var(--font-size-base);
    line-height: 1;
}

.sidebar-menu-text {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    flex: 1;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-menu-indicator {
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    flex-shrink: 0;
    margin-left: var(--spacing-sm);
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 24px;
    border-top: 1px solid #2a2d3a;
}

.sidebar-upgrade {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-radius: 12px;
    padding: 16px;
    position: relative;
    overflow: hidden;
}

.upgrade-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
}

.upgrade-content h4 {
    color: white;
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 4px 0;
}

.upgrade-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    margin: 0 0 12px 0;
    line-height: 1.4;
}

.upgrade-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    width: 100%;
    justify-content: center;
}

.upgrade-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.upgrade-btn i {
    font-size: 10px;
}

/* Enhanced Animations and Interactions */
.sidebar-menu-link {
    position: relative;
    overflow: hidden;
}

.sidebar-menu-link::after {
    content: '';
    position: absolute;
    top: 50%;
    right: var(--spacing-md);
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    transform: translateY(-50%) scale(0);
    transition: transform var(--transition-base);
}

.sidebar-menu-item.active .sidebar-menu-link::after {
    transform: translateY(-50%) scale(1);
}

/* Smooth scrolling for sidebar content */
.sidebar-content {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.sidebar-menu-link:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
}

/* Loading state for menu items */
.sidebar-menu-item.loading .sidebar-menu-link {
    opacity: 0.6;
    pointer-events: none;
}

.sidebar-menu-item.loading .sidebar-menu-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid var(--white);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Hover effects for section headers */
.sidebar-section-header {
    transition: all var(--transition-base);
}

.sidebar-section:hover .sidebar-section-title {
    color: rgba(255, 255, 255, 0.8);
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.user-info {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
    margin-right: var(--spacing-md);
    position: relative;
}

.user-avatar i {
    font-size: 32px;
    color: rgba(255, 255, 255, 0.8);
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--white);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    font-size: var(--font-size-xs);
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar-actions {
    display: flex;
    justify-content: center;
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
}

.sidebar-action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-base);
    position: relative;
}

.sidebar-action:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    transform: translateY(-1px);
}

.sidebar-action:active {
    transform: translateY(0);
}

/* Main Content */
.admin-main {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: #f8fafc;
}

/* Modern Header - Hirelly Design */
.admin-header {
    background: white;
    border-bottom: 1px solid #f1f5f9;
    height: 72px;
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
    padding: 0;
}

.header-content {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 32px;
    gap: 24px;
}

/* Sidebar Toggle */
.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: #64748b;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    background: #f1f5f9;
    color: #334155;
}

/* Header Search */
.header-search {
    flex: 1;
    max-width: 600px;
}

.search-container {
    position: relative;
    width: 100%;
}

.search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #94a3b8;
    font-size: 16px;
    pointer-events: none;
}

.search-input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    font-size: 14px;
    background: #f8fafc;
    transition: all 0.2s ease;
    outline: none;
}

.search-input:focus {
    border-color: #4f46e5;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-input::placeholder {
    color: #94a3b8;
}

/* Header Right */
.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Header Actions */
.header-action {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    border: none;
    background: #f8fafc;
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.header-action:hover {
    background: #e2e8f0;
    color: #334155;
}

.header-action.notifications {
    position: relative;
}

/* User Profile */
.user-profile {
    position: relative;
}

.user-profile-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.user-profile-btn:hover {
    background: #f8fafc;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-fallback {
    width: 100%;
    height: 100%;
    background: #4f46e5;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    color: #1e293b;
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    min-width: 240px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1000;
    margin-top: 8px;
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: 16px;
    border-bottom: 1px solid #f1f5f9;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.user-avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-fallback-small {
    width: 100%;
    height: 100%;
    background: #4f46e5;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 16px;
}

.user-details .user-name {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2px;
}

.user-details .user-role {
    font-size: 12px;
    color: #64748b;
    text-transform: capitalize;
}

.dropdown-divider {
    height: 1px;
    background: #f1f5f9;
    margin: 8px 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: #475569;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: #f8fafc;
    color: #1e293b;
}

.dropdown-item.logout {
    color: #ef4444;
}

.dropdown-item.logout:hover {
    background: #fef2f2;
    color: #dc2626;
}

.dropdown-item i {
    width: 16px;
    font-size: 14px;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    cursor: pointer;
    display: none;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.sidebar-toggle:hover {
    background-color: var(--gray-100);
    color: var(--gray-800);
}

.header-title-section {
    min-width: 0;
    flex: 1;
}

.admin-header h1 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: var(--line-height-tight);
}

/* Breadcrumb Navigation */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-600);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-fast);
}

.breadcrumb-item:hover {
    color: var(--primary-color);
}

.breadcrumb-item.current {
    color: var(--gray-800);
    font-weight: var(--font-weight-semibold);
}

.breadcrumb-separator {
    color: var(--gray-400);
    font-size: var(--font-size-xs);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-menu {
    display: flex;
    align-items: center;
    position: relative;
    gap: var(--spacing-md);
}

.user-welcome {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-fast);
}

.dropdown-toggle:hover {
    background-color: var(--gray-100);
    color: var(--gray-800);
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + var(--spacing-sm));
    right: 0;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 180px;
    display: none;
    z-index: 1000;
    overflow: hidden;
}

.dropdown:hover .dropdown-menu {
    display: block;
    animation: fadeInDown 0.2s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-700);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

.dropdown-menu a:hover {
    background-color: var(--gray-50);
    color: var(--gray-900);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--spacing-xs) 0;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.header-action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-fast);
    position: relative;
    background: transparent;
    border: none;
    cursor: pointer;
}

.header-action:hover {
    background-color: var(--gray-100);
    color: var(--gray-800);
    transform: translateY(-1px);
}

.header-action:active {
    transform: translateY(0);
}

.notifications {
    cursor: pointer;
}

.notification-badge {
    position: absolute;
    top: 6px;
    right: 6px;
    background: var(--danger-color);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    line-height: 1;
    box-shadow: 0 0 0 2px var(--white);
}

/* Modern Content Area */
.admin-content {
    flex: 1;
    padding: 32px;
    background: rgb(241 241 250);
    position: relative;
    overflow-y: auto;
}

/* Enhanced Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 32px;
    color: white;
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.dashboard-title-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    position: relative;
    z-index: 1;
}

.dashboard-title h1 {
    font-size: 36px;
    font-weight: 700;
    color: white;
    margin: 0 0 12px 0;
    letter-spacing: -0.025em;
}

.dashboard-subtitle {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.welcome-text {
    font-size: 18px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
    display: block;
}

.dashboard-subtitle p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    line-height: 1.5;
}

.dashboard-stats-summary {
    display: flex;
    gap: 32px;
    align-items: center;
}

.stat-summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-summary-item .stat-number {
    font-size: 28px;
    font-weight: 700;
    color: white;
    line-height: 1;
    margin-bottom: 4px;
}

.stat-summary-item .stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.dashboard-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.action-buttons {
    display: flex;
    gap: 12px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn.primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-btn.primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.dashboard-filters {
    display: flex;
    align-items: center;
    gap: 16px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.filter-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
    font-weight: 500;
    min-width: 120px;
    backdrop-filter: blur(10px);
}

.filter-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.15);
}

.filter-select option {
    background: #1e293b;
    color: white;
}

.refresh-btn {
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.refresh-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: rotate(180deg);
}

/* Quick Stats Bar */
.quick-stats-bar {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 32px;
}

.quick-stat-item {
    background: white;
    border-radius: 16px;
    padding: 20px;
    border: 1px solid #f1f5f9;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.2s ease;
}

.quick-stat-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.quick-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.quick-stat-icon.revenue {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.quick-stat-icon.inquiries {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.quick-stat-icon.media {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.quick-stat-icon.performance {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.quick-stat-content {
    flex: 1;
    min-width: 0;
}

.quick-stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    line-height: 1;
    margin-bottom: 4px;
}

.quick-stat-label {
    display: block;
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 6px;
}

.quick-stat-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.quick-stat-change.positive {
    color: #10b981;
}

.quick-stat-change.negative {
    color: #ef4444;
}

.quick-stat-change.neutral {
    color: #64748b;
}

.quick-stat-change i {
    font-size: 10px;
}

.admin-content::before {
    content: '';
    position: fixed;
    top: 0;
    left: var(--sidebar-width);
    right: 0;
    height: 100vh;
    background:
        radial-gradient(circle at 20% 20%, rgba(52, 152, 219, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(23, 162, 184, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(52, 152, 219, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.admin-content > * {
    position: relative;
    z-index: 1;
}

/* Welcome Banner */
.welcome-banner {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.welcome-banner::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.welcome-banner::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, 50%);
}

.welcome-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.welcome-text h1 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    line-height: var(--line-height-tight);
}

.welcome-text p {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin: 0;
    font-weight: var(--font-weight-normal);
}

.welcome-stats {
    display: flex;
    gap: var(--spacing-xl);
}

.welcome-stat {
    text-align: center;
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 100px;
}

.welcome-stat .stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-xs);
}

.welcome-stat .stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: var(--font-weight-medium);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    transition: all var(--transition-base);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    transition: height var(--transition-base);
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.02), rgba(41, 128, 185, 0.05));
    opacity: 0;
    transition: opacity var(--transition-base);
    pointer-events: none;
}

.stat-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.stat-card:hover::before {
    height: 6px;
}

.stat-card:hover::after {
    opacity: 1;
}

.stat-card:hover .stat-icon {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.stat-card:hover .stat-info h3 {
    color: var(--primary-color);
}

.stat-icon {
    width: 72px;
    height: 72px;
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-xl);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    font-size: var(--font-size-2xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
    position: relative;
}

.stat-icon::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--border-radius-xl);
    opacity: 0;
    transition: opacity var(--transition-base);
    z-index: -1;
}

.stat-icon.completed {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
}

.stat-icon.completed::before {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
}

.stat-icon.ongoing {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
}

.stat-icon.ongoing::before {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
}

.stat-icon.media {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.stat-icon.media::before {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.stat-icon.inquiries {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    position: relative;
}

.stat-icon.inquiries::before {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-icon .notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    padding: 4px 8px;
    border-radius: 12px;
    min-width: 20px;
    text-align: center;
    line-height: 1;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
    border: 2px solid var(--white);
    animation: pulse 2s infinite;
}

.stat-info {
    flex: 1;
    min-width: 0;
}

.stat-info h3 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    line-height: var(--line-height-tight);
    transition: color var(--transition-base);
}

.stat-info p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-details {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
}

.stat-highlight {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-fast);
}

.stat-highlight.new {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.15));
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.stat-highlight.new i {
    color: var(--danger-color);
    animation: blink 1.5s ease-in-out infinite;
}

.stat-highlight.today {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.15));
    color: var(--primary-color);
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.stat-highlight.today i {
    color: var(--primary-color);
}

.inquiry-card:hover .stat-highlight {
    transform: scale(1.05);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.dashboard-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-base);
    position: relative;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    opacity: 0;
    transition: opacity var(--transition-base);
}

.dashboard-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: var(--primary-color);
}

.dashboard-card:hover::before {
    opacity: 1;
}

.card-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-xl);
    right: var(--spacing-xl);
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--gray-300), transparent);
}

.card-header h2 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-header h2::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 2px;
}

.card-content {
    padding: var(--spacing-xl);
    position: relative;
}

.no-data {
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
    padding: var(--spacing-2xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.no-data::before {
    content: '📋';
    font-size: var(--font-size-3xl);
    opacity: 0.5;
}

/* Project and Inquiry Lists */
.project-list,
.inquiry-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.project-item,
.inquiry-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.project-item::before,
.inquiry-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    opacity: 0;
    transition: opacity var(--transition-base);
}

.project-item:hover,
.inquiry-item:hover {
    background: var(--white);
    box-shadow: var(--shadow-md);
    transform: translateX(4px);
    border-color: var(--primary-color);
}

.project-item:hover::before,
.inquiry-item:hover::before {
    opacity: 1;
}

.project-info,
.inquiry-info {
    flex: 1;
    min-width: 0;
}

.project-info h4,
.inquiry-info h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    transition: color var(--transition-fast);
}

.project-item:hover .project-info h4,
.inquiry-item:hover .inquiry-info h4 {
    color: var(--primary-color);
}

.project-info p,
.inquiry-info p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
    line-height: var(--line-height-base);
}

.project-date,
.inquiry-date {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.project-date::before,
.inquiry-date::before {
    content: '📅';
    font-size: var(--font-size-xs);
}

.project-status,
.inquiry-status {
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.project-status::before,
.inquiry-status::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

.project-status.completed {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.project-status.ongoing {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.inquiry-status.new {
    background: linear-gradient(135deg, #cce5ff, #b3d7ff);
    color: #004085;
    border: 1px solid #b3d7ff;
}

.inquiry-status.replied {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: var(--spacing-2xl);
}

.quick-actions h2 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.quick-actions h2::before {
    content: '⚡';
    font-size: var(--font-size-lg);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: var(--spacing-lg);
}

.action-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    text-align: center;
    text-decoration: none;
    color: var(--gray-700);
    transition: all var(--transition-base);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    transform: scaleX(0);
    transition: transform var(--transition-base);
}

.action-card::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.02), rgba(23, 162, 184, 0.05));
    opacity: 0;
    transition: opacity var(--transition-base);
    pointer-events: none;
}

.action-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.action-card:hover::before {
    transform: scaleX(1);
}

.action-card:hover::after {
    opacity: 1;
}

.action-card:hover i {
    transform: scale(1.1);
    color: var(--primary-color);
}

.action-card i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    transition: all var(--transition-base);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(23, 162, 184, 0.1));
    border-radius: var(--border-radius-lg);
}

.action-card span {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    transition: color var(--transition-fast);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    text-decoration: none;
    border-radius: var(--border-radius-lg);
    border: none;
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:hover::before {
    opacity: 1;
}

.btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), var(--danger-dark));
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #138496);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline::before {
    background: var(--primary-color);
}

.btn-outline:hover {
    color: var(--white);
}

.btn-outline:hover::before {
    opacity: 1;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-control.error {
    border-color: #e74c3c;
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table th,
.table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.table th {
    background: #f8f9fa;
    font-weight: 500;
    color: #2c3e50;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* Alerts */
.alert {
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-error,
.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background: #cce5ff;
    border-color: #b3d7ff;
    color: #004085;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    gap: 5px;
}

.pagination a,
.pagination span {
    padding: 8px 12px;
    border: 1px solid #ddd;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.pagination a:hover {
    background: #f8f9fa;
    border-color: #3498db;
}

.pagination .active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* File Upload */
.file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%;
}

.file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: block;
    padding: 40px 20px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.file-upload:hover .file-upload-label {
    border-color: #3498db;
    background: #e3f2fd;
}

.file-upload-label i {
    font-size: 48px;
    color: #6c757d;
    margin-bottom: 10px;
    display: block;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

/* Enhanced Responsive Design */

/* Tablet Styles (768px - 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
    .admin-sidebar {
        width: 240px;
    }

    .sidebar-header {
        padding: var(--spacing-lg);
    }

    .sidebar-logo img {
        width: 45px;
        height: 45px;
    }

    .sidebar-title h3 {
        font-size: var(--font-size-base);
    }

    .sidebar-menu-link {
        padding: var(--spacing-sm) var(--spacing-md);
        min-height: 44px;
    }

    .sidebar-menu-text {
        font-size: var(--font-size-xs);
    }
}

/* Mobile Styles (≤768px) */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        width: min(280px, 85vw);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
    }

    .admin-sidebar.show {
        transform: translateX(0);
    }

    .admin-main {
        margin-left: 0;
    }

    .sidebar-toggle {
        display: block !important;
    }

    /* Modern Header Mobile */
    .header-content {
        padding: 0 16px;
        gap: 16px;
    }

    .header-search {
        max-width: none;
        flex: 1;
    }

    .search-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .user-name {
        display: none;
    }

    /* Enhanced Dashboard Header Mobile */
    .dashboard-header {
        padding: 24px 20px;
        margin-bottom: 24px;
    }

    .dashboard-title-section {
        flex-direction: column;
        gap: 20px;
        margin-bottom: 20px;
    }

    .dashboard-title h1 {
        font-size: 28px;
    }

    .welcome-text {
        font-size: 16px;
    }

    .dashboard-subtitle p {
        font-size: 14px;
    }

    .dashboard-stats-summary {
        gap: 20px;
        justify-content: center;
    }

    .stat-summary-item .stat-number {
        font-size: 24px;
    }

    .dashboard-actions {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .action-buttons {
        justify-content: center;
    }

    .dashboard-filters {
        flex-wrap: wrap;
        justify-content: center;
        gap: 12px;
    }

    .filter-group {
        min-width: 100px;
    }

    /* Quick Stats Bar Mobile */
    .quick-stats-bar {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 24px;
    }

    .quick-stat-item {
        padding: 16px;
        gap: 12px;
    }

    .quick-stat-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .quick-stat-value {
        font-size: 20px;
    }

    .quick-stat-label {
        font-size: 12px;
    }

    /* Overview Section Mobile */
    .overview-section {
        flex-direction: column;
        gap: 16px;
        margin-bottom: 24px;
    }

    .overview-card {
        padding: 20px;
        gap: 16px;
    }

    .overview-icon {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }

    .overview-number {
        font-size: 36px;
    }

    .overview-label {
        font-size: 14px;
    }

    /* Status Overview Mobile */
    .status-overview-section {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 24px;
    }

    .status-chart {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .activity-section {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .admin-content {
        padding: 16px;
    }

    .admin-header {
        padding: 0 var(--spacing-lg);
        height: 60px;
    }

    .admin-content {
        padding: var(--spacing-lg);
    }

    .admin-content::before {
        left: 0;
    }

    /* Enhanced Mobile Sidebar */
    .sidebar-header {
        padding: var(--spacing-lg) var(--spacing-md);
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .sidebar-logo img {
        width: 60px;
        height: 60px;
    }

    .sidebar-title h3 {
        font-size: var(--font-size-lg);
        margin-bottom: 0;
    }

    .sidebar-subtitle {
        font-size: var(--font-size-xs);
    }

    .sidebar-content {
        padding: var(--spacing-lg) 0;
    }

    .sidebar-section {
        margin-bottom: var(--spacing-xl);
    }

    .sidebar-section-header {
        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-sm);
    }

    .sidebar-menu-link {
        padding: var(--spacing-lg) var(--spacing-lg);
        min-height: 56px; /* Larger touch targets for mobile */
        font-size: var(--font-size-base);
    }

    .sidebar-menu-icon {
        width: 28px;
        margin-right: var(--spacing-lg);
    }

    .sidebar-menu-icon i {
        font-size: var(--font-size-lg);
    }

    .sidebar-menu-text {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-medium);
    }

    .sidebar-footer {
        padding: var(--spacing-xl) var(--spacing-lg);
    }

    .welcome-banner {
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
    }

    .welcome-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .welcome-text h1 {
        font-size: var(--font-size-2xl);
    }

    .welcome-text p {
        font-size: var(--font-size-base);
    }

    .welcome-stats {
        gap: var(--spacing-lg);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .user-welcome {
        display: none;
    }

    .breadcrumb {
        display: none;
    }

    .admin-header h1 {
        margin-bottom: 0;
    }

    .header-actions {
        gap: var(--spacing-xs);
    }

    .header-action {
        width: 36px;
        height: 36px;
    }

    .admin-header h1 {
        font-size: var(--font-size-xl);
    }

    .stat-card {
        padding: var(--spacing-lg);
    }

    .stat-icon {
        width: 56px;
        height: 56px;
        margin-right: var(--spacing-lg);
    }

    .stat-info h3 {
        font-size: var(--font-size-2xl);
    }
}

/* Small Mobile Styles (≤480px) */
@media (max-width: 480px) {
    .actions-grid {
        grid-template-columns: 1fr;
    }

    .table {
        font-size: var(--font-size-xs);
    }

    .table th,
    .table td {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .admin-content {
        padding: var(--spacing-md);
    }

    .welcome-banner {
        padding: var(--spacing-lg);
    }

    .welcome-text h1 {
        font-size: var(--font-size-xl);
    }

    .welcome-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .welcome-stat {
        min-width: auto;
        padding: var(--spacing-md);
    }

    .stats-grid {
        gap: var(--spacing-md);
    }

    .stat-card {
        padding: var(--spacing-md);
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        margin-right: 0;
        margin-bottom: var(--spacing-md);
    }

    .admin-header h1 {
        font-size: var(--font-size-lg);
    }

    .sidebar-footer {
        position: relative;
    }

    /* Enhanced Small Mobile Sidebar */
    .admin-sidebar {
        width: 100vw;
        max-width: 100vw;
    }

    .sidebar-header {
        padding: var(--spacing-md);
    }

    .sidebar-logo img {
        width: 50px;
        height: 50px;
    }

    .sidebar-title h3 {
        font-size: var(--font-size-base);
    }

    .sidebar-content {
        padding: var(--spacing-md) 0;
    }

    .sidebar-section {
        margin-bottom: var(--spacing-lg);
    }

    .sidebar-section-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .sidebar-section-title {
        font-size: 10px;
    }

    .sidebar-menu-link {
        padding: var(--spacing-md) var(--spacing-md);
        min-height: 52px;
        margin-right: 0;
    }

    .sidebar-menu-icon {
        width: 24px;
        margin-right: var(--spacing-md);
    }

    .sidebar-menu-icon i {
        font-size: var(--font-size-base);
    }

    .sidebar-menu-text {
        font-size: var(--font-size-sm);
    }

    .sidebar-footer {
        padding: var(--spacing-lg) var(--spacing-md);
    }

    .alert-container {
        top: var(--spacing-md);
        right: var(--spacing-md);
        left: var(--spacing-md);
        max-width: none;
    }
}

/* ===== ENHANCED HIRELLY DASHBOARD STYLES ===== */

/* Overview Section - Matching Reference Colors */
.overview-section {
    display: flex;
    gap: 24px;
    margin-bottom: 32px;
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    border: 1px solid #f1f5f9;
    transition: all 0.2s ease;
    flex: 1;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
}

.overview-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

/* Projects Card - Blue Icon (matching reference) */
.projects-card .overview-icon {
    background: #4f46e5;
    color: white;
}

/* Inquiries Card - Brown/Orange Icon (matching reference) */
.inquiries-card .overview-icon {
    background: #b45309;
    color: white;
}

.overview-icon {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    flex-shrink: 0;
}

.overview-content {
    flex: 1;
}

.overview-number {
    font-size: 48px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
    line-height: 1;
}

.overview-label {
    font-size: 16px;
    color: #64748b;
    margin-bottom: 12px;
    font-weight: 500;
}

.view-all-link {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #4f46e5;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.view-all-link:hover {
    color: #3730a3;
}

.view-all-link i {
    font-size: 12px;
}

/* Status Overview Section */
.status-overview-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.status-overview-card,
.upcoming-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    border: 1px solid #f1f5f9;
}

.status-overview-card h3,
.upcoming-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 20px 0;
}

/* Donut Chart Styles */
.status-chart {
    display: flex;
    align-items: center;
    gap: 32px;
}

.chart-container {
    position: relative;
    flex-shrink: 0;
}

.donut-chart {
    position: relative;
}

.chart-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.chart-percentage {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    line-height: 1;
}

.chart-label {
    display: block;
    font-size: 12px;
    color: #64748b;
    margin-top: 4px;
}

.status-legend {
    flex: 1;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-color.completed {
    background: #4f46e5;
}

.legend-color.ongoing {
    background: #f59e0b;
}

.legend-color.pending {
    background: #e5e7eb;
}

.legend-text {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

/* Upcoming Schedule Card */
.upcoming-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.schedule-nav {
    display: flex;
    gap: 8px;
}

.nav-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #64748b;
}

.nav-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
    color: #1e293b;
}

.upcoming-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.upcoming-date {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.date-label {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.date-value {
    font-size: 14px;
    font-weight: 500;
    color: #1e293b;
}

.upcoming-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #f1f5f9;
}

.upcoming-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.upcoming-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.upcoming-details {
    flex: 1;
    min-width: 0;
}

.upcoming-title {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2px;
}

.upcoming-subtitle {
    font-size: 12px;
    color: #64748b;
}

.upcoming-time {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    background: white;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.view-schedule-link {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #4f46e5;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.view-schedule-link:hover {
    color: #3730a3;
}

.view-schedule-link i {
    font-size: 12px;
}

/* Chart Section */
.chart-section {
    grid-column: span 2;
}

.chart-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    border: 1px solid #f1f5f9;
}

.chart-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 20px 0;
}

.progress-chart {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.progress-item {
    display: flex;
    align-items: center;
    gap: 16px;
}

.progress-label {
    font-size: 14px;
    font-weight: 500;
    color: #475569;
    min-width: 80px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #f1f5f9;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-fill.completed {
    background: #10b981;
}

.progress-fill.ongoing {
    background: #f59e0b;
}

.progress-value {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    min-width: 30px;
    text-align: right;
}

/* Schedule Section */
.schedule-section {
    grid-column: span 2;
}

.schedule-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    border: 1px solid #f1f5f9;
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.schedule-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.schedule-nav {
    display: flex;
    gap: 8px;
}

.nav-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.schedule-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.schedule-date {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.date-label {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.date-value {
    font-size: 14px;
    font-weight: 500;
    color: #1e293b;
}

.schedule-item {
    display: flex;
    gap: 16px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 12px;
}

.schedule-time {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    min-width: 80px;
}

.schedule-details {
    flex: 1;
}

.schedule-title {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2px;
}

.schedule-location {
    font-size: 12px;
    color: #64748b;
}

.view-schedule-link {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #4f46e5;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.view-schedule-link:hover {
    color: #3730a3;
}

.view-schedule-link i {
    font-size: 12px;
}

/* Activity Section */
.activity-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.activity-card,
.saved-jobs-card {
    background: white;
    border-radius: 16px;
    border: 1px solid #f1f5f9;
    overflow: hidden;
}

.activity-header,
.saved-jobs-header {
    padding: 24px 24px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-header h3,
.saved-jobs-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.view-all-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #4f46e5;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.view-all-btn:hover {
    color: #3730a3;
}

.view-all-btn i {
    font-size: 12px;
}

.activity-content,
.saved-jobs-content {
    padding: 0 24px 24px;
}

.activity-date-label {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 16px;
}

.activity-list,
.saved-jobs-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.activity-item,
.saved-job-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border: 1px solid #f1f5f9;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.activity-item:hover,
.saved-job-item:hover {
    border-color: #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.activity-icon,
.job-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.activity-icon {
    background: #f1f5f9;
    color: #64748b;
}

.job-icon {
    background: #fef3c7;
    color: #d97706;
}

.activity-details,
.job-details {
    flex: 1;
    min-width: 0;
}

.activity-title,
.job-title {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.activity-meta,
.job-meta {
    display: flex;
    gap: 8px;
    font-size: 12px;
    color: #64748b;
}

.activity-meta span,
.job-meta span {
    position: relative;
}

.activity-meta span:not(:last-child)::after,
.job-meta span:not(:last-child)::after {
    content: '•';
    margin-left: 8px;
    color: #cbd5e1;
}

.activity-status,
.job-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
}

.status-badge.completed {
    background: #dcfce7;
    color: #166534;
}

.status-badge.ongoing {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.pending {
    background: #f1f5f9;
    color: #64748b;
}

.status-badge.new {
    background: #dbeafe;
    color: #1e40af;
}

.status-badge.contacted {
    background: #f3e8ff;
    color: #7c3aed;
}

.status-badge.quoted {
    background: #ecfdf5;
    color: #059669;
}

.status-badge.closed {
    background: #f1f5f9;
    color: #64748b;
}

.status-badge.default {
    background: #f8fafc;
    color: #64748b;
}

.activity-menu,
.job-menu {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    color: #94a3b8;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.activity-menu:hover,
.job-menu:hover {
    background: #f1f5f9;
    color: #64748b;
}

.days-to-apply {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #64748b;
}

.no-activity,
.no-saved-jobs {
    text-align: center;
    padding: 32px;
    color: #94a3b8;
}

.no-activity i,
.no-saved-jobs i {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.no-activity p,
.no-saved-jobs p {
    margin: 0;
    font-size: 14px;
}

/* Sidebar Overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    transition: opacity var(--transition-base);
    backdrop-filter: blur(2px);
}

.sidebar-overlay.show {
    opacity: 1;
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--gray-300);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltip Styles */
.tooltip {
    position: absolute;
    background: var(--gray-900);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
    z-index: 10000;
    opacity: 0;
    transition: opacity var(--transition-fast);
    pointer-events: none;
    box-shadow: var(--shadow-lg);
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--gray-900);
}

/* Animation Classes */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-slow);
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* ===== ENHANCED ADMIN PAGES STYLES ===== */

/* Universal Page Header Styles */
.page-header-enhanced,
.services-header,
.projects-header,
.media-header,
.inquiries-header,
.analytics-header,
.seo-header,
.branding-header,
.settings-header,
.profile-header,
.content-header,
.testimonials-header,
.users-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

/* Universal Header Background Effect */
.page-header-enhanced::before,
.services-header::before,
.projects-header::before,
.media-header::before,
.inquiries-header::before,
.analytics-header::before,
.seo-header::before,
.branding-header::before,
.settings-header::before,
.profile-header::before,
.content-header::before,
.testimonials-header::before,
.users-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

/* Universal Header Content */
.page-header-enhanced .header-content,
.services-header-content,
.projects-header-content,
.media-header .header-content,
.inquiries-header-content,
.analytics-header-content,
.seo-header-content,
.branding-header-content,
.settings-header-content,
.profile-header-content,
.content-header-content,
.testimonials-header-content,
.users-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

/* Universal Title Styles */
.page-header-enhanced h1,
.page-header-enhanced h2,
.services-title-section h2.services-main-title,
.projects-title-section h2.projects-main-title,
.media-header h1.page-title,
.inquiries-title-section h2,
.analytics-title-section h2,
.seo-title-section h2,
.branding-title-section h2,
.settings-title-section h2,
.profile-title-section h2,
.content-title-section h2,
.testimonials-title-section h2,
.users-title-section h2 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* Universal Icon Styles */
.page-header-enhanced i,
.services-icon,
.projects-icon,
.media-icon,
.inquiries-icon,
.analytics-icon,
.seo-icon,
.branding-icon,
.settings-icon,
.profile-icon,
.content-icon,
.testimonials-icon,
.users-icon {
    font-size: var(--font-size-2xl);
    opacity: 0.9;
}

/* Universal Subtitle Styles */
.page-header-enhanced .page-description,
.services-subtitle,
.projects-subtitle,
.media-header .page-description,
.inquiries-subtitle,
.analytics-subtitle,
.seo-subtitle,
.branding-subtitle,
.settings-subtitle,
.profile-subtitle,
.content-subtitle,
.testimonials-subtitle,
.users-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin-bottom: var(--spacing-lg);
}

/* Universal Stats Container */
.page-header-enhanced .stats,
.services-stats,
.projects-stats,
.media-stats,
.inquiries-stats,
.analytics-stats,
.seo-stats,
.branding-stats,
.settings-stats,
.profile-stats,
.content-stats,
.testimonials-stats,
.users-stats {
    display: flex;
    gap: var(--spacing-xl);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 80px;
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-xs);
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: var(--font-weight-medium);
}

/* Search & Filters */
.search-filters-container {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.modern-search-form {
    width: 100%;
}

.search-input-group {
    display: flex;
    align-items: center;
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-sm);
    transition: all var(--transition-base);
    position: relative;
}

.search-input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    background: var(--white);
}

.search-icon {
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--gray-500);
    font-size: var(--font-size-lg);
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: var(--spacing-md);
    font-size: var(--font-size-base);
    color: var(--gray-800);
    outline: none;
}

.search-input::placeholder {
    color: var(--gray-500);
}

.search-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.search-btn,
.clear-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    transition: all var(--transition-base);
    border: none;
    cursor: pointer;
}

.search-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
}

.search-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.clear-btn {
    background: var(--gray-200);
    color: var(--gray-700);
}

.clear-btn:hover {
    background: var(--gray-300);
    color: var(--gray-800);
}

/* Universal Card Styles */
.admin-card,
.dashboard-card,
.content-card,
.form-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-base);
}

.admin-card:hover,
.dashboard-card:hover,
.content-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-content {
    padding: var(--spacing-xl);
}

.card-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
}

/* Universal Form Styles */
.modern-form,
.admin-form {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.form-section {
    margin-bottom: var(--spacing-2xl);
}

.section-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
}

.section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0 0 var(--spacing-xs) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-description {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.form-field.full-width {
    grid-column: 1 / -1;
}

.field-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--gray-700);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.field-help {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-top: var(--spacing-xs);
}

/* Media Grid Styles */
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.media-item {
    overflow: hidden;
    transition: all var(--transition-base);
}

.media-preview {
    height: 200px;
    overflow: hidden;
    position: relative;
    background: var(--gray-100);
}

.media-file {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-base);
}

.media-item:hover .media-file {
    transform: scale(1.05);
}

.media-overlay {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
}

.media-type-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--white);
    backdrop-filter: blur(10px);
}

.media-type-badge.image {
    background: rgba(59, 130, 246, 0.9);
}

.media-type-badge.video {
    background: rgba(245, 158, 11, 0.9);
}

.media-details {
    margin-bottom: var(--spacing-lg);
}

.media-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: 1.4;
}

.media-description {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0 0 var(--spacing-sm) 0;
    line-height: 1.5;
}

.media-meta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.meta-item i {
    width: 12px;
    text-align: center;
}

.media-actions {
    border-top: 1px solid var(--gray-200);
    padding-top: var(--spacing-md);
}

.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

.copy-btn {
    background: var(--info-color);
    color: var(--white);
}

.copy-btn:hover {
    background: var(--info-dark);
}

/* Current Media Preview */
.current-media-preview {
    max-width: 500px;
    margin: 0 auto;
}

.media-preview-container {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.current-media-file {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: contain;
    background: var(--gray-100);
}

.media-info-overlay {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.media-size {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(0, 0, 0, 0.7);
    color: var(--white);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    backdrop-filter: blur(10px);
}

/* Analytics Page Styles */
.analytics-overview {
    margin-bottom: var(--spacing-2xl);
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.overview-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-base);
}

.overview-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.overview-card .card-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.overview-card .card-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--white);
}

.projects-card .card-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.media-card .card-icon {
    background: linear-gradient(135deg, #10b981, #047857);
}

.inquiries-card .card-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.users-card .card-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.overview-card .card-title h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0;
}

.overview-card .card-title p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.overview-card .card-content {
    padding: var(--spacing-xl);
}

.main-stat {
    margin-bottom: var(--spacing-lg);
}

.main-stat .stat-number {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    display: block;
}

.main-stat .stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: var(--font-weight-medium);
}

.sub-stats {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-md);
}

.sub-stat {
    text-align: center;
    flex: 1;
}

.sub-stat .sub-number {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
    display: block;
}

.sub-stat .sub-label {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Charts Section */
.charts-section {
    margin-bottom: var(--spacing-2xl);
}

.section-header {
    margin-bottom: var(--spacing-xl);
}

.section-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin: 0 0 var(--spacing-xs) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-description {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    margin: 0;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.chart-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.chart-toggle {
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition-base);
}

.chart-toggle:hover {
    background: var(--gray-200);
    color: var(--gray-800);
}

.chart-container {
    height: 300px;
    position: relative;
}

/* Detailed Analytics */
.detailed-analytics {
    margin-bottom: var(--spacing-2xl);
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.analytics-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.progress-analytics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.progress-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.label-text {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--gray-700);
}

.label-value {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.progress-bar {
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--border-radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: var(--border-radius-full);
    transition: width var(--transition-base);
}

.progress-fill.active {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.progress-fill.completed {
    background: linear-gradient(90deg, #10b981, #047857);
}

.progress-percentage {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    text-align: right;
}

.media-breakdown {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.media-type-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
}

.media-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--white);
}

.media-icon.images {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.media-icon.videos {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.media-details h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0;
}

.media-details p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.media-details small {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.storage-info {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

.storage-stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* Form Header Styles */
.form-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.form-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.form-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.form-main-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.form-icon {
    font-size: var(--font-size-2xl);
    opacity: 0.9;
}

.form-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin: 0;
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-start;
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--gray-200);
    margin-top: var(--spacing-xl);
}

/* Empty State */
.empty-state-container {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-3xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.empty-state {
    max-width: 400px;
    margin: 0 auto;
}

.empty-state-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto var(--spacing-xl);
    background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.empty-state-icon::before {
    content: '';
    position: absolute;
    inset: -4px;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    border-radius: 50%;
    opacity: 0.1;
    animation: pulse 2s infinite;
}

.empty-state-icon i {
    font-size: var(--font-size-3xl);
    color: var(--gray-500);
    z-index: 1;
    position: relative;
}

.empty-state-content h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
}

.empty-state-content p {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-xl);
}

.empty-state-actions {
    display: flex;
    justify-content: center;
}

/* Services Table Container */
.services-table-container {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border-bottom: 1px solid var(--gray-200);
}

.table-title h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0 0 var(--spacing-xs) 0;
}

.table-count {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    background: var(--gray-100);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-lg);
    font-weight: var(--font-weight-medium);
}

.table-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.view-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition-base);
}

.view-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(52, 152, 219, 0.05);
}

.view-toggle.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
}

/* Modern Table */
.modern-table-wrapper {
    overflow-x: auto;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
}

.modern-table thead {
    background: var(--gray-50);
}

.modern-table th {
    padding: var(--spacing-lg) var(--spacing-xl);
    text-align: left;
    border-bottom: 2px solid var(--gray-200);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
    position: relative;
}

.th-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.th-content i {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
}

.modern-table tbody tr {
    transition: all var(--transition-base);
    border-bottom: 1px solid var(--gray-200);
}

.modern-table tbody tr:hover {
    background: var(--gray-50);
    transform: translateX(2px);
}

.modern-table td {
    padding: var(--spacing-lg) var(--spacing-xl);
    vertical-align: top;
}

/* Table Column Styles */
.col-image {
    width: 80px;
}

.col-title {
    min-width: 300px;
}

.col-status {
    width: 120px;
}

.col-meta {
    width: 150px;
}

.col-actions {
    width: 120px;
}

/* Service Row Components */
.service-image-container {
    position: relative;
}

.service-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.service-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.service-thumbnail-placeholder {
    width: 60px;
    height: 60px;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    font-size: var(--font-size-lg);
    border: 2px dashed var(--gray-300);
}

.service-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.service-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0;
    line-height: var(--line-height-tight);
}

.service-description {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    line-height: var(--line-height-base);
    margin: 0;
}

.service-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.meta-item code {
    background: var(--gray-100);
    padding: 2px var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    font-family: var(--font-family-monospace);
    font-size: var(--font-size-xs);
}

/* Status Indicators */
.status-indicators {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-fast);
}

.status-badge.featured {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.2));
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-badge.regular {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.1), rgba(108, 117, 125, 0.2));
    color: var(--gray-600);
    border: 1px solid rgba(108, 117, 125, 0.3);
}

/* Meta Info */
.meta-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.meta-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-xs);
}

.meta-label {
    color: var(--gray-500);
    font-weight: var(--font-weight-medium);
}

.meta-value {
    color: var(--gray-700);
    font-weight: var(--font-weight-semibold);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius-md);
    border: none;
    cursor: pointer;
    transition: all var(--transition-base);
    text-decoration: none;
    font-size: var(--font-size-sm);
}

.edit-btn {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(52, 152, 219, 0.2));
    color: var(--primary-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.edit-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.view-btn {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.2));
    color: var(--info-color);
    border: 1px solid rgba(23, 162, 184, 0.3);
}

.view-btn:hover {
    background: var(--info-color);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.delete-form {
    display: inline-block;
}

.delete-btn {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.2));
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.delete-btn:hover {
    background: var(--danger-color);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* Enhanced Pagination */
.table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
}

.pagination-info {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: var(--font-weight-medium);
}

.modern-pagination {
    display: flex;
    align-items: center;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--white);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-md);
    color: var(--gray-700);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-base);
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.pagination-numbers {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin: 0 var(--spacing-md);
}

.pagination-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: var(--white);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-md);
    color: var(--gray-700);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-base);
}

.pagination-number:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.pagination-number.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.pagination-ellipsis {
    padding: 0 var(--spacing-sm);
    color: var(--gray-500);
    font-weight: var(--font-weight-medium);
}

/* Form Header */
.form-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.form-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.form-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.form-title-section h2.form-main-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.form-icon {
    font-size: var(--font-size-2xl);
    opacity: 0.9;
}

.form-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin: 0;
}

/* Modern Form Sections */
.modern-service-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.form-section {
    background: var(--white) !important;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-base);
}

.form-section:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.section-header {
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border-bottom: 1px solid var(--gray-200);
}

.section-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-title i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.section-description {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    margin: 0;
    line-height: var(--line-height-relaxed);
}

.section-content {
    padding: var(--spacing-xl);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-field.full-width {
    grid-column: 1 / -1;
}

.field-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-xs);
}

.field-label i {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
}

.form-input,
.form-textarea {
    padding: var(--spacing-md);
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    font-family: inherit;
    transition: all var(--transition-base);
    background: var(--white);
    color: var(--gray-800);
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    transform: translateY(-1px);
}

.form-input.error,
.form-textarea.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
    line-height: var(--line-height-relaxed);
}

.field-help {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    line-height: var(--line-height-base);
    margin-top: var(--spacing-xs);
}

/* Character Counter (Enhanced) */
.char-count {
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.count-text {
    font-weight: var(--font-weight-medium);
    min-width: 100px;
}

.count-bar {
    flex: 1;
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
}

.count-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--primary-color));
    border-radius: 2px;
    transition: width var(--transition-base);
}

.char-count.normal .count-text {
    color: var(--gray-600);
}

.char-count.near-limit .count-text {
    color: var(--warning-color);
}

.char-count.near-limit .count-progress {
    background: linear-gradient(90deg, var(--warning-color), var(--danger-color));
}

.char-count.over-limit .count-text {
    color: var(--danger-color);
    font-weight: var(--font-weight-bold);
}

.char-count.over-limit .count-progress {
    background: var(--danger-color);
}

/* Modern File Upload */
.current-image-preview {
    margin-bottom: var(--spacing-xl);
}

.preview-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    display: block;
}

.image-preview-container {
    position: relative;
    display: inline-block;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.current-image {
    max-width: 200px;
    height: auto;
    display: block;
    transition: all var(--transition-base);
}

.image-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all var(--transition-base);
    cursor: pointer;
}

.image-preview-container:hover .image-overlay {
    opacity: 1;
}

.image-info {
    color: var(--white);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.modern-file-upload {
    position: relative;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 2;
}

.file-upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3xl);
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius-xl);
    background: var(--gray-50);
    cursor: pointer;
    transition: all var(--transition-base);
    text-align: center;
    min-height: 200px;
}

.file-upload-area:hover,
.file-upload-area.drag-over {
    border-color: var(--primary-color);
    background: rgba(52, 152, 219, 0.05);
    transform: translateY(-2px);
}

.upload-icon {
    font-size: var(--font-size-3xl);
    color: var(--gray-400);
    margin-bottom: var(--spacing-lg);
    transition: all var(--transition-base);
}

.file-upload-area:hover .upload-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

.upload-text {
    font-size: var(--font-size-base);
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.upload-formats {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.file-preview {
    display: none;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    position: relative;
}

.preview-image {
    width: 80px;
    height: 80px;
    background-size: cover;
    background-position: center;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.preview-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.file-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
}

.file-size {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

.remove-file {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    width: 24px;
    height: 24px;
    background: var(--danger-color);
    color: var(--white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    transition: all var(--transition-base);
}

.remove-file:hover {
    background: var(--danger-dark);
    transform: scale(1.1);
}

/* Modern Checkbox */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.modern-checkbox {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    cursor: pointer;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-base);
}

.modern-checkbox:hover {
    background: var(--gray-50);
}

.modern-checkbox input[type="checkbox"] {
    display: none;
}

.checkbox-mark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-base);
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-mark::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: var(--font-size-xs);
    color: var(--white);
    opacity: 0;
    transform: scale(0);
    transition: all var(--transition-base);
}

.modern-checkbox input[type="checkbox"]:checked + .checkbox-mark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.modern-checkbox input[type="checkbox"]:checked + .checkbox-mark::after {
    opacity: 1;
    transform: scale(1);
}

.checkbox-text {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.checkbox-text strong {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
}

.checkbox-text small {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    line-height: var(--line-height-base);
}

/* Form Actions */
.form-actions {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    margin-top: var(--spacing-xl);
}

.actions-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
}

.primary-actions,
.secondary-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* Responsive Design for Services Page */
@media (max-width: 1024px) {
    .services-header-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .services-stats {
        justify-content: center;
    }

    .form-header-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .actions-container {
        flex-direction: column-reverse;
        gap: var(--spacing-md);
    }

    .primary-actions,
    .secondary-actions {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .services-header,
    .form-header {
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
    }

    .services-title-section h2.services-main-title,
    .form-title-section h2.form-main-title {
        font-size: var(--font-size-2xl);
    }

    .services-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .stat-item {
        min-width: auto;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .search-filters-container {
        padding: var(--spacing-lg);
    }

    .search-input-group {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .search-actions {
        width: 100%;
        justify-content: center;
    }

    .table-header {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .table-footer {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .modern-table-wrapper {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .modern-table {
        min-width: 600px;
    }

    .form-section {
        margin: 0 -var(--spacing-md);
        border-radius: var(--border-radius-lg);
    }

    .section-header,
    .section-content {
        padding: var(--spacing-lg);
    }

    .file-upload-area {
        padding: var(--spacing-xl);
        min-height: 150px;
    }

    .upload-icon {
        font-size: var(--font-size-2xl);
    }
}

@media (max-width: 480px) {
    .services-header,
    .form-header {
        padding: var(--spacing-lg);
    }

    .services-title-section h2.services-main-title,
    .form-title-section h2.form-main-title {
        font-size: var(--font-size-xl);
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .empty-state-container {
        padding: var(--spacing-xl);
    }

    .empty-state-icon {
        width: 80px;
        height: 80px;
    }

    .empty-state-icon i {
        font-size: var(--font-size-2xl);
    }

    .action-buttons {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .action-btn {
        width: 100%;
        height: 40px;
        justify-content: flex-start;
        padding: 0 var(--spacing-md);
        gap: var(--spacing-sm);
    }

    .action-btn::after {
        content: attr(title);
        font-size: var(--font-size-xs);
    }

    .pagination-numbers {
        margin: var(--spacing-sm) 0;
    }

    .pagination-btn span {
        display: none;
    }
}

/* ===== ENHANCED PROJECTS PAGE STYLES ===== */

/* Projects Header (reuse services styles with project-specific naming) */
.projects-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.projects-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.projects-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.projects-title-section h2.projects-main-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.projects-icon {
    font-size: var(--font-size-2xl);
    opacity: 0.9;
}

.projects-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin-bottom: var(--spacing-lg);
}

.projects-stats {
    display: flex;
    gap: var(--spacing-xl);
}

.projects-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* Projects Table Container (reuse services styles) */
.projects-table-container {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

/* Project-specific table columns */
.col-location {
    width: 150px;
}

/* Project Row Components */
.project-image-container {
    position: relative;
}

.project-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.project-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.project-thumbnail-placeholder {
    width: 60px;
    height: 60px;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    font-size: var(--font-size-lg);
    border: 2px dashed var(--gray-300);
}

.project-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.project-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0;
    line-height: var(--line-height-tight);
}

.project-description {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    line-height: var(--line-height-base);
    margin: 0;
}

.project-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

/* Location Info */
.location-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.location-name {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    font-weight: var(--font-weight-medium);
}

.location-empty {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-style: italic;
}

/* Project Type Status Badges */
.status-badge.project-type-completed {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.2));
    color: #155724;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-badge.project-type-ongoing {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.2));
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

/* Timeline Info */
.timeline-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.timeline-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-xs);
}

.timeline-label {
    color: var(--gray-500);
    font-weight: var(--font-weight-medium);
    min-width: 60px;
}

.timeline-value {
    color: var(--gray-700);
    font-weight: var(--font-weight-semibold);
    text-align: right;
}

.timeline-value.ongoing {
    color: var(--warning-color);
    font-weight: var(--font-weight-bold);
}

/* Filter Select Enhancement */
.filter-section {
    display: flex;
    align-items: center;
    margin: 0 var(--spacing-md);
}

.filter-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    background: var(--white);
    color: var(--gray-700);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-base);
    min-width: 150px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Modern Project Form */
.modern-project-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

/* Responsive Design for Projects Page */
@media (max-width: 1024px) {
    .projects-header-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .projects-stats {
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .projects-header {
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
    }

    .projects-title-section h2.projects-main-title {
        font-size: var(--font-size-2xl);
    }

    .projects-stats {
        flex-direction: column;
        gap: var(--spacing-md);
        width: 100%;
    }

    .filter-section {
        margin: var(--spacing-sm) 0;
        width: 100%;
    }

    .filter-select {
        width: 100%;
    }

    .search-input-group {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .timeline-info {
        font-size: var(--font-size-xs);
    }

    .timeline-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }

    .timeline-label {
        min-width: auto;
    }

    .timeline-value {
        text-align: left;
    }
}

@media (max-width: 480px) {
    .projects-header {
        padding: var(--spacing-lg);
    }

    .projects-title-section h2.projects-main-title {
        font-size: var(--font-size-xl);
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .projects-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .stat-item {
        min-width: auto;
        padding: var(--spacing-sm);
    }

    .location-info,
    .timeline-info {
        font-size: var(--font-size-xs);
    }

    .status-indicators {
        flex-direction: column;
        gap: var(--spacing-xs);
        align-items: flex-start;
    }

    .status-badge {
        font-size: var(--font-size-xs);
        padding: 2px var(--spacing-xs);
    }
}

/* ===== ENHANCED PROFILE PAGE STYLES ===== */

/* Profile Header */
.profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.profile-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.profile-avatar-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.profile-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--white);
    border: 3px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.profile-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.profile-name {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    margin: 0;
    line-height: var(--line-height-tight);
}

.profile-role {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin: 0;
    font-weight: var(--font-weight-medium);
}

.profile-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-sm);
}

.profile-meta .meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.profile-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-sm);
}

.profile-status .status-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: rgba(40, 167, 69, 0.2);
    border: 1px solid rgba(40, 167, 69, 0.3);
    border-radius: var(--border-radius-lg);
    color: #d4edda;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    backdrop-filter: blur(10px);
}

/* Profile Content */
.profile-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
}

.profile-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Modern Profile Forms */
.modern-profile-form,
.modern-password-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-input.disabled {
    background: var(--gray-100);
    color: var(--gray-600);
    cursor: not-allowed;
    border-color: var(--gray-300);
}

.form-input.disabled:focus {
    border-color: var(--gray-300);
    box-shadow: none;
}

/* Password Input Groups */
.password-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input {
    padding-right: 50px;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-base);
    z-index: 2;
}

.password-toggle:hover {
    color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
}

/* Password Strength Indicator */
.password-strength {
    margin-top: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.strength-bar {
    flex: 1;
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    border-radius: 3px;
    transition: all var(--transition-base);
    background: var(--gray-400);
}

.strength-fill.weak {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.strength-fill.fair {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

.strength-fill.good {
    background: linear-gradient(90deg, #f1c40f, #f39c12);
}

.strength-fill.strong {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.strength-text {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--gray-600);
    min-width: 100px;
}

/* Password Match Indicator */
.password-match {
    margin-top: var(--spacing-sm);
}

.match-indicator {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.match-indicator.match {
    color: var(--success-color);
}

.match-indicator.no-match {
    color: var(--danger-color);
}

/* Account Information Grid */
.account-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.info-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-sm);
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.info-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.info-content {
    flex: 1;
}

.info-content h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0 0 var(--spacing-xs) 0;
}

.info-content p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
    line-height: var(--line-height-base);
}

/* Account Summary */
.account-summary {
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-top: var(--spacing-lg);
}

.summary-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
}

.summary-header h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.summary-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
}

.summary-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--gray-600);
}

.summary-value {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
}

.security-level {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
}

.security-level.high {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.2));
    color: #155724;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

/* Responsive Design for Profile Page */
@media (max-width: 1024px) {
    .profile-header-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .profile-avatar-section {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .profile-meta {
        justify-content: center;
    }

    .account-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .profile-header {
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
    }

    .profile-avatar {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }

    .profile-name {
        font-size: var(--font-size-xl);
    }

    .profile-role {
        font-size: var(--font-size-base);
    }

    .profile-meta {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
    }

    .account-info-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .info-card {
        padding: var(--spacing-md);
    }

    .info-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }

    .summary-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .profile-header {
        padding: var(--spacing-lg);
    }

    .profile-avatar {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }

    .profile-name {
        font-size: var(--font-size-lg);
    }

    .profile-meta .meta-item {
        font-size: var(--font-size-xs);
    }

    .password-strength {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .strength-text {
        min-width: auto;
    }

    .account-summary {
        padding: var(--spacing-lg);
    }
}

/* ===== ENHANCED INQUIRIES PAGE STYLES ===== */

/* Inquiries Header */
.inquiries-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.inquiries-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.header-info .page-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin: 0 0 var(--spacing-sm) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-info .page-description {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin: 0;
    line-height: var(--line-height-relaxed);
}

.header-stats {
    display: flex;
    gap: var(--spacing-xl);
}

.quick-stat {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.quick-stat .stat-number {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.quick-stat .stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    font-weight: var(--font-weight-medium);
}

/* Enhanced Statistics Grid */
.inquiries-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.inquiry-stat-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.inquiry-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    transition: all var(--transition-base);
}

.inquiry-stat-card.new::before {
    background: linear-gradient(180deg, #f39c12, #e67e22);
}

.inquiry-stat-card.read::before {
    background: linear-gradient(180deg, #3498db, #2980b9);
}

.inquiry-stat-card.replied::before {
    background: linear-gradient(180deg, #27ae60, #2ecc71);
}

.inquiry-stat-card.closed::before {
    background: linear-gradient(180deg, #e74c3c, #c0392b);
}

.inquiry-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.inquiry-stat-card:hover::before {
    width: 8px;
}

.inquiry-stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--white);
    flex-shrink: 0;
}

.inquiry-stat-card.new .stat-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.inquiry-stat-card.read .stat-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.inquiry-stat-card.replied .stat-icon {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.inquiry-stat-card.closed .stat-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-content {
    flex: 1;
}

.stat-content .stat-number {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.stat-content .stat-label {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.stat-trend i {
    font-size: var(--font-size-xs);
}

/* Enhanced Filters */
.inquiries-filters {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    margin-bottom: var(--spacing-2xl);
    overflow: hidden;
}

.filters-header {
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filters-header h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.results-count {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: var(--font-weight-medium);
}

.filters-content {
    padding: var(--spacing-xl);
}

.modern-filter-form {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: var(--spacing-lg);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-group i {
    position: absolute;
    left: var(--spacing-md);
    color: var(--gray-500);
    font-size: var(--font-size-base);
    z-index: 1;
}

.search-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 40px;
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    transition: all var(--transition-base);
    background: var(--white);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.filter-select {
    padding: var(--spacing-md);
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    background: var(--white);
    transition: all var(--transition-base);
    min-width: 150px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* Inquiries List */
.inquiries-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg) 0;
    border-bottom: 2px solid var(--gray-200);
}

.list-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.list-actions {
    display: flex;
    gap: var(--spacing-md);
}

.inquiries-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Modern Inquiry Cards */
.modern-inquiry-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-base);
    overflow: hidden;
    position: relative;
    cursor: pointer;
}

.modern-inquiry-card:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.modern-inquiry-card.new {
    border-left: 4px solid #f39c12;
}

.modern-inquiry-card.read {
    border-left: 4px solid #3498db;
}

.modern-inquiry-card.replied {
    border-left: 4px solid #27ae60;
}

.modern-inquiry-card.closed {
    border-left: 4px solid #e74c3c;
}

.inquiry-priority {
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
}

.priority-indicator {
    width: 100%;
    height: 100%;
    transition: all var(--transition-base);
}

.priority-indicator.new {
    background: linear-gradient(180deg, #f39c12, #e67e22);
}

.priority-indicator.read {
    background: linear-gradient(180deg, #3498db, #2980b9);
}

.priority-indicator.replied {
    background: linear-gradient(180deg, #27ae60, #2ecc71);
}

.priority-indicator.closed {
    background: linear-gradient(180deg, #e74c3c, #c0392b);
}

.inquiry-main {
    padding: var(--spacing-xl);
    margin-left: 6px;
}

.inquiry-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
}

.inquiry-title {
    flex: 1;
}

.client-name {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin: 0 0 var(--spacing-xs) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.inquiry-subject {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--gray-600);
    margin: 0;
    line-height: var(--line-height-base);
}

.inquiry-meta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-end;
}

.inquiry-date {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-new {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(243, 156, 18, 0.2));
    color: #b7791f;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.status-badge.status-read {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(52, 152, 219, 0.2));
    color: #2980b9;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.status-badge.status-replied {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(39, 174, 96, 0.2));
    color: #27ae60;
    border: 1px solid rgba(39, 174, 96, 0.3);
}

.status-badge.status-closed {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.2));
    color: #c0392b;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Inquiry Details */
.inquiry-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.contact-content {
    flex: 1;
}

.contact-content label {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: var(--spacing-xs);
}

.contact-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-base);
}

.contact-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.service-tag,
.project-ref {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: linear-gradient(135deg, var(--info-color), var(--primary-color));
    color: var(--white);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
}

/* Message and Notes Sections */
.message-section,
.notes-section {
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--gray-200);
}

.message-header,
.notes-header {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--gray-300);
}

.message-header h6,
.notes-header h6 {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    color: var(--gray-700);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.message-content,
.notes-content {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    color: var(--gray-800);
    white-space: pre-wrap;
    word-wrap: break-word;
}

.notes-section {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.05), rgba(243, 156, 18, 0.1));
    border-color: rgba(243, 156, 18, 0.2);
}

.expand-message-btn {
    margin-top: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    cursor: pointer;
    padding: var(--spacing-xs) 0;
    transition: all var(--transition-base);
}

.expand-message-btn:hover {
    color: var(--primary-dark);
}

/* Inquiry Actions */
.inquiry-actions {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.action-group {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    flex-wrap: wrap;
}

.primary-actions {
    justify-content: flex-start;
}

.status-actions {
    justify-content: center;
}

.danger-actions {
    justify-content: flex-end;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    transition: all var(--transition-base);
    border: none;
    cursor: pointer;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.status-form {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.status-update-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    background: var(--gray-50);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
}

.status-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    background: var(--white);
    min-width: 120px;
}

.notes-input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    background: var(--white);
    min-width: 200px;
    flex: 1;
}

.notes-input:focus,
.status-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

/* Modern Pagination */
.modern-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-2xl);
    padding: var(--spacing-xl);
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.pagination-info {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: var(--font-weight-medium);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--white);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    color: var(--gray-700);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-base);
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.pagination-numbers {
    display: flex;
    gap: var(--spacing-xs);
}

.pagination-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-lg);
    background: var(--white);
    border: 1px solid var(--gray-300);
    color: var(--gray-700);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-base);
}

.pagination-number:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.pagination-number.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

/* Responsive Design for Inquiries */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .modern-filter-form {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .filter-actions {
        justify-content: center;
    }

    .contact-info {
        grid-template-columns: 1fr;
    }

    .action-group {
        justify-content: center;
    }

    .status-update-group {
        flex-direction: column;
        align-items: stretch;
    }
}

@media (max-width: 768px) {
    .inquiries-header {
        padding: var(--spacing-xl);
    }

    .header-info .page-title {
        font-size: var(--font-size-2xl);
    }

    .inquiries-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
    }

    .inquiry-stat-card {
        padding: var(--spacing-lg);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }

    .stat-content .stat-number {
        font-size: var(--font-size-2xl);
    }

    .inquiries-list-header {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .inquiry-header {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .inquiry-meta {
        align-items: flex-start;
    }

    .modern-pagination {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .inquiries-header {
        padding: var(--spacing-lg);
    }

    .header-stats {
        flex-direction: column;
        gap: var(--spacing-md);
        width: 100%;
    }

    .quick-stat {
        padding: var(--spacing-md);
    }

    .inquiries-stats-grid {
        grid-template-columns: 1fr;
    }

    .inquiry-stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .modern-inquiry-card {
        margin: 0 -var(--spacing-md);
        border-radius: var(--border-radius-lg);
    }

    .inquiry-main {
        padding: var(--spacing-lg);
    }

    .action-group {
        flex-direction: column;
        align-items: stretch;
    }

    .status-update-group {
        padding: var(--spacing-sm);
    }

    .notes-input {
        min-width: auto;
    }

    .pagination-numbers {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Dashboard Entry Animations */
.stats-grid .stat-card {
    animation: slideInUp 0.6s ease-out forwards;
}

.stats-grid .stat-card:nth-child(1) { animation-delay: 0.1s; }
.stats-grid .stat-card:nth-child(2) { animation-delay: 0.2s; }
.stats-grid .stat-card:nth-child(3) { animation-delay: 0.3s; }
.stats-grid .stat-card:nth-child(4) { animation-delay: 0.4s; }
.stats-grid .stat-card:nth-child(5) { animation-delay: 0.5s; }

/* ===== ENHANCED TESTIMONIALS PAGE STYLES ===== */

/* Testimonials Header */
.testimonials-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.testimonials-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.testimonials-stats {
    margin-top: var(--spacing-lg);
    display: flex;
    justify-content: center;
}

.testimonials-stats .stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.testimonials-stats .stat-number {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.testimonials-stats .stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    font-weight: var(--font-weight-medium);
}

/* ===== ENHANCED ANALYTICS PAGE STYLES ===== */

/* Analytics Header */
.analytics-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.analytics-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

/* Analytics Stats Grid */
.analytics-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.analytics-stat-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.analytics-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #4facfe, #00f2fe);
    transition: all var(--transition-base);
}

.analytics-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.analytics-stat-card:hover::before {
    width: 8px;
}

.analytics-stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.analytics-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--white);
    flex-shrink: 0;
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.analytics-stat-content {
    flex: 1;
}

.analytics-stat-content .stat-number {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.analytics-stat-content .stat-label {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.analytics-stat-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.analytics-stat-trend i {
    font-size: var(--font-size-xs);
}

/* Chart Container */
.chart-container {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    margin-bottom: var(--spacing-xl);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
}

.chart-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.chart-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.chart-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-700);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-base);
}

.chart-btn:hover,
.chart-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

/* Date Filter */
.date-filter {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-date-form {
    display: flex;
    align-items: end;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.date-input-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.date-input-group label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--white);
    opacity: 0.9;
}

.date-input-group input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    font-size: var(--font-size-sm);
    backdrop-filter: blur(10px);
}

.date-input-group input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.2);
}

.date-input-group input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* ===== ENHANCED EMAIL TEST PAGE STYLES ===== */

/* Email Test Header */
.email-test-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.email-test-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.system-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    font-weight: var(--font-weight-semibold);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.system-status.online {
    background: rgba(40, 167, 69, 0.2);
    color: #d4edda;
}

.system-status.offline {
    background: rgba(220, 53, 69, 0.2);
    color: #f8d7da;
}

/* Email Configuration Grid */
.email-config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.config-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.config-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.config-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.config-content {
    flex: 1;
}

.config-content h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0 0 var(--spacing-sm) 0;
}

.config-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-xs);
}

.config-status.success {
    color: var(--success-color);
}

.config-status.error {
    color: var(--danger-color);
}

.config-value {
    font-family: 'Courier New', monospace;
    background: var(--gray-100);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    word-break: break-all;
}

.config-description {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

/* Email Test Grid */
.email-test-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.test-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-base);
}

.test-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.test-header {
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.test-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.test-icon.basic {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
}

.test-icon.advanced {
    background: linear-gradient(135deg, var(--success-color), #2ecc71);
}

.test-info h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin: 0 0 var(--spacing-xs) 0;
}

.test-info p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
    line-height: var(--line-height-relaxed);
}

.test-content {
    padding: var(--spacing-xl);
}

.modern-test-form {
    margin-bottom: var(--spacing-xl);
}

.form-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    transition: all var(--transition-base);
    background: var(--white);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-help {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    margin-top: var(--spacing-xs);
}

.test-features {
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--gray-200);
}

.test-features h5 {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin: 0 0 var(--spacing-md) 0;
}

.test-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.test-features li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.test-features li:last-child {
    margin-bottom: 0;
}

.test-features li i {
    color: var(--success-color);
    font-size: var(--font-size-xs);
}

/* ===== ENHANCED MEDIA PAGE STYLES ===== */

/* Media Header */
.media-header {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.media-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.media-stats {
    margin-top: var(--spacing-lg);
    display: flex;
    justify-content: center;
}

.media-stats .stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.media-stats .stat-number {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.media-stats .stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    font-weight: var(--font-weight-medium);
}

/* Responsive Design for Email Test and Media Pages */
@media (max-width: 1024px) {
    .email-test-header,
    .media-header,
    .testimonials-header,
    .analytics-header {
        padding: var(--spacing-xl);
    }

    .email-config-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .email-test-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .analytics-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .email-test-header,
    .media-header,
    .testimonials-header,
    .analytics-header {
        padding: var(--spacing-lg);
    }

    .header-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .email-config-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .config-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .email-test-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .test-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .analytics-stats-grid {
        grid-template-columns: 1fr;
    }

    .analytics-stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .chart-header {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .chart-controls {
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .email-test-header,
    .media-header {
        padding: var(--spacing-md);
    }

    .system-status {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .config-card {
        padding: var(--spacing-lg);
    }

    .config-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }

    .test-card {
        margin: 0 -var(--spacing-sm);
    }

    .test-content {
        padding: var(--spacing-lg);
    }

    .test-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }

    .testimonials-stats,
    .media-stats {
        margin-top: var(--spacing-md);
    }

    .testimonials-stats .stat-item,
    .media-stats .stat-item {
        padding: var(--spacing-md);
    }

    .chart-container {
        padding: var(--spacing-lg);
    }
}

.dashboard-grid .dashboard-card {
    animation: slideInUp 0.6s ease-out forwards;
}

.dashboard-grid .dashboard-card:nth-child(1) { animation-delay: 0.6s; }
.dashboard-grid .dashboard-card:nth-child(2) { animation-delay: 0.7s; }

.quick-actions {
    animation: slideInUp 0.6s ease-out forwards;
    animation-delay: 0.8s;
}

.welcome-banner {
    animation: slideInDown 0.8s ease-out forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Pulse animation for notification badge */
.notification-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
    }
}

/* ===== SEO PAGE STYLES ===== */

/* SEO Header */
.seo-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.seo-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.seo-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.seo-header-text h1 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.seo-header-text p {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin-bottom: var(--spacing-lg);
}

.seo-stats {
    display: flex;
    gap: var(--spacing-xl);
}

.seo-stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.seo-stat .stat-icon {
    font-size: var(--font-size-lg);
}

.seo-stat .stat-text {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.seo-header-visual {
    display: flex;
    align-items: center;
    justify-content: center;
}

.seo-icon-container {
    position: relative;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.seo-icon-container i {
    font-size: var(--font-size-4xl);
    color: var(--white);
}

.seo-pulse {
    position: absolute;
    inset: -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: seo-pulse 2s infinite;
}

@keyframes seo-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

/* SEO Tab Navigation */
.seo-tab-navigation {
    display: flex;
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xs);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-2xl);
    border: 1px solid var(--gray-200);
    overflow-x: auto;
}

.seo-tab-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: transparent;
    border: none;
    border-radius: var(--border-radius-lg);
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    white-space: nowrap;
    min-width: 120px;
    justify-content: center;
}

.seo-tab-btn i {
    font-size: var(--font-size-base);
    transition: all var(--transition-base);
}

.seo-tab-btn:hover {
    color: var(--primary-color);
    background: rgba(52, 152, 219, 0.05);
}

.seo-tab-btn.active {
    color: var(--primary-color);
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.15));
    font-weight: var(--font-weight-semibold);
}

.seo-tab-btn.active i {
    transform: scale(1.1);
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    border-radius: 2px;
    transition: width var(--transition-base);
}

.seo-tab-btn.active .tab-indicator {
    width: 80%;
}

/* SEO Tab Content */
.seo-tab-content {
    display: none;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.seo-tab-content.active {
    display: block !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* SEO Sections */
.seo-section {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.seo-section-header {
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border-bottom: 1px solid var(--gray-200);
    position: relative;
}

.seo-section-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-xl);
    right: var(--spacing-xl);
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--gray-300), transparent);
}

.seo-section-header h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.seo-section-header p {
    color: var(--gray-600);
    font-size: var(--font-size-base);
    margin: 0;
    line-height: var(--line-height-relaxed);
}

/* Modern SEO Forms */
.seo-form-container {
    padding: var(--spacing-xl);
}

.modern-seo-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.form-section {
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-base);
}

.form-section:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.form-section h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--gray-300);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-field.full-width {
    grid-column: 1 / -1;
}

.form-field label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.form-field label i {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
}

.form-input,
.form-textarea {
    padding: var(--spacing-md);
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    font-family: inherit;
    transition: all var(--transition-base);
    background: var(--white);
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    transform: translateY(-1px);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
    line-height: var(--line-height-relaxed);
}

.form-help {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    line-height: var(--line-height-base);
    margin-top: var(--spacing-xs);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-2xl);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
}

/* Character Counter */
.char-count {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.count-text {
    font-weight: var(--font-weight-medium);
}

.count-bar {
    flex: 1;
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
}

.count-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--primary-color));
    border-radius: 2px;
    transition: width var(--transition-base);
}

.char-count.normal .count-text {
    color: var(--gray-600);
}

.char-count.near-limit .count-text {
    color: var(--warning-color);
}

.char-count.near-limit .count-progress {
    background: linear-gradient(90deg, var(--warning-color), var(--danger-color));
}

.char-count.over-limit .count-text {
    color: var(--danger-color);
    font-weight: var(--font-weight-bold);
}

.char-count.over-limit .count-progress {
    background: var(--danger-color);
}

/* Page Cards */
.pages-overview {
    padding: var(--spacing-xl);
}

.pages-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.pages-header h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0;
}

.pages-count {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    background: var(--gray-100);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-lg);
    font-weight: var(--font-weight-medium);
}

.modern-pages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.modern-page-card {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-lg);
    text-decoration: none;
    color: inherit;
    transition: all var(--transition-base);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.modern-page-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    opacity: 0;
    transition: opacity var(--transition-base);
}

.modern-page-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
}

.modern-page-card:hover::before {
    opacity: 1;
}

.page-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-lg);
}

.page-status {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
}

.page-status.optimized {
    background: var(--success-color);
    color: var(--white);
}

.page-status.pending {
    background: var(--warning-color);
    color: var(--white);
}

.page-card-content h5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.page-url {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-family: 'Courier New', monospace;
    margin-bottom: var(--spacing-sm);
}

.page-optimization-status {
    margin-top: var(--spacing-sm);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.optimized {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(34, 153, 84, 0.15));
    color: var(--success-color);
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.status-badge.pending {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.15));
    color: var(--warning-color);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

.page-card-footer {
    margin-top: auto;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--gray-200);
}

.edit-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-fast);
}

.modern-page-card:hover .edit-link {
    color: var(--primary-dark);
}

/* Page SEO Editor */
.page-seo-editor {
    padding: var(--spacing-xl);
}

.page-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--gray-200);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.page-breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    min-width: 0;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
}

.breadcrumb-separator {
    color: var(--gray-400);
    font-size: var(--font-size-xs);
}

.page-url-display {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gray-100);
    border-radius: var(--border-radius-md);
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.page-url-display i {
    color: var(--primary-color);
}

/* Enhanced Checkbox Fields */
.checkbox-field {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    cursor: pointer;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-base);
    border: 2px solid transparent;
}

.checkbox-field:hover {
    background: rgba(52, 152, 219, 0.05);
    border-color: rgba(52, 152, 219, 0.2);
}

.form-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-400);
    border-radius: var(--border-radius-sm);
    background: var(--white);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    flex-shrink: 0;
    margin-top: 2px;
}

.form-checkbox:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.form-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--gray-700);
    line-height: var(--line-height-base);
}

.checkbox-label i {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
}

.checkbox-field:hover .checkbox-label {
    color: var(--primary-color);
}

/* Form Field Enhancements */
.form-field .form-help {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    line-height: var(--line-height-base);
    margin-top: var(--spacing-xs);
    font-style: italic;
}

/* Analytics Dashboard */
.analytics-dashboard {
    padding: var(--spacing-xl);
}

.analytics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.analytics-stat {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.analytics-stat::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    opacity: 0;
    transition: opacity var(--transition-base);
}

.analytics-stat:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
}

.analytics-stat:hover::before {
    opacity: 1;
}

.analytics-stat .stat-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--white);
    flex-shrink: 0;
}

.analytics-stat .stat-icon.analytics {
    background: linear-gradient(135deg, #4285f4, #34a853);
}

.analytics-stat .stat-icon.console {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.analytics-stat .stat-icon.sitemap {
    background: linear-gradient(135deg, var(--info-color), #138496);
}

.analytics-stat .stat-icon.robots {
    background: linear-gradient(135deg, #6f42c1, #5a2d91);
}

.stat-content {
    flex: 1;
    min-width: 0;
}

.stat-content h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.stat-content .status {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    display: inline-block;
}

.stat-content .status.active {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(34, 153, 84, 0.15));
    color: var(--success-color);
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.stat-content .status.inactive {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.15));
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.2);
}

/* SEO Tools */
.seo-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
}

.seo-tool-card {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.seo-tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    opacity: 0;
    transition: opacity var(--transition-base);
}

.seo-tool-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
}

.seo-tool-card:hover::before {
    opacity: 1;
}

.tool-icon {
    width: 72px;
    height: 72px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
}

.seo-tool-card:hover .tool-icon {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.tool-content h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.tool-content p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-lg);
}

/* Responsive Design for SEO Page */
@media (max-width: 768px) {
    .seo-header-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .seo-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .seo-tab-navigation {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .seo-tab-btn {
        min-width: auto;
        flex: 1;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .modern-pages-grid {
        grid-template-columns: 1fr;
    }

    .analytics-overview {
        grid-template-columns: 1fr;
    }

    .seo-tools-grid {
        grid-template-columns: 1fr;
    }

    .analytics-stat {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .page-editor-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-lg);
    }

    .page-breadcrumb {
        justify-content: center;
    }

    .page-url-display {
        justify-content: center;
        text-align: center;
    }

    .checkbox-field {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .form-checkbox {
        margin-top: 0;
    }
}

/* Enhanced Alert Styles */
.alert-container {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: 10000;
    max-width: 400px;
    width: 100%;
}

.alert-dismissible {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    transform: translateX(100%);
    transition: all var(--transition-base);
    border-left: 4px solid;
}

.alert-dismissible.show {
    transform: translateX(0);
}

.alert-dismissible.hide {
    transform: translateX(100%);
    opacity: 0;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
}

.alert-icon {
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.alert-message {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-base);
}

.alert-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    opacity: 0.7;
    flex-shrink: 0;
}

.alert-close:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
}

.alert-success {
    background: var(--white);
    color: var(--success-color);
    border-left-color: var(--success-color);
}

.alert-error,
.alert-danger {
    background: var(--white);
    color: var(--danger-color);
    border-left-color: var(--danger-color);
}

.alert-warning {
    background: var(--white);
    color: var(--warning-color);
    border-left-color: var(--warning-color);
}

.alert-info {
    background: var(--white);
    color: var(--info-color);
    border-left-color: var(--info-color);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.mb-0 { margin-bottom: 0; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mt-20 { margin-top: 20px; }
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }
.gap-10 { gap: 10px; }
.gap-20 { gap: 20px; }

/* Grid System */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 10px;
}

@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Profile Page Styles */
.info-item {
    margin-bottom: 15px;
    padding: 10px 0;
    border-bottom: 1px solid #f1f1f1;
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-item strong {
    display: inline-block;
    min-width: 120px;
    color: #2c3e50;
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 3px;
    text-transform: uppercase;
}

.badge-success {
    background: #27ae60;
    color: white;
}

.badge-warning {
    background: #f39c12;
    color: white;
}

.badge-danger {
    background: #e74c3c;
    color: white;
}

.badge-info {
    background: #3498db;
    color: white;
}

/* Enhanced Admin Styles for New Features */
.tabs {
    margin-top: 20px;
}

.tab-buttons {
    display: flex;
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.tab-button {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-button:hover {
    color: #495057;
    background: #f8f9fa;
}

.tab-button.active {
    color: #e74c3c;
    border-bottom-color: #e74c3c;
    background: #fff;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.file-upload-area:hover {
    border-color: #e74c3c;
    background: #f8f9fa;
}

.file-upload-area.dragover {
    border-color: #e74c3c;
    background: #fff5f5;
}

.file-upload-label {
    display: block;
    cursor: pointer;
    color: #6c757d;
}

.file-upload-label i {
    font-size: 48px;
    color: #e74c3c;
    margin-bottom: 15px;
    display: block;
}

.color-input-group {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.color-input {
    width: 60px;
    height: 40px;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    cursor: pointer;
    padding: 0;
}

.color-preview {
    min-width: 120px;
    height: 40px;
    border-radius: 5px;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    font-weight: 500;
}

.font-preview {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-top: 15px;
    background: #f8f9fa;
}

.logo-preview {
    max-width: 200px;
    max-height: 100px;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    background: #f8f9fa;
}

.content-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
}

.content-label {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 16px;
}

.char-count {
    font-size: 12px;
    color: #6c757d;
    margin-top: 8px;
    text-align: right;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-success { background-color: #28a745; }
.status-warning { background-color: #ffc107; }
.status-error { background-color: #dc3545; }

.config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.config-item:last-child {
    border-bottom: none;
}

.test-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
    padding: 0 15px;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: 0 15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}

.col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
    padding: 0 15px;
}

.btn-block {
    width: 100%;
    display: block;
}

.btn-outline {
    background: transparent;
    border: 1px solid #3498db;
    color: #3498db;
}

.btn-outline:hover {
    background: #3498db;
    color: white;
}

.btn-info {
    background: #17a2b8;
}

.btn-info:hover {
    background: #138496;
}

.btn-primary {
    background: #e74c3c;
}

.btn-primary:hover {
    background: #c0392b;
}

/* Responsive Design for New Features */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }

    .admin-sidebar.active {
        transform: translateX(0);
    }

    .admin-main {
        margin-left: 0;
    }

    .sidebar-toggle {
        display: block !important;
    }

    .tab-buttons {
        overflow-x: auto;
        white-space: nowrap;
    }

    .color-input-group {
        flex-direction: column;
        align-items: flex-start;
    }

    .col-md-3,
    .col-md-4,
    .col-md-6,
    .col-md-8 {
        flex: 0 0 100%;
        max-width: 100%;
        padding: 0;
        margin-bottom: 20px;
    }
}
