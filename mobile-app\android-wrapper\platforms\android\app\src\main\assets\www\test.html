<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test</title>
</head>
<body style="margin: 0; padding: 0;">
    <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        font-family: Arial, sans-serif;
    ">
        <h1 style="font-size: 48px; margin: 20px; text-align: center;">
            🎉 TEST WORKING!
        </h1>
        <p style="font-size: 24px; margin: 20px; text-align: center;">
            If you see this, HTML is working
        </p>
    </div>
</body>
</html>
