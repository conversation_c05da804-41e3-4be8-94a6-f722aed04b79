package com.adobe.phonegap.push;

public interface PushConstants {
  public static final String COM_ADOBE_PHONEGAP_PUSH = "com.adobe.phonegap.push";
  public static final String REGISTRATION_ID = "registrationId";
  public static final String REGISTRATION_TYPE = "registrationType";
  public static final String FOREGROUND = "foreground";
  public static final String TITLE = "title";
  public static final String NOT_ID = "notId";
  public static final String PUSH_BUNDLE = "pushBundle";
  public static final String ICON = "icon";
  public static final String ICON_COLOR = "iconColor";
  public static final String SOUND = "sound";
  public static final String SOUND_DEFAULT = "default";
  public static final String SOUND_RINGTONE = "ringtone";
  public static final String VIBRATE = "vibrate";
  public static final String ACTIONS = "actions";
  public static final String CALLBACK = "callback";
  public static final String ACTION_CALLBACK = "actionCallback";
  public static final String DRAWABLE = "drawable";
  public static final String MSGCNT = "msgcnt";
  public static final String VIBRATION_PATTERN = "vibrationPattern";
  public static final String STYLE = "style";
  public static final String SUMMARY_TEXT = "summaryText";
  public static final String PICTURE = "picture";
  public static final String GCM_N = "gcm.n.";
  public static final String GCM_NOTIFICATION = "gcm.notification";
  public static final String GCM_NOTIFICATION_BODY = "gcm.notification.body";
  public static final String UA_PREFIX = "com.urbanairship.push";
  public static final String PARSE_COM_DATA = "data";
  public static final String ALERT = "alert";
  public static final String MESSAGE = "message";
  public static final String BODY = "body";
  public static final String SOUNDNAME = "soundname";
  public static final String COLOR = "color";
  public static final String LED_COLOR = "ledColor";
  public static final String PRIORITY = "priority";
  public static final String IMAGE = "image";
  public static final String STYLE_INBOX = "inbox";
  public static final String STYLE_PICTURE = "picture";
  public static final String STYLE_TEXT = "text";
  public static final String BADGE = "badge";
  public static final String INITIALIZE = "init";
  public static final String SUBSCRIBE = "subscribe";
  public static final String UNSUBSCRIBE = "unsubscribe";
  public static final String UNREGISTER = "unregister";
  public static final String EXIT = "exit";
  public static final String FINISH = "finish";
  public static final String HAS_PERMISSION = "hasPermission";
  public static final String ANDROID = "android";
  public static final String SENDER_ID = "senderID";
  public static final String CLEAR_BADGE = "clearBadge";
  public static final String CLEAR_NOTIFICATIONS = "clearNotifications";
  public static final String COLDSTART = "coldstart";
  public static final String ADDITIONAL_DATA = "additionalData";
  public static final String COUNT = "count";
  public static final String FROM = "from";
  public static final String COLLAPSE_KEY = "collapse_key";
  public static final String FORCE_SHOW = "forceShow";
  public static final String FCM = "FCM";
  public static final String CONTENT_AVAILABLE = "content-available";
  public static final String TOPICS = "topics";
  public static final String SET_APPLICATION_ICON_BADGE_NUMBER = "setApplicationIconBadgeNumber";
  public static final String GET_APPLICATION_ICON_BADGE_NUMBER = "getApplicationIconBadgeNumber";
  public static final String CLEAR_ALL_NOTIFICATIONS = "clearAllNotifications";
  public static final String VISIBILITY = "visibility";
  public static final String INLINE_REPLY = "inlineReply";
  public static final String INLINE_REPLY_LABEL = "replyLabel";
  public static final String LOC_KEY = "locKey";
  public static final String LOC_DATA = "locData";
  public static final String TWILIO_BODY = "twi_body";
  public static final String TWILIO_TITLE = "twi_title";
  public static final String TWILIO_SOUND = "twi_sound";
  public static final String AWS_PINPOINT_BODY = "pinpoint.notification.body";
  public static final String AWS_PINPOINT_PICTURE = "pinpoint.notification.imageUrl";
  public static final String AWS_PINPOINT_PREFIX = "pinpoint.notification";
  public static final String MP_MESSAGE = "mp_message";
  public static final String START_IN_BACKGROUND = "cdvStartInBackground";
  public static final String FORCE_START = "force-start";
  public static final String MESSAGE_KEY = "messageKey";
  public static final String TITLE_KEY = "titleKey";
  public static final String NO_CACHE = "no-cache";
  public static final String DISMISSED = "dismissed";
  public static final String IMAGE_TYPE = "image-type";
  public static final String IMAGE_TYPE_SQUARE = "square";
  public static final String IMAGE_TYPE_CIRCLE = "circle";
  public static final String SUBJECT = "subject";
  public static final String GOOGLE_APP_ID = "google_app_id";
  public static final String GCM_DEFAULT_SENDER_ID = "gcm_defaultSenderId";
  public static final String PUSH_DISMISSED = "push_dismissed";
  public static final String DEFAULT_CHANNEL_ID = "PushPluginChannel";
  public static final String CHANNELS = "channels";
  public static final String CHANNEL_ID = "id";
  public static final String CHANNEL_DESCRIPTION = "description";
  public static final String CHANNEL_IMPORTANCE = "importance";
  public static final String CHANNEL_LIGHT_COLOR = "lightColor";
  public static final String CHANNEL_VIBRATION = "vibration";
  public static final String ANDROID_CHANNEL_ID = "android_channel_id";
  public static final String CHANNEL_STATE = "state";
  public static final String CREATE_CHANNEL = "createChannel";
  public static final String DELETE_CHANNEL = "deleteChannel";
  public static final String ONGOING = "ongoing";
  public static final String LIST_CHANNELS = "listChannels";
  public static final String CLEAR_NOTIFICATION = "clearNotification";
}
