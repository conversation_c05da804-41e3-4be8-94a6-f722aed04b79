<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<plugin 
  xmlns="http://www.phonegap.com/ns/plugins/1.0" 
  xmlns:android="http://schemas.android.com/apk/res/android" 
  xmlns:amazon="http://schemas.android.com/apk/lib/com.amazon.device.ads" 
  xmlns:rim="http://www.blackberry.com/ns/widgets" id="phonegap-plugin-multidex" version="1.0.0">
  <name>Multidex</name>
  <description>Enable multidex in a Apache Cordova/PhoneGap application</description>
  <license>MIT</license>
  <platform name="android">
    <framework src="multidex.gradle" custom="true" type="gradleReference"/>
  </platform>
</plugin>