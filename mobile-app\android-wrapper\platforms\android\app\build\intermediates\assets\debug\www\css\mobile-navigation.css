/**
 * Mobile Navigation CSS for Flori Construction Admin App
 * Bottom Navigation Bar and Floating Action Button
 */

/* ===== MAIN APP LAYOUT UPDATES ===== */
#main-app {
    display: grid;
    grid-template-areas:
        "header"
        "content"
        "bottom-nav";
    grid-template-columns: 1fr;
    grid-template-rows: 60px 1fr 70px;
    height: 100vh;
    position: relative;
}

/* ===== HEADER UPDATES ===== */
.app-header {
    grid-area: header;
    background: white;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.app-logo {
    display: flex;
    align-items: center;
    margin-right: 15px;
}

.app-logo img {
    width: 32px;
    height: 32px;
    border-radius: 4px;
}

/* ===== MAIN CONTENT UPDATES ===== */
.main-content {
    grid-area: content;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    padding-bottom: 90px;
    /* Extra space for FAB */
}

/* ===== BOTTOM NAVIGATION ===== */
.bottom-navigation {
    grid-area: bottom-nav;
    background: white;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 8px 0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 70px;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: #757575;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    min-width: 60px;
    position: relative;
}

.nav-item i {
    font-size: 20px;
    margin-bottom: 4px;
    transition: all 0.3s ease;
}

.nav-item span {
    font-size: 11px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-item:hover {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.nav-item.active {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.nav-item.active i {
    transform: scale(1.1);
}

/* Active indicator */
.nav-item.active::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background: #e74c3c;
    border-radius: 0 0 3px 3px;
}

/* ===== FLOATING ACTION BUTTON ===== */
.fab-container {
    position: fixed;
    bottom: 90px;
    /* Above bottom navigation */
    right: 20px;
    z-index: 1001;
}

.fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(231, 76, 60, 0.5);
}

.fab:active {
    transform: scale(0.95);
}

.fab.active {
    transform: rotate(45deg);
}

/* FAB Menu */
.fab-menu {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.fab-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.fab-option {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: white;
    border: 2px solid #e74c3c;
    color: #e74c3c;
    font-size: 18px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.fab-option:hover {
    background: #e74c3c;
    color: white;
    transform: scale(1.1);
}

/* Tooltip for FAB options */
.fab-option::before {
    content: attr(data-tooltip);
    position: absolute;
    right: 60px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
}

.fab-option:hover::before {
    opacity: 1;
    visibility: visible;
}

/* FAB animation sequence */
.fab-option:nth-child(1) {
    animation-delay: 0.1s;
}

.fab-option:nth-child(2) {
    animation-delay: 0.2s;
}

.fab-option:nth-child(3) {
    animation-delay: 0.3s;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 480px) {
    .nav-item {
        min-width: 50px;
        padding: 6px 8px;
    }

    .nav-item i {
        font-size: 18px;
    }

    .nav-item span {
        font-size: 10px;
    }

    .fab {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .fab-option {
        width: 42px;
        height: 42px;
        font-size: 16px;
    }

    .main-content {
        padding: 15px;
        padding-bottom: 85px;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fabSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.fab-option.animate {
    animation: fabSlideIn 0.3s ease forwards;
}

/* ===== PAGE CONTENT STYLES ===== */
.projects-grid,
.media-grid,
.services-list {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.media-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.project-card,
.media-item,
.service-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.project-card:hover,
.media-item:hover,
.service-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.media-placeholder {
    width: 100%;
    height: 120px;
    background: #f8f9fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 24px;
    margin-bottom: 10px;
}

.status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status.ongoing {
    background: #fff3cd;
    color: #856404;
}

.status.active {
    background: #d4edda;
    color: #155724;
}

/* ===== ACCESSIBILITY ===== */
.nav-item:focus,
.fab:focus,
.fab-option:focus {
    outline: 2px solid #e74c3c;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .bottom-navigation {
        border-top: 2px solid #000;
    }

    .nav-item.active {
        background: #000;
        color: #fff;
    }

    .fab {
        background: #000;
        border: 2px solid #fff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

    .nav-item,
    .fab,
    .fab-option,
    .fab-menu {
        transition: none;
    }

    .fab.active {
        transform: none;
    }
}