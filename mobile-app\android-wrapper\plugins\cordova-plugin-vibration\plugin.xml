<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

<plugin xmlns="http://apache.org/cordova/ns/plugins/1.0"
    xmlns:android="http://schemas.android.com/apk/res/android"
    id="cordova-plugin-vibration"
    version="3.1.1">

    <name>Vibration</name>
    <description>Cordova Vibration Plugin</description>
    <license>Apache 2.0</license>
    <keywords>cordova,vibration</keywords>
    <repo>https://git-wip-us.apache.org/repos/asf/cordova-plugin-vibration.git</repo>
    <issue>https://issues.apache.org/jira/browse/CB/component/12320639</issue>

    <!-- android -->
    <platform name="android">
        <config-file target="AndroidManifest.xml" parent="/manifest">
            <uses-permission android:name="android.permission.VIBRATE"/>
        </config-file>
    </platform>

    <!-- ios -->
    <platform name="ios">
        <config-file target="config.xml" parent="/*">
            <feature name="Vibration">
                <param name="ios-package" value="CDVVibration"/>
            </feature>
        </config-file>
        <header-file src="src/ios/CDVVibration.h" />
	    <source-file src="src/ios/CDVVibration.m" />
        <js-module src="www/vibration.js" name="notification">
            <merges target="navigator" />
        </js-module>

		<framework src="AudioToolbox.framework" weak="true" />
    </platform>

    <!-- windows -->
    <platform name="windows">
        <js-module src="src/windows/VibrationProxy.js" name="VibrationProxy">
            <runs />
        </js-module>
        <js-module src="www/vibration.js" name="notification">
            <merges target="navigator" />
        </js-module>

        <framework src="src/windows/Vibration/Vibration.csproj" target="phone"
            type="projectReference" custom="true" versions="&lt;10.0.0" />
    </platform>

    <!-- browser -->
    <platform name="browser">
        <js-module src="src/browser/Vibration.js" name="Vibration">
            <merges target="navigator" />
        </js-module>
        <js-module src="www/vibration.js" name="notification">
            <merges target="navigator" />
        </js-module>
    </platform>

</plugin>
