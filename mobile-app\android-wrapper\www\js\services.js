/**
 * Services Manager for Flori Construction Admin Mobile App
 * Handles services CRUD operations and UI management
 */

class ServicesManager {
    constructor() {
        // Use the same API base as FloriAdmin
        this.apiBase = window.floriAdmin?.apiBase || this.getApiBaseUrl();
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.currentFilter = '';
        this.currentSearch = '';
        this.currentSort = 'created_desc';
        this.currentStatus = '';
        this.currentView = 'grid';
        this.services = [];
        this.selectedServices = new Set();
        this.isInitialized = false;

        this.init();
    }

    getApiBaseUrl() {
        // Get current location and construct API URL
        const currentLocation = window.location;
        const baseUrl = `${currentLocation.protocol}//${currentLocation.host}`;

        // Check if we're in mobile-app directory
        if (currentLocation.pathname.includes('/mobile-app/')) {
            return `${baseUrl}${currentLocation.pathname.replace('/mobile-app/', '/').replace(/\/[^\/]*$/, '')}/api`;
        }

        // Default fallback
        return `${baseUrl}/api`;
    }

    init() {
        console.log('ServicesManager: Initializing...');
        this.bindEvents();
        this.isInitialized = true;
        console.log('ServicesManager: Initialized successfully');
    }

    bindEvents() {
        // Add service button
        const addServiceBtn = document.getElementById('add-service-btn');
        if (addServiceBtn) {
            addServiceBtn.addEventListener('click', () => this.showAddServiceModal());
        }

        // Templates button
        const templatesBtn = document.getElementById('service-templates-btn');
        if (templatesBtn) {
            templatesBtn.addEventListener('click', () => this.showTemplatesModal());
        }

        // Export button
        const exportBtn = document.getElementById('export-services-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportServices());
        }

        // Service filter
        const serviceFilter = document.getElementById('service-filter');
        if (serviceFilter) {
            serviceFilter.addEventListener('change', (e) => {
                this.currentFilter = e.target.value;
                this.currentPage = 1;
                this.load();
            });
        }

        // Status filter
        const statusFilter = document.getElementById('service-status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.currentStatus = e.target.value;
                this.currentPage = 1;
                this.load();
            });
        }

        // Sort
        const sortSelect = document.getElementById('service-sort');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.currentPage = 1;
                this.load();
            });
        }

        // View toggle
        const viewBtns = document.querySelectorAll('.view-btn');
        viewBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                viewBtns.forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentView = e.target.dataset.view;
                this.renderServices();
            });
        });

        // Service search
        const serviceSearch = document.getElementById('service-search');
        if (serviceSearch) {
            let searchTimeout;
            serviceSearch.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.currentSearch = e.target.value;
                    this.currentPage = 1;
                    this.load();

                    // Show/hide clear button
                    const clearBtn = document.getElementById('clear-search');
                    if (clearBtn) {
                        clearBtn.style.display = e.target.value ? 'block' : 'none';
                    }
                }, 300);
            });
        }

        // Clear search
        const clearSearchBtn = document.getElementById('clear-search');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', () => {
                const searchInput = document.getElementById('service-search');
                if (searchInput) {
                    searchInput.value = '';
                    this.currentSearch = '';
                    this.currentPage = 1;
                    this.load();
                    clearSearchBtn.style.display = 'none';
                }
            });
        }

        // Bulk selection events
        this.bindBulkEvents();
    }

    async load() {
        try {
            console.log('ServicesManager: Loading services...');

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage,
                sort: this.currentSort
            });

            if (this.currentFilter) {
                if (this.currentFilter === 'featured') {
                    params.append('featured', 'true');
                }
            }

            if (this.currentStatus) {
                params.append('status', this.currentStatus);
            }

            if (this.currentSearch) {
                params.append('search', this.currentSearch);
            }

            const response = await window.floriAdmin.apiRequest(`services.php?${params}`);

            if (response && response.success) {
                this.services = response.services;
                this.renderServices();
                this.renderPagination(response.pagination);
                this.updateServicesStats();
                console.log('ServicesManager: Services loaded successfully');
            } else {
                throw new Error(response?.error || 'Failed to load services');
            }

        } catch (error) {
            console.error('Failed to load services:', error);
            window.floriAdmin.showToast('Failed to load services', 'error');
        }
    }

    renderServices() {
        const container = document.getElementById('services-list');
        if (!container) return;

        // Update container class based on view
        container.className = this.currentView === 'list' ? 'services-list' : 'services-grid';

        if (!this.services || this.services.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-tools fa-3x"></i>
                    <h3>No services found</h3>
                    <p>No services match your current filters.</p>
                    <button class="btn btn-primary" onclick="window.ServicesManager.showAddServiceModal()">
                        <i class="fas fa-plus"></i> Add First Service
                    </button>
                </div>
            `;
            return;
        }

        const servicesHTML = this.services.map(service => {
            return this.currentView === 'list' ?
                this.renderServiceListItem(service) :
                this.renderServiceCard(service);
        }).join('');

        container.innerHTML = servicesHTML;

        // Update selection states
        this.updateAllServiceCardsSelection();
    }

    renderServiceCard(service) {
        const imageUrl = service.featured_image_url || '../assets/images/placeholder-service.jpg';
        const featuredBadge = service.is_featured ? '<span class="featured-badge"><i class="fas fa-star"></i> Featured</span>' : '';
        const statusClass = service.status || 'active';
        const statusLabel = (service.status || 'active').charAt(0).toUpperCase() + (service.status || 'active').slice(1);
        const tags = service.tags ? service.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

        return `
            <div class="service-card ${this.selectedServices.has(service.id) ? 'selected' : ''}" data-service-id="${service.id}">
                <div class="service-checkbox">
                    <input type="checkbox" ${this.selectedServices.has(service.id) ? 'checked' : ''}
                           onchange="window.ServicesManager.toggleServiceSelection(${service.id})">
                </div>
                <div class="service-image">
                    <img src="${imageUrl}" alt="${this.escapeHtml(service.title)}" loading="lazy"
                         onerror="this.src='../assets/images/placeholder-service.jpg'">
                    ${featuredBadge}
                    <span class="service-status ${statusClass}">${statusLabel}</span>
                </div>
                <div class="service-content">
                    <h3 class="service-title">${this.escapeHtml(service.title)}</h3>
                    <p class="service-description">${this.escapeHtml(this.truncateText(service.short_description, 100))}</p>
                    ${tags.length > 0 ? `
                        <div class="service-tags">
                            ${tags.slice(0, 3).map(tag => `<span class="service-tag">${this.escapeHtml(tag)}</span>`).join('')}
                            ${tags.length > 3 ? `<span class="service-tag">+${tags.length - 3} more</span>` : ''}
                        </div>
                    ` : ''}
                    <div class="service-meta">
                        <span class="service-date">
                            <i class="fas fa-calendar"></i>
                            ${window.floriAdmin.formatDate(service.created_at)}
                        </span>
                        <span class="service-order">
                            <i class="fas fa-sort-numeric-down"></i>
                            Order: ${service.sort_order}
                        </span>
                    </div>
                </div>
                <div class="service-actions">
                    <button class="btn btn-sm btn-outline" onclick="window.ServicesManager.editService(${service.id})" title="Edit Service">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="window.ServicesManager.viewService('${service.slug}')" title="View Service">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="window.ServicesManager.deleteService(${service.id}, '${this.escapeHtml(service.title)}')" title="Delete Service">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;
    }

    renderServiceListItem(service) {
        const imageUrl = service.featured_image_url || '../assets/images/placeholder-service.jpg';
        const featuredBadge = service.is_featured ? '<i class="fas fa-star" style="color: #f39c12;"></i>' : '';
        const statusClass = service.status || 'active';
        const statusLabel = (service.status || 'active').charAt(0).toUpperCase() + (service.status || 'active').slice(1);
        const tags = service.tags ? service.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

        return `
            <div class="service-list-item ${this.selectedServices.has(service.id) ? 'selected' : ''}" data-service-id="${service.id}">
                <div class="service-checkbox">
                    <input type="checkbox" ${this.selectedServices.has(service.id) ? 'checked' : ''}
                           onchange="window.ServicesManager.toggleServiceSelection(${service.id})">
                </div>
                <div class="service-list-image">
                    <img src="${imageUrl}" alt="${this.escapeHtml(service.title)}" loading="lazy"
                         onerror="this.src='../assets/images/placeholder-service.jpg'">
                </div>
                <div class="service-list-content">
                    <div class="service-header">
                        <h3 class="service-title">
                            ${this.escapeHtml(service.title)} ${featuredBadge}
                            <span class="service-status ${statusClass}">${statusLabel}</span>
                        </h3>
                    </div>
                    <p class="service-description">${this.escapeHtml(this.truncateText(service.short_description, 150))}</p>
                    ${tags.length > 0 ? `
                        <div class="service-tags">
                            ${tags.slice(0, 5).map(tag => `<span class="service-tag">${this.escapeHtml(tag)}</span>`).join('')}
                            ${tags.length > 5 ? `<span class="service-tag">+${tags.length - 5} more</span>` : ''}
                        </div>
                    ` : ''}
                    <div class="service-meta">
                        <span><i class="fas fa-calendar"></i> ${window.floriAdmin.formatDate(service.created_at)}</span>
                        <span><i class="fas fa-sort-numeric-down"></i> Order: ${service.sort_order}</span>
                    </div>
                </div>
                <div class="service-list-actions">
                    <button class="btn btn-sm btn-outline" onclick="window.ServicesManager.editService(${service.id})" title="Edit Service">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="window.ServicesManager.viewService('${service.slug}')" title="View Service">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="window.ServicesManager.deleteService(${service.id}, '${this.escapeHtml(service.title)}')" title="Delete Service">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    renderPagination(pagination) {
        const container = document.getElementById('services-pagination');
        if (!container || !pagination) return;

        if (pagination.pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination-controls">';

        // Previous button
        if (pagination.page > 1) {
            paginationHTML += `
                <button class="btn btn-outline" onclick="window.ServicesManager.goToPage(${pagination.page - 1})">
                    <i class="fas fa-chevron-left"></i> Previous
                </button>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === pagination.page ? 'btn-primary' : 'btn-outline';
            paginationHTML += `
                <button class="btn ${activeClass}" onclick="window.ServicesManager.goToPage(${i})">
                    ${i}
                </button>
            `;
        }

        // Next button
        if (pagination.page < pagination.pages) {
            paginationHTML += `
                <button class="btn btn-outline" onclick="window.ServicesManager.goToPage(${pagination.page + 1})">
                    Next <i class="fas fa-chevron-right"></i>
                </button>
            `;
        }

        paginationHTML += '</div>';
        container.innerHTML = paginationHTML;
    }

    goToPage(page) {
        this.currentPage = page;
        this.load();
    }

    async showAddServiceModal() {
        const modalContent = `
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> Add New Service</h3>
                <button class="modal-close" onclick="window.floriAdmin.hideModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="add-service-form" class="service-form">
                    <div class="form-group">
                        <label for="service-title">Service Title *</label>
                        <input type="text" id="service-title" name="title" required>
                    </div>

                    <div class="form-group">
                        <label for="service-short-description">Short Description *</label>
                        <textarea id="service-short-description" name="short_description" rows="3" maxlength="200" required></textarea>
                        <small class="char-count">0/200 characters</small>
                    </div>

                    <div class="form-group">
                        <label for="service-description">Full Description *</label>
                        <textarea id="service-description" name="description" rows="6" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="service-image">Featured Image</label>
                        <div class="image-upload-area" id="service-image-upload">
                            <div class="upload-placeholder">
                                <i class="fas fa-image fa-2x"></i>
                                <p>Click to upload image or drag & drop</p>
                                <small>JPG, PNG, WebP (Max: 5MB)</small>
                            </div>
                            <input type="file" id="service-image" name="featured_image" accept="image/*" style="display: none;">
                            <div class="image-preview" style="display: none;">
                                <img src="" alt="Preview">
                                <button type="button" class="remove-image" onclick="window.ServicesManager.removeImage()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="service-sort-order">Sort Order</label>
                            <input type="number" id="service-sort-order" name="sort_order" value="0" min="0">
                        </div>

                        <div class="form-group">
                            <label for="service-status">Status</label>
                            <select id="service-status" name="status">
                                <option value="active">Active</option>
                                <option value="draft">Draft</option>
                                <option value="archived">Archived</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="service-featured" name="is_featured">
                            <span class="checkmark"></span>
                            Featured Service
                        </label>
                    </div>

                    <div class="form-group">
                        <label for="service-tags">Tags (comma-separated)</label>
                        <input type="text" id="service-tags" name="tags" placeholder="e.g., construction, renovation, commercial">
                        <small>Add relevant tags to help categorize this service</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="window.floriAdmin.hideModal()">Cancel</button>
                <button class="btn btn-success" onclick="window.ServicesManager.saveService()">
                    <i class="fas fa-save"></i> Save Service
                </button>
                <button class="btn btn-primary" onclick="window.ServicesManager.saveAndAddAnother()">
                    <i class="fas fa-plus"></i> Save & Add Another
                </button>
            </div>
        `;

        window.floriAdmin.showModal(modalContent);
        this.initServiceForm();
        this.initImageUpload();
    }

    initServiceForm() {
        // Character counter for short description
        const shortDesc = document.getElementById('service-short-description');
        const charCount = document.querySelector('.char-count');

        if (shortDesc && charCount) {
            shortDesc.addEventListener('input', () => {
                const length = shortDesc.value.length;
                charCount.textContent = `${length}/200 characters`;
                charCount.className = length > 200 ? 'char-count over-limit' : 'char-count';
            });
        }
    }

    initImageUpload() {
        const uploadArea = document.getElementById('service-image-upload');
        const fileInput = document.getElementById('service-image');

        if (!uploadArea || !fileInput) {
            console.warn('Image upload elements not found');
            return;
        }

        const placeholder = uploadArea.querySelector('.upload-placeholder');
        const preview = uploadArea.querySelector('.image-preview');

        if (!placeholder || !preview) {
            console.warn('Image upload placeholder or preview not found');
            return;
        }

        // Remove existing event listeners to prevent duplicates
        this.cleanupImageUploadEvents();

        // Store references for cleanup
        this.imageUploadElements = {
            uploadArea,
            fileInput,
            placeholder,
            preview
        };

        // Click to upload
        this.handlePlaceholderClick = () => {
            fileInput.click();
        };
        placeholder.addEventListener('click', this.handlePlaceholderClick);

        // Improved drag and drop with proper event handling
        this.handleDragOver = (e) => {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.classList.add('drag-over');
        };

        this.handleDragLeave = (e) => {
            e.preventDefault();
            e.stopPropagation();
            // Only remove drag-over if we're leaving the upload area itself
            if (!uploadArea.contains(e.relatedTarget)) {
                uploadArea.classList.remove('drag-over');
            }
        };

        this.handleDrop = (e) => {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleImageFile(files[0]);
            }
        };

        uploadArea.addEventListener('dragover', this.handleDragOver);
        uploadArea.addEventListener('dragleave', this.handleDragLeave);
        uploadArea.addEventListener('drop', this.handleDrop);

        // File input change
        this.handleFileInputChange = (e) => {
            if (e.target.files.length > 0) {
                this.handleImageFile(e.target.files[0]);
            }
        };
        fileInput.addEventListener('change', this.handleFileInputChange);
    }

    cleanupImageUploadEvents() {
        if (this.imageUploadElements) {
            const { uploadArea, fileInput, placeholder } = this.imageUploadElements;

            if (placeholder && this.handlePlaceholderClick) {
                placeholder.removeEventListener('click', this.handlePlaceholderClick);
            }

            if (uploadArea) {
                if (this.handleDragOver) uploadArea.removeEventListener('dragover', this.handleDragOver);
                if (this.handleDragLeave) uploadArea.removeEventListener('dragleave', this.handleDragLeave);
                if (this.handleDrop) uploadArea.removeEventListener('drop', this.handleDrop);
            }

            if (fileInput && this.handleFileInputChange) {
                fileInput.removeEventListener('change', this.handleFileInputChange);
            }
        }
    }

    handleImageFile(file) {
        if (!file) {
            window.floriAdmin.showToast('No file selected', 'error');
            return;
        }

        // Enhanced file type validation
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/svg+xml'];
        if (!allowedTypes.includes(file.type.toLowerCase())) {
            window.floriAdmin.showToast('Please select a valid image file (JPG, PNG, WebP, GIF, SVG)', 'error');
            return;
        }

        // Validate file size (5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
            const sizeMB = (file.size / (1024 * 1024)).toFixed(2);
            window.floriAdmin.showToast(`Image size (${sizeMB}MB) exceeds the 5MB limit`, 'error');
            return;
        }

        const uploadArea = document.getElementById('service-image-upload');
        if (!uploadArea) {
            window.floriAdmin.showToast('Upload area not found', 'error');
            return;
        }

        const placeholder = uploadArea.querySelector('.upload-placeholder');
        const preview = uploadArea.querySelector('.image-preview');
        const img = preview?.querySelector('img');

        if (!placeholder || !preview || !img) {
            window.floriAdmin.showToast('Upload interface elements not found', 'error');
            return;
        }

        // Show loading state
        placeholder.innerHTML = `
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p>Processing image...</p>
            <small>Please wait</small>
        `;

        // Create preview with error handling
        const reader = new FileReader();

        reader.onload = (e) => {
            try {
                // Validate image can be loaded
                const tempImg = new Image();
                tempImg.onload = () => {
                    // Check image dimensions (optional - can be removed if not needed)
                    const maxDimension = 4000; // 4000px max width/height
                    if (tempImg.width > maxDimension || tempImg.height > maxDimension) {
                        window.floriAdmin.showToast(`Image dimensions (${tempImg.width}x${tempImg.height}) are too large. Maximum: ${maxDimension}px`, 'warning');
                    }

                    // Set preview image
                    img.src = e.target.result;
                    img.alt = file.name;
                    placeholder.style.display = 'none';
                    preview.style.display = 'block';

                    // Store file for upload
                    this.selectedImage = file;

                    window.floriAdmin.showToast('Image loaded successfully', 'success');
                };

                tempImg.onerror = () => {
                    this.resetImageUpload();
                    window.floriAdmin.showToast('Invalid image file or corrupted data', 'error');
                };

                tempImg.src = e.target.result;

            } catch (error) {
                console.error('Error processing image:', error);
                this.resetImageUpload();
                window.floriAdmin.showToast('Error processing image file', 'error');
            }
        };

        reader.onerror = () => {
            this.resetImageUpload();
            window.floriAdmin.showToast('Error reading image file', 'error');
        };

        reader.readAsDataURL(file);
    }

    resetImageUpload() {
        const uploadArea = document.getElementById('service-image-upload');
        if (!uploadArea) return;

        const placeholder = uploadArea.querySelector('.upload-placeholder');
        const preview = uploadArea.querySelector('.image-preview');
        const fileInput = document.getElementById('service-image');

        if (placeholder) {
            placeholder.innerHTML = `
                <i class="fas fa-image fa-2x"></i>
                <p>Click to upload image or drag & drop</p>
                <small>JPG, PNG, WebP (Max: 5MB)</small>
            `;
            placeholder.style.display = 'block';
        }

        if (preview) {
            preview.style.display = 'none';
        }

        if (fileInput) {
            fileInput.value = '';
        }

        this.selectedImage = null;
    }

    removeImage() {
        const uploadArea = document.getElementById('service-image-upload');
        if (!uploadArea) {
            console.warn('Upload area not found');
            return;
        }

        const placeholder = uploadArea.querySelector('.upload-placeholder');
        const preview = uploadArea.querySelector('.image-preview');
        const fileInput = document.getElementById('service-image');

        if (placeholder) {
            // Reset placeholder content
            placeholder.innerHTML = `
                <i class="fas fa-image fa-2x"></i>
                <p>Click to upload image or drag & drop</p>
                <small>JPG, PNG, WebP (Max: 5MB)</small>
            `;
            placeholder.style.display = 'block';
        }

        if (preview) {
            preview.style.display = 'none';
            // Clear the image src to free memory
            const img = preview.querySelector('img');
            if (img) {
                img.src = '';
                img.alt = '';
            }
        }

        if (fileInput) {
            fileInput.value = '';
        }

        // Clear selected image
        this.selectedImage = null;

        window.floriAdmin.showToast('Image removed', 'info');
    }

    showImageOptions() {
        // Show options to change or browse existing images
        const modalContent = `
            <div class="modal-header">
                <h3><i class="fas fa-images"></i> Change Service Image</h3>
                <button class="modal-close" onclick="window.floriAdmin.hideModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="image-options">
                    <button class="btn btn-primary btn-block" onclick="window.ServicesManager.uploadNewImage()">
                        <i class="fas fa-upload"></i> Upload New Image
                    </button>
                    <button class="btn btn-outline btn-block" onclick="window.ServicesManager.browseExistingImages()">
                        <i class="fas fa-folder-open"></i> Browse Existing Images
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="window.floriAdmin.hideModal()">Cancel</button>
            </div>
        `;

        window.floriAdmin.showModal(modalContent);
    }

    uploadNewImage() {
        window.floriAdmin.hideModal();
        // Trigger file input
        setTimeout(() => {
            const fileInput = document.getElementById('service-image');
            if (fileInput) {
                fileInput.click();
            }
        }, 100);
    }

    async browseExistingImages() {
        try {
            // Load existing images from media API
            const response = await window.floriAdmin.apiRequest('media.php?type=image&limit=20');

            if (!response || !response.success) {
                throw new Error('Failed to load images');
            }

            const images = response.media || [];

            if (images.length === 0) {
                window.floriAdmin.hideModal();
                window.floriAdmin.showToast('No images found. Upload some images first.', 'info');
                return;
            }

            const modalContent = `
                <div class="modal-header">
                    <h3><i class="fas fa-images"></i> Select Existing Image</h3>
                    <button class="modal-close" onclick="window.floriAdmin.hideModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="media-browser-grid">
                        ${images.map(image => `
                            <div class="media-browser-item" onclick="window.ServicesManager.selectExistingImage('${image.file_path}', '${image.url}')">
                                <img src="${image.thumbnail_url || image.url}" alt="${this.escapeHtml(image.original_name)}" loading="lazy">
                                <div class="media-item-info">
                                    <span class="media-name">${this.escapeHtml(image.original_name)}</span>
                                    <span class="media-size">${window.floriAdmin.formatFileSize(image.file_size)}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="window.floriAdmin.hideModal()">Cancel</button>
                </div>
            `;

            window.floriAdmin.showModal(modalContent);

        } catch (error) {
            console.error('Failed to browse images:', error);
            window.floriAdmin.hideModal();
            window.floriAdmin.showToast('Failed to load images: ' + error.message, 'error');
        }
    }

    selectExistingImage(filePath, imageUrl) {
        // Set the selected image path for the service
        this.selectedImagePath = filePath;

        // Update the preview
        const uploadArea = document.getElementById('service-image-upload');
        if (uploadArea) {
            const currentImagePreview = uploadArea.querySelector('.current-image-preview');
            const placeholder = uploadArea.querySelector('.upload-placeholder');
            const preview = uploadArea.querySelector('.image-preview');
            const img = preview?.querySelector('img');

            if (currentImagePreview) {
                currentImagePreview.style.display = 'none';
            }

            if (placeholder) {
                placeholder.style.display = 'none';
            }

            if (preview && img) {
                img.src = imageUrl;
                img.alt = 'Selected image';
                preview.style.display = 'block';
            }
        }

        window.floriAdmin.hideModal();
        window.floriAdmin.showToast('Image selected successfully', 'success');
    }

    removeCurrentImage() {
        if (!confirm('Are you sure you want to remove the current image?')) {
            return;
        }

        const uploadArea = document.getElementById('service-image-upload');
        if (!uploadArea) return;

        const currentImagePreview = uploadArea.querySelector('.current-image-preview');
        const placeholder = uploadArea.querySelector('.upload-placeholder');

        if (currentImagePreview) {
            currentImagePreview.style.display = 'none';
        }

        if (placeholder) {
            placeholder.style.display = 'block';
        }

        // Mark image for removal
        this.removeCurrentImageFlag = true;

        window.floriAdmin.showToast('Current image will be removed when you save', 'info');
    }

    async saveService(serviceId = null) {
        const form = document.getElementById('add-service-form') || document.getElementById('edit-service-form');
        if (!form) return;

        const formData = new FormData(form);
        const serviceData = {
            title: formData.get('title'),
            short_description: formData.get('short_description'),
            description: formData.get('description'),
            sort_order: parseInt(formData.get('sort_order')) || 0,
            is_featured: formData.get('is_featured') === 'on',
            status: formData.get('status') || 'active',
            tags: formData.get('tags') || ''
        };

        // Validation
        if (!serviceData.title || !serviceData.short_description || !serviceData.description) {
            window.floriAdmin.showToast('Please fill in all required fields', 'error');
            return;
        }

        try {
            // Handle image operations
            if (this.selectedImage) {
                // Upload new image
                const imageData = await this.uploadServiceImage(this.selectedImage);
                if (imageData && imageData.success) {
                    serviceData.featured_image = imageData.media.file_path;
                } else {
                    throw new Error('Failed to upload image');
                }
            } else if (this.selectedImagePath) {
                // Use existing image
                serviceData.featured_image = this.selectedImagePath;
            } else if (this.removeCurrentImageFlag && serviceId) {
                // Remove current image
                serviceData.featured_image = null;
            }

            let response;
            if (serviceId) {
                serviceData.id = serviceId;
                response = await window.floriAdmin.apiRequest('services.php', 'PUT', serviceData);
            } else {
                response = await window.floriAdmin.apiRequest('services.php', 'POST', serviceData);
            }

            if (response && response.success) {
                window.floriAdmin.showToast(serviceId ? 'Service updated successfully' : 'Service created successfully', 'success');
                window.floriAdmin.hideModal();
                this.load();

                // Clean up
                this.selectedImage = null;
                this.selectedImagePath = null;
                this.removeCurrentImageFlag = false;
                this.currentEditingService = null;
                this.cleanupImageUploadEvents();
            } else {
                throw new Error(response?.error || 'Failed to save service');
            }

        } catch (error) {
            console.error('Failed to save service:', error);
            window.floriAdmin.showToast('Failed to save service: ' + error.message, 'error');
        }
    }

    async saveAndAddAnother() {
        await this.saveService();
        // If save was successful, show add modal again
        setTimeout(() => {
            this.showAddServiceModal();
        }, 500);
    }

    async uploadServiceImage(file, retryCount = 0) {
        const maxRetries = 2;

        if (!file) {
            throw new Error('No file provided for upload');
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('directory', 'services');
        formData.append('alt_text', file.name || '');
        formData.append('caption', '');

        try {
            // Show upload progress
            if (retryCount === 0) {
                window.floriAdmin.showToast('Uploading image...', 'info');
            }

            const response = await fetch(`${this.apiBase}/media.php`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${window.floriAdmin.token}`
                },
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Upload failed');
            }

            window.floriAdmin.showToast('Image uploaded successfully', 'success');
            return result;

        } catch (error) {
            console.error('Image upload failed:', error);

            // Retry logic
            if (retryCount < maxRetries) {
                console.log(`Retrying upload (attempt ${retryCount + 1}/${maxRetries})`);
                window.floriAdmin.showToast(`Upload failed, retrying... (${retryCount + 1}/${maxRetries})`, 'warning');

                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));

                return this.uploadServiceImage(file, retryCount + 1);
            }

            // Final failure
            const errorMessage = error.message || 'Unknown error occurred';
            window.floriAdmin.showToast(`Failed to upload image: ${errorMessage}`, 'error');
            throw error;
        }
    }

    async editService(serviceId) {
        try {
            // Get service details
            const response = await window.floriAdmin.apiRequest(`services.php?id=${serviceId}`);

            if (!response || !response.success || !response.services || response.services.length === 0) {
                throw new Error('Service not found');
            }

            const service = response.services[0];

            const modalContent = `
                <div class="modal-header">
                    <h3><i class="fas fa-edit"></i> Edit Service</h3>
                    <button class="modal-close" onclick="window.floriAdmin.hideModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="edit-service-form" class="service-form">
                        <div class="form-group">
                            <label for="edit-service-title">Service Title *</label>
                            <input type="text" id="edit-service-title" name="title" value="${this.escapeHtml(service.title)}" required>
                        </div>

                        <div class="form-group">
                            <label for="edit-service-short-description">Short Description *</label>
                            <textarea id="edit-service-short-description" name="short_description" rows="3" maxlength="200" required>${this.escapeHtml(service.short_description)}</textarea>
                            <small class="char-count">${service.short_description.length}/200 characters</small>
                        </div>

                        <div class="form-group">
                            <label for="edit-service-description">Full Description *</label>
                            <textarea id="edit-service-description" name="description" rows="6" required>${this.escapeHtml(service.description)}</textarea>
                        </div>

                        <div class="form-group">
                            <label for="edit-service-image">Featured Image</label>
                            <div class="image-upload-area" id="service-image-upload">
                                ${service.featured_image_url ? `
                                    <div class="current-image-preview">
                                        <img src="${service.featured_image_url}" alt="Current image" style="max-width: 200px; max-height: 150px; border-radius: 8px; object-fit: cover;">
                                        <button type="button" class="btn btn-sm btn-outline" onclick="window.ServicesManager.showImageOptions()">
                                            <i class="fas fa-edit"></i> Change Image
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="window.ServicesManager.removeCurrentImage()">
                                            <i class="fas fa-trash"></i> Remove
                                        </button>
                                    </div>
                                ` : ''}
                                <div class="upload-placeholder" style="${service.featured_image_url ? 'display: none;' : ''}">
                                    <i class="fas fa-image fa-2x"></i>
                                    <p>Click to upload image or drag & drop</p>
                                    <small>JPG, PNG, WebP (Max: 5MB)</small>
                                </div>
                                <input type="file" id="service-image" name="featured_image" accept="image/*" style="display: none;">
                                <div class="image-preview" style="display: none;">
                                    <img src="" alt="Preview">
                                    <button type="button" class="remove-image" onclick="window.ServicesManager.removeImage()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit-service-sort-order">Sort Order</label>
                                <input type="number" id="edit-service-sort-order" name="sort_order" value="${service.sort_order}" min="0">
                            </div>

                            <div class="form-group">
                                <label for="edit-service-status">Status</label>
                                <select id="edit-service-status" name="status">
                                    <option value="active" ${service.status === 'active' ? 'selected' : ''}>Active</option>
                                    <option value="draft" ${service.status === 'draft' ? 'selected' : ''}>Draft</option>
                                    <option value="archived" ${service.status === 'archived' ? 'selected' : ''}>Archived</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="edit-service-featured" name="is_featured" ${service.is_featured ? 'checked' : ''}>
                                <span class="checkmark"></span>
                                Featured Service
                            </label>
                        </div>

                        <div class="form-group">
                            <label for="edit-service-tags">Tags (comma-separated)</label>
                            <input type="text" id="edit-service-tags" name="tags" value="${this.escapeHtml(service.tags || '')}" placeholder="e.g., construction, renovation, commercial">
                            <small>Add relevant tags to help categorize this service</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="window.floriAdmin.hideModal()">Cancel</button>
                    <button class="btn btn-primary" onclick="window.ServicesManager.saveService(${serviceId})">
                        <i class="fas fa-save"></i> Update Service
                    </button>
                </div>
            `;

            window.floriAdmin.showModal(modalContent);
            this.initServiceForm();
            this.initImageUpload();

            // Store current service data for image management
            this.currentEditingService = service;

        } catch (error) {
            console.error('Failed to load service for editing:', error);
            window.floriAdmin.showToast('Failed to load service details', 'error');
        }
    }

    viewService(slug) {
        // Open service page in new tab
        const serviceUrl = `../service.php?slug=${slug}`;
        window.open(serviceUrl, '_blank');
    }

    async deleteService(serviceId, serviceName) {
        if (!confirm(`Are you sure you want to delete the service "${serviceName}"? This action cannot be undone.`)) {
            return;
        }

        try {
            const response = await window.floriAdmin.apiRequest(`services.php?id=${serviceId}`, 'DELETE');

            if (response && response.success) {
                window.floriAdmin.showToast('Service deleted successfully', 'success');
                this.load();
            } else {
                throw new Error(response?.error || 'Failed to delete service');
            }

        } catch (error) {
            console.error('Failed to delete service:', error);
            window.floriAdmin.showToast('Failed to delete service: ' + error.message, 'error');
        }
    }

    // Helper methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
}

// Initialize services manager when DOM is ready
function initializeServicesManager() {
    if (!window.ServicesManager) {
        console.log('ServicesManager: Creating new instance');
        window.ServicesManager = new ServicesManager();
    } else {
        console.log('ServicesManager: Instance already exists');
    }
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', initializeServicesManager);

// Also initialize when floriAdmin is ready (for cases where scripts load out of order)
if (window.floriAdmin) {
    console.log('ServicesManager: floriAdmin already available');
    initializeServicesManager();
} else {
    // Wait for floriAdmin to be available
    let attempts = 0;
    const maxAttempts = 20;

    const waitForFloriAdmin = () => {
        attempts++;
        if (window.floriAdmin) {
            console.log('ServicesManager: floriAdmin now available');
            initializeServicesManager();
        } else if (attempts < maxAttempts) {
            setTimeout(waitForFloriAdmin, 100);
        } else {
            console.warn('ServicesManager: floriAdmin not available after waiting');
        }
    };

    setTimeout(waitForFloriAdmin, 100);
}

// Add enhanced methods to ServicesManager prototype
ServicesManager.prototype.bindBulkEvents = function () {
    // Bulk selection events
    const selectAllBtn = document.getElementById('select-all-btn');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', () => this.selectAllServices());
    }

    const clearSelectionBtn = document.getElementById('clear-selection-btn');
    if (clearSelectionBtn) {
        clearSelectionBtn.addEventListener('click', () => this.clearSelection());
    }

    // Bulk action buttons
    const bulkFeatureBtn = document.getElementById('bulk-feature-btn');
    if (bulkFeatureBtn) {
        bulkFeatureBtn.addEventListener('click', () => this.bulkFeatureServices(true));
    }

    const bulkUnfeatureBtn = document.getElementById('bulk-unfeature-btn');
    if (bulkUnfeatureBtn) {
        bulkUnfeatureBtn.addEventListener('click', () => this.bulkFeatureServices(false));
    }

    const bulkArchiveBtn = document.getElementById('bulk-archive-btn');
    if (bulkArchiveBtn) {
        bulkArchiveBtn.addEventListener('click', () => this.bulkArchiveServices());
    }

    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', () => this.bulkDeleteServices());
    }
};

ServicesManager.prototype.updateServicesStats = function () {
    const servicesCount = document.getElementById('services-count');
    const featuredCount = document.getElementById('featured-count');

    if (servicesCount) {
        const total = this.services.length;
        servicesCount.textContent = `${total} service${total !== 1 ? 's' : ''}`;
    }

    if (featuredCount) {
        const featured = this.services.filter(s => s.is_featured).length;
        featuredCount.textContent = `${featured} featured`;
    }
};

ServicesManager.prototype.toggleServiceSelection = function (serviceId) {
    if (this.selectedServices.has(serviceId)) {
        this.selectedServices.delete(serviceId);
    } else {
        this.selectedServices.add(serviceId);
    }

    this.updateBulkSelectionUI();
    this.updateServiceCardSelection(serviceId);
};

ServicesManager.prototype.selectAllServices = function () {
    this.services.forEach(service => {
        this.selectedServices.add(service.id);
    });
    this.updateBulkSelectionUI();
    this.updateAllServiceCardsSelection();
};

ServicesManager.prototype.clearSelection = function () {
    this.selectedServices.clear();
    this.updateBulkSelectionUI();
    this.updateAllServiceCardsSelection();
};

ServicesManager.prototype.updateBulkSelectionUI = function () {
    const selectionBar = document.getElementById('bulk-selection-bar');
    const selectedCount = document.getElementById('selected-count');

    if (this.selectedServices.size > 0) {
        selectionBar.style.display = 'flex';
        selectedCount.textContent = `${this.selectedServices.size} selected`;
    } else {
        selectionBar.style.display = 'none';
    }
};

ServicesManager.prototype.updateServiceCardSelection = function (serviceId) {
    const card = document.querySelector(`[data-service-id="${serviceId}"]`);
    if (card) {
        const checkbox = card.querySelector('.service-checkbox input');
        if (checkbox) {
            checkbox.checked = this.selectedServices.has(serviceId);
        }

        if (this.selectedServices.has(serviceId)) {
            card.classList.add('selected');
        } else {
            card.classList.remove('selected');
        }
    }
};

ServicesManager.prototype.updateAllServiceCardsSelection = function () {
    this.services.forEach(service => {
        this.updateServiceCardSelection(service.id);
    });
};

ServicesManager.prototype.showTemplatesModal = function () {
    const templates = [
        {
            name: 'Residential Construction',
            title: 'Residential Construction Services',
            short_description: 'Complete residential construction solutions for your dream home.',
            description: 'We provide comprehensive residential construction services including new home construction, renovations, and custom builds. Our experienced team ensures quality craftsmanship and attention to detail.',
            tags: 'residential, construction, new homes, renovations'
        },
        {
            name: 'Commercial Construction',
            title: 'Commercial Construction Services',
            short_description: 'Professional commercial construction for businesses and organizations.',
            description: 'Our commercial construction services cover office buildings, retail spaces, warehouses, and industrial facilities. We deliver projects on time and within budget.',
            tags: 'commercial, office, retail, industrial'
        },
        {
            name: 'Renovation & Remodeling',
            title: 'Renovation & Remodeling',
            short_description: 'Transform your space with our expert renovation services.',
            description: 'From kitchen and bathroom remodels to complete home renovations, we help transform your space to meet your needs and style preferences.',
            tags: 'renovation, remodeling, kitchen, bathroom'
        }
    ];

    const modalContent = `
        <div class="modal-header">
            <h3><i class="fas fa-copy"></i> Service Templates</h3>
            <button class="modal-close" onclick="window.floriAdmin.hideModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <p>Choose a template to quickly create a new service:</p>
            <div class="templates-grid">
                ${templates.map(template => `
                    <div class="template-card" onclick="window.ServicesManager.useTemplate('${template.name}')">
                        <h4>${template.title}</h4>
                        <p>${template.short_description}</p>
                        <div class="template-tags">
                            ${template.tags.split(', ').map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="window.floriAdmin.hideModal()">Cancel</button>
        </div>
    `;

    window.floriAdmin.showModal(modalContent);
};

ServicesManager.prototype.useTemplate = function (templateName) {
    const templates = {
        'Residential Construction': {
            title: 'Residential Construction Services',
            short_description: 'Complete residential construction solutions for your dream home.',
            description: 'We provide comprehensive residential construction services including new home construction, renovations, and custom builds. Our experienced team ensures quality craftsmanship and attention to detail.',
            tags: 'residential, construction, new homes, renovations'
        },
        'Commercial Construction': {
            title: 'Commercial Construction Services',
            short_description: 'Professional commercial construction for businesses and organizations.',
            description: 'Our commercial construction services cover office buildings, retail spaces, warehouses, and industrial facilities. We deliver projects on time and within budget.',
            tags: 'commercial, office, retail, industrial'
        },
        'Renovation & Remodeling': {
            title: 'Renovation & Remodeling',
            short_description: 'Transform your space with our expert renovation services.',
            description: 'From kitchen and bathroom remodels to complete home renovations, we help transform your space to meet your needs and style preferences.',
            tags: 'renovation, remodeling, kitchen, bathroom'
        }
    };

    const template = templates[templateName];
    if (template) {
        window.floriAdmin.hideModal();
        setTimeout(() => {
            this.showAddServiceModal();
            // Pre-fill form with template data
            setTimeout(() => {
                document.getElementById('service-title').value = template.title;
                document.getElementById('service-short-description').value = template.short_description;
                document.getElementById('service-description').value = template.description;
                document.getElementById('service-tags').value = template.tags;

                // Update character count
                const event = new Event('input');
                document.getElementById('service-short-description').dispatchEvent(event);
            }, 100);
        }, 200);
    }
};

ServicesManager.prototype.bulkFeatureServices = async function (featured) {
    if (this.selectedServices.size === 0) return;

    const action = featured ? 'feature' : 'unfeature';
    const confirmMsg = `Are you sure you want to ${action} ${this.selectedServices.size} service(s)?`;

    if (!confirm(confirmMsg)) return;

    try {
        const promises = Array.from(this.selectedServices).map(serviceId => {
            return window.floriAdmin.apiRequest('services.php', 'PUT', {
                id: serviceId,
                is_featured: featured
            });
        });

        const results = await Promise.allSettled(promises);
        const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;

        if (successful > 0) {
            window.floriAdmin.showToast(`${successful} service(s) ${action}d successfully`, 'success');
            this.clearSelection();
            this.load();
        }
    } catch (error) {
        window.floriAdmin.showToast(`Failed to ${action} services`, 'error');
    }
};

ServicesManager.prototype.bulkArchiveServices = async function () {
    if (this.selectedServices.size === 0) return;

    const confirmMsg = `Are you sure you want to archive ${this.selectedServices.size} service(s)?`;
    if (!confirm(confirmMsg)) return;

    try {
        const promises = Array.from(this.selectedServices).map(serviceId => {
            return window.floriAdmin.apiRequest('services.php', 'PUT', {
                id: serviceId,
                status: 'archived'
            });
        });

        const results = await Promise.allSettled(promises);
        const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;

        if (successful > 0) {
            window.floriAdmin.showToast(`${successful} service(s) archived successfully`, 'success');
            this.clearSelection();
            this.load();
        }
    } catch (error) {
        window.floriAdmin.showToast('Failed to archive services', 'error');
    }
};

ServicesManager.prototype.bulkDeleteServices = async function () {
    if (this.selectedServices.size === 0) return;

    const confirmMsg = `Are you sure you want to delete ${this.selectedServices.size} service(s)? This action cannot be undone.`;
    if (!confirm(confirmMsg)) return;

    try {
        const promises = Array.from(this.selectedServices).map(serviceId => {
            return window.floriAdmin.apiRequest(`services.php?id=${serviceId}`, 'DELETE');
        });

        const results = await Promise.allSettled(promises);
        const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;

        if (successful > 0) {
            window.floriAdmin.showToast(`${successful} service(s) deleted successfully`, 'success');
            this.clearSelection();
            this.load();
        }
    } catch (error) {
        window.floriAdmin.showToast('Failed to delete services', 'error');
    }
};

ServicesManager.prototype.exportServices = async function () {
    try {
        // Get all services for export
        const response = await window.floriAdmin.apiRequest('services.php?limit=1000');

        if (response && response.success) {
            const services = response.services;

            // Create CSV content
            const headers = ['ID', 'Title', 'Short Description', 'Description', 'Featured', 'Status', 'Sort Order', 'Tags', 'Created At'];
            const csvContent = [
                headers.join(','),
                ...services.map(service => [
                    service.id,
                    `"${service.title.replace(/"/g, '""')}"`,
                    `"${service.short_description.replace(/"/g, '""')}"`,
                    `"${service.description.replace(/"/g, '""')}"`,
                    service.is_featured ? 'Yes' : 'No',
                    service.status || 'active',
                    service.sort_order,
                    `"${service.tags || ''}"`,
                    service.created_at
                ].join(','))
            ].join('\n');

            // Download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `services-export-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            window.floriAdmin.showToast('Services exported successfully', 'success');
        }
    } catch (error) {
        window.floriAdmin.showToast('Failed to export services', 'error');
    }
};
