/* ===== ADMIN LOGIN PAGE STYLES ===== */

/* CSS Variables */
:root {
    /* Colors */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;

    /* Grays */
    --white: #ffffff;
    --gray-50: #f8f9fa;
    --gray-100: #f1f3f4;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;

    /* Typography */
    --font-family-base: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --line-height-tight: 1.25;
    --line-height-base: 1.5;
    --line-height-relaxed: 1.625;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
    --spacing-xl: 1.5rem;
    --spacing-2xl: 2rem;
    --spacing-3xl: 3rem;

    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --border-radius-2xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-base);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--gray-800);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* Background Animation */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.construction-icons {
    position: absolute;
    width: 100%;
    height: 100%;
}

.construction-icons i {
    position: absolute;
    font-size: var(--font-size-3xl);
    color: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.construction-icons i:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.construction-icons i:nth-child(2) {
    top: 60%;
    left: 20%;
    animation-delay: 1s;
}

.construction-icons i:nth-child(3) {
    top: 30%;
    right: 15%;
    animation-delay: 2s;
}

.construction-icons i:nth-child(4) {
    bottom: 30%;
    right: 25%;
    animation-delay: 3s;
}

.construction-icons i:nth-child(5) {
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

.construction-icons i:nth-child(6) {
    top: 10%;
    right: 30%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.2;
    }
}

/* Login Container */
.login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 450px;
    padding: var(--spacing-lg);
}

/* Login Card */
.login-card {
    background: var(--white);
    border-radius: var(--border-radius-2xl);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-3xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color), var(--success-color));
}

/* Login Header */
.login-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.logo-container {
    position: relative;
    display: inline-block;
    margin-bottom: var(--spacing-lg);
}

.logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
    object-fit: cover;
}

.logo:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.logo-fallback {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-2xl);
    box-shadow: var(--shadow-md);
}

.login-header h1 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.title-main {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: var(--font-size-3xl);
}

.title-sub {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    font-weight: var(--font-weight-medium);
}

.login-header p {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-relaxed);
}

/* Error Message */
.error-message {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.05));
    border: 1px solid rgba(231, 76, 60, 0.2);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--danger-color);
    font-weight: var(--font-weight-medium);
    animation: shake 0.5s ease-in-out;
}

.error-message i {
    font-size: var(--font-size-lg);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Success Message */
.success-message {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--success-color);
    font-weight: var(--font-weight-medium);
    animation: slideInDown 0.5s ease-out;
}

.success-message i {
    font-size: var(--font-size-lg);
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Login Form */
.login-form {
    margin-bottom: var(--spacing-2xl);
}

.form-group {
    margin-bottom: var(--spacing-xl);
}

.form-group label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.form-group label i {
    color: var(--primary-color);
    font-size: var(--font-size-base);
}

/* Enhanced Input Wrapper */
.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper.focused {
    transform: translateY(-1px);
}

.form-input {
    width: 100%;
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-lg) 50px;
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    font-family: inherit;
    transition: all var(--transition-base);
    background: var(--white);
    color: var(--gray-800);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-input.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.form-input::placeholder {
    color: var(--gray-500);
}

.input-icon {
    position: absolute;
    left: var(--spacing-lg);
    color: var(--gray-500);
    font-size: var(--font-size-base);
    transition: all var(--transition-base);
    pointer-events: none;
    z-index: 1;
}

.input-wrapper.focused .input-icon {
    color: var(--primary-color);
}

.password-input {
    padding-right: 50px;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-lg);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-base);
    z-index: 2;
}

.password-toggle:hover {
    color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
}

/* Form Options */
.form-options {
    margin-bottom: var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    user-select: none;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-400);
    border-radius: var(--border-radius-sm);
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-base);
    position: relative;
}

.checkmark::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: var(--font-size-xs);
    color: var(--white);
    opacity: 0;
    transform: scale(0);
    transition: all var(--transition-base);
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    opacity: 1;
    transform: scale(1);
}

.remember-text {
    font-weight: var(--font-weight-medium);
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.login-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

.login-btn:disabled,
.login-btn.loading {
    opacity: 0.8;
    cursor: not-allowed;
    transform: none;
}

.login-btn i {
    font-size: var(--font-size-base);
}

/* Security Notice */
.security-notice {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    color: var(--success-color);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    opacity: 0;
    transform: translateY(10px);
    transition: all var(--transition-slow);
}

.security-notice i {
    font-size: var(--font-size-base);
}

/* Login Footer */
.login-footer {
    border-top: 1px solid var(--gray-200);
    padding-top: var(--spacing-xl);
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.footer-links {
    display: flex;
    justify-content: center;
}

.back-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-base);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    border: 1px solid transparent;
}

.back-link:hover {
    color: var(--primary-dark);
    background: rgba(52, 152, 219, 0.1);
    border-color: rgba(52, 152, 219, 0.2);
    transform: translateX(-2px);
}

/* Enhanced Login Info */
.login-info {
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    text-align: left;
}

.info-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
}

.info-header i {
    color: var(--info-color);
    font-size: var(--font-size-base);
}

.info-header h3 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    margin: 0;
}

.credentials {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.credential-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
}

.credential-item .label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--gray-600);
}

.credential-item code {
    background: var(--gray-100);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-sm);
    color: var(--gray-800);
    border: 1px solid var(--gray-300);
}

.security-warning {
    color: var(--warning-color);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(243, 156, 18, 0.1);
    border-radius: var(--border-radius-sm);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

.security-warning i {
    font-size: var(--font-size-sm);
}

/* Login Stats */
.login-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    font-weight: var(--font-weight-medium);
}

.stat-item i {
    font-size: var(--font-size-base);
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        max-width: 100%;
        padding: var(--spacing-md);
    }

    .login-card {
        padding: var(--spacing-2xl);
    }

    .logo {
        width: 60px;
        height: 60px;
    }

    .login-header h1 {
        font-size: var(--font-size-2xl);
    }

    .login-header p {
        font-size: var(--font-size-base);
    }

    .construction-icons i {
        font-size: var(--font-size-2xl);
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: var(--spacing-xl);
    }

    .login-header {
        margin-bottom: var(--spacing-2xl);
    }

    .logo {
        width: 50px;
        height: 50px;
    }

    .login-header h1 {
        font-size: var(--font-size-xl);
    }

    .form-group input,
    .login-btn {
        padding: var(--spacing-md);
    }

    .construction-icons i {
        font-size: var(--font-size-xl);
    }
}

/* Loading Animation */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
