# General

**IMPORTANT:** Please raise general questions in [litehelpers / Cordova-sqlite-help / issues](https://github.com/litehelpers/Cordova-sqlite-help/issues).

## Reproducible bugs

- Verify the results of calling the echoTest and selfTest functions as described in README.md
- Check the pitfalls and other troubleshooting steps in README.md and [brodybits / Avoiding-some-Cordova-pitfalls](https://github.com/brodybits/Avoiding-some-Cordova-pitfalls)
- Reproduce the issue in a fresh, clean project, preferably based on [brodybits / Cordova-sqlite-bootstrap-test](https://github.com/brodybits/Cordova-sqlite-bootstrap-test) or [brodybits / cordova-sqlite-test-app](https://github.com/brodybits/cordova-sqlite-test-app).

Please see README.md for more details.

## Feature requests

Feature requests are welcome for community discussion and future consideration. Preference will be given to paid customers. Please contact <<EMAIL>> for more information.

## Other questions

**NOTE:** As stated above please other questions in [litehelpers / Cordova-sqlite-help / issues](https://github.com/litehelpers/Cordova-sqlite-help/issues).

## Contributing patches

- Patches with bug fixes are helpful, especially when submitted with test code.
- Other enhancements are welcome for consideration, when submitted with test code and are working for all supported platforms. Increase of complexity should be avoided.
- All contributions may be reused by [@brodybits](https://github.com/brodybits) under another license in the future. Efforts will be taken to give credit for major contributions but it will not be guaranteed.
- Project restructuring, i.e. moving files and/or directories around, should be avoided if possible.

Please see README.md for more details.

## Other

- Testimonials of apps that are using this plugin would be especially helpful.
- Reporting issues can help improve the quality of this plugin.
