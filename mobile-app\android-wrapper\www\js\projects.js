/**
 * Projects Manager for Flori Construction Mobile App
 * Handles project listing, creation, editing, and management
 */

class ProjectsManager {
    constructor() {
        // Use the same API base as FloriAdmin
        this.apiBase = window.floriAdmin?.apiBase || this.getApiBaseUrl();
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.currentFilter = '';
        this.currentSearch = '';
        this.projects = [];
        this.isInitialized = false;

        this.init();
    }

    getApiBaseUrl() {
        // Get current location and construct API URL
        const currentLocation = window.location;
        const baseUrl = `${currentLocation.protocol}//${currentLocation.host}`;

        // Check if we're in mobile-app directory
        if (currentLocation.pathname.includes('/mobile-app/')) {
            return `${baseUrl}${currentLocation.pathname.replace('/mobile-app/', '/').replace(/\/[^\/]*$/, '')}/api`;
        }

        // Default fallback
        return `${baseUrl}/api`;
    }

    init() {
        this.setupEventListeners();
        this.isInitialized = true;
        console.log('ProjectsManager: Initialized successfully');
    }

    setupEventListeners() {
        console.log('ProjectsManager: Setting up event listeners...');

        // Add project button
        const addProjectBtn = document.getElementById('add-project-btn');
        if (addProjectBtn) {
            console.log('ProjectsManager: Add project button found');
            addProjectBtn.addEventListener('click', () => {
                this.showAddProjectModal();
            });
        } else {
            console.warn('ProjectsManager: Add project button not found');
        }

        // Filter dropdown
        const filterSelect = document.getElementById('project-type-filter');
        if (filterSelect) {
            console.log('ProjectsManager: Filter select found');
            filterSelect.addEventListener('change', (e) => {
                this.currentFilter = e.target.value;
                this.currentPage = 1;
                this.load();
            });
        } else {
            console.warn('ProjectsManager: Filter select not found');
        }

        // Search input
        const searchInput = document.getElementById('project-search');
        if (searchInput) {
            console.log('ProjectsManager: Search input found');
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.currentSearch = e.target.value;
                    this.currentPage = 1;
                    this.load();
                }, 500);
            });
        } else {
            console.warn('ProjectsManager: Search input not found');
        }

        console.log('ProjectsManager: Event listeners setup complete');
    }

    async load() {
        try {
            console.log('ProjectsManager: Starting to load projects...');

            // Check if floriAdmin is available
            if (!window.floriAdmin) {
                console.error('ProjectsManager: floriAdmin not available');
                throw new Error('Application not properly initialized');
            }

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage
            });

            if (this.currentFilter) {
                params.append('type', this.currentFilter);
            }

            if (this.currentSearch) {
                params.append('search', this.currentSearch);
            }

            const url = `mobile.php?action=projects&${params}`;
            console.log('ProjectsManager: Making API request to:', url);

            const response = await window.floriAdmin.apiRequest(url);
            console.log('ProjectsManager: API response:', response);

            // Check if response is null (network error or auth failure)
            if (!response) {
                throw new Error('No response received from server');
            }

            if (response.success) {
                // Handle different response structures
                const projectsData = response.data?.projects || response.projects || [];
                const paginationData = response.data?.pagination || response.pagination || {
                    page: this.currentPage,
                    pages: 1,
                    total: projectsData.length
                };

                console.log('ProjectsManager: Projects data:', projectsData);
                this.projects = projectsData;
                this.renderProjects();
                this.renderPagination(paginationData);
                console.log('ProjectsManager: Projects loaded successfully');
            } else {
                console.error('ProjectsManager: API returned error:', response);
                throw new Error(response.error || 'Failed to load projects');
            }

        } catch (error) {
            console.error('ProjectsManager: Failed to load projects:', error);
            console.error('ProjectsManager: Error details:', error.message, error.stack);

            // Show user-friendly error message
            const errorMessage = error.message === 'Application not properly initialized'
                ? 'Please refresh the page and try again'
                : 'Failed to load projects: ' + error.message;

            window.floriAdmin?.showToast(errorMessage, 'error');
        }
    }

    renderProjects() {
        const container = document.getElementById('projects-list');
        if (!container) {
            console.error('ProjectsManager: Projects list container not found');
            return;
        }

        console.log('ProjectsManager: Rendering', this.projects.length, 'projects');

        if (this.projects.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-building fa-3x"></i>
                    <h3>No projects found</h3>
                    <p>Start by adding your first project or check your connection</p>
                    <button class="btn btn-primary" onclick="window.ProjectsManager.showAddProjectModal()">
                        <i class="fas fa-plus"></i> Add Project
                    </button>
                    <button class="btn btn-secondary" onclick="window.ProjectsManager.load()" style="margin-left: 10px;">
                        <i class="fas fa-refresh"></i> Retry
                    </button>
                </div>
            `;
            return;
        }

        const html = this.projects.map(project => `
            <div class="project-card" data-id="${project.id}">
                <div class="project-image" style="background-image: url('${this.getProjectImage(project)}')">
                    <div class="project-actions">
                        <button class="btn btn-ghost btn-sm" onclick="window.ProjectsManager.viewProject(${project.id})" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-ghost btn-sm" onclick="window.ProjectsManager.editProject(${project.id})" title="Edit Project">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-ghost btn-sm" onclick="window.ProjectsManager.deleteProject(${project.id})" title="Delete Project">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="project-content">
                    <h3 class="project-title">${window.floriAdmin.escapeHtml(project.title)}</h3>
                    <p class="project-meta">
                        <i class="fas fa-map-marker-alt"></i> ${window.floriAdmin.escapeHtml(project.location || 'No location')}
                    </p>
                    <p class="project-description">${this.truncateText(project.short_description || project.description, 100)}</p>
                    <div class="project-footer">
                        <span class="project-type ${project.project_type}">${project.project_type}</span>
                        <span class="project-date">${window.floriAdmin.formatDate(project.created_at)}</span>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    renderPagination(pagination) {
        const container = document.getElementById('projects-pagination');
        if (!container || !pagination) return;

        const { page, pages } = pagination;

        if (pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = `
            <button class="pagination-btn" ${page <= 1 ? 'disabled' : ''} 
                    onclick="window.ProjectsManager.goToPage(${page - 1})">
                <i class="fas fa-chevron-left"></i>
            </button>
        `;

        // Page numbers
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(pages, page + 2);

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="pagination-btn ${i === page ? 'active' : ''}" 
                        onclick="window.ProjectsManager.goToPage(${i})">
                    ${i}
                </button>
            `;
        }

        html += `
            <button class="pagination-btn" ${page >= pages ? 'disabled' : ''} 
                    onclick="window.ProjectsManager.goToPage(${page + 1})">
                <i class="fas fa-chevron-right"></i>
            </button>
        `;

        container.innerHTML = html;
    }

    goToPage(page) {
        this.currentPage = page;
        this.load();
    }

    getProjectImage(project) {
        if (project.featured_image) {
            return `../uploads/${project.featured_image}`;
        }
        return '../assets/images/placeholder-project.jpg';
    }

    truncateText(text, maxLength) {
        if (!text) return '';
        if (text.length <= maxLength) return window.floriAdmin.escapeHtml(text);
        return window.floriAdmin.escapeHtml(text.substring(0, maxLength)) + '...';
    }

    showAddProjectModal() {
        const modalContent = `
            <div class="modal-header">
                <h2>Add New Project</h2>
                <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="add-project-form" class="project-form">
                <div class="form-group">
                    <label for="project-title">Project Title *</label>
                    <input type="text" id="project-title" name="title" required>
                </div>
                
                <div class="form-group">
                    <label for="project-type">Project Type *</label>
                    <select id="project-type" name="project_type" required>
                        <option value="">Select Type</option>
                        <option value="completed">Completed</option>
                        <option value="ongoing">Ongoing</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="project-location">Location</label>
                    <input type="text" id="project-location" name="location">
                </div>
                
                <div class="form-group">
                    <label for="project-client">Client Name</label>
                    <input type="text" id="project-client" name="client_name">
                </div>
                
                <div class="form-group">
                    <label for="project-short-desc">Short Description</label>
                    <textarea id="project-short-desc" name="short_description" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="project-description">Full Description</label>
                    <textarea id="project-description" name="description" rows="5"></textarea>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="is_featured"> Featured Project
                    </label>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-ghost" onclick="window.floriAdmin.closeModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Project
                    </button>
                </div>
            </form>
        `;

        window.floriAdmin.showModal(modalContent);

        // Setup form submission
        const form = document.getElementById('add-project-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCreateProject(form);
            });
        }
    }

    showEditProjectModal(project) {
        const modalContent = `
            <div class="modal-header">
                <h2>Edit Project</h2>
                <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="edit-project-form" class="project-form">
                <input type="hidden" id="project-id" name="id" value="${project.id}">

                <div class="form-group">
                    <label for="edit-project-title">Project Title *</label>
                    <input type="text" id="edit-project-title" name="title" value="${this.escapeHtml(project.title)}" required>
                </div>

                <div class="form-group">
                    <label for="edit-project-type">Project Type *</label>
                    <select id="edit-project-type" name="project_type" required>
                        <option value="">Select Type</option>
                        <option value="completed" ${project.project_type === 'completed' ? 'selected' : ''}>Completed</option>
                        <option value="ongoing" ${project.project_type === 'ongoing' ? 'selected' : ''}>Ongoing</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="edit-project-location">Location</label>
                    <input type="text" id="edit-project-location" name="location" value="${this.escapeHtml(project.location || '')}">
                </div>

                <div class="form-group">
                    <label for="edit-project-client">Client Name</label>
                    <input type="text" id="edit-project-client" name="client_name" value="${this.escapeHtml(project.client_name || '')}">
                </div>

                <div class="form-group">
                    <label for="edit-project-short-desc">Short Description</label>
                    <textarea id="edit-project-short-desc" name="short_description" rows="3">${this.escapeHtml(project.short_description || '')}</textarea>
                </div>

                <div class="form-group">
                    <label for="edit-project-description">Full Description</label>
                    <textarea id="edit-project-description" name="description" rows="5">${this.escapeHtml(project.description || '')}</textarea>
                </div>

                <div class="form-group">
                    <label for="edit-project-start-date">Start Date</label>
                    <input type="date" id="edit-project-start-date" name="start_date" value="${project.start_date || ''}">
                </div>

                <div class="form-group">
                    <label for="edit-project-end-date">End Date</label>
                    <input type="date" id="edit-project-end-date" name="end_date" value="${project.end_date || ''}">
                </div>

                <div class="form-group">
                    <label for="edit-project-value">Project Value</label>
                    <input type="number" id="edit-project-value" name="project_value" step="0.01" value="${project.project_value || ''}">
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" name="is_featured" ${project.is_featured ? 'checked' : ''}> Featured Project
                    </label>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-ghost" onclick="window.floriAdmin.closeModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Project
                    </button>
                </div>
            </form>
        `;

        window.floriAdmin.showModal(modalContent);

        // Setup form submission
        const form = document.getElementById('edit-project-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleUpdateProject(form);
            });
        }
    }

    handleCreateProject(form) {
        return this.createProject(form);
    }

    handleUpdateProject(form) {
        return this.updateProject(form);
    }

    async createProject(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        submitBtn.disabled = true;

        try {
            const projectData = {
                title: formData.get('title'),
                project_type: formData.get('project_type'),
                location: formData.get('location'),
                client_name: formData.get('client_name'),
                short_description: formData.get('short_description'),
                description: formData.get('description'),
                is_featured: formData.has('is_featured')
            };

            const response = await window.floriAdmin.apiRequest('mobile.php?action=project', {
                method: 'POST',
                body: JSON.stringify(projectData)
            });

            if (response && response.success) {
                window.floriAdmin.showToast('Project created successfully!', 'success');
                window.floriAdmin.closeModal();
                this.load(); // Reload projects list
            } else {
                throw new Error(response?.error || 'Failed to create project');
            }

        } catch (error) {
            console.error('Failed to create project:', error);
            window.floriAdmin.showToast('Failed to create project: ' + error.message, 'error');
        } finally {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    async updateProject(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const projectId = formData.get('id');

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
        submitBtn.disabled = true;

        try {
            const projectData = {
                id: projectId,
                title: formData.get('title'),
                project_type: formData.get('project_type'),
                location: formData.get('location'),
                client_name: formData.get('client_name'),
                short_description: formData.get('short_description'),
                description: formData.get('description'),
                start_date: formData.get('start_date') || null,
                end_date: formData.get('end_date') || null,
                project_value: formData.get('project_value') || null,
                is_featured: formData.has('is_featured')
            };

            const response = await window.floriAdmin.apiRequest(`mobile.php?action=project&id=${projectId}`, {
                method: 'PUT',
                body: JSON.stringify(projectData)
            });

            if (response && response.success) {
                window.floriAdmin.showToast('Project updated successfully!', 'success');
                window.floriAdmin.closeModal();
                this.load(); // Reload projects list
            } else {
                throw new Error(response?.error || 'Failed to update project');
            }

        } catch (error) {
            console.error('Failed to update project:', error);
            window.floriAdmin.showToast('Failed to update project: ' + error.message, 'error');
        } finally {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    async editProject(projectId) {
        console.log('ProjectsManager: Edit project requested for ID:', projectId);

        try {
            // First, fetch the project data
            const response = await window.floriAdmin.apiRequest(`mobile.php?action=project&id=${projectId}`);

            if (response && response.success) {
                const project = response.data.project;
                this.showEditProjectModal(project);
            } else {
                throw new Error(response?.error || 'Failed to load project data');
            }
        } catch (error) {
            console.error('Failed to load project for editing:', error);
            window.floriAdmin.showToast('Failed to load project: ' + error.message, 'error');
        }
    }

    async viewProject(projectId) {
        console.log('ProjectsManager: View project requested for ID:', projectId);

        try {
            // Fetch the project data
            const response = await window.floriAdmin.apiRequest(`mobile.php?action=project&id=${projectId}`);

            if (response && response.success) {
                const project = response.data.project;
                this.showViewProjectModal(project);
            } else {
                throw new Error(response?.error || 'Failed to load project data');
            }
        } catch (error) {
            console.error('Failed to load project for viewing:', error);
            window.floriAdmin.showToast('Failed to load project: ' + error.message, 'error');
        }
    }

    showViewProjectModal(project) {
        const modalContent = `
            <div class="modal-header">
                <h2>Project Details</h2>
                <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="project-details">
                <div class="project-detail-section">
                    <h3>${this.escapeHtml(project.title)}</h3>
                    <div class="project-badges">
                        <span class="project-type ${project.project_type}">${project.project_type}</span>
                        ${project.is_featured ? '<span class="featured-badge"><i class="fas fa-star"></i> Featured</span>' : ''}
                    </div>
                </div>

                ${project.client_name ? `
                <div class="project-detail-section">
                    <h4><i class="fas fa-user"></i> Client</h4>
                    <p>${this.escapeHtml(project.client_name)}</p>
                </div>
                ` : ''}

                ${project.location ? `
                <div class="project-detail-section">
                    <h4><i class="fas fa-map-marker-alt"></i> Location</h4>
                    <p>${this.escapeHtml(project.location)}</p>
                </div>
                ` : ''}

                ${project.start_date || project.end_date ? `
                <div class="project-detail-section">
                    <h4><i class="fas fa-calendar"></i> Timeline</h4>
                    <p>
                        ${project.start_date ? `Started: ${this.formatDate(project.start_date)}` : ''}
                        ${project.start_date && project.end_date ? '<br>' : ''}
                        ${project.end_date ? `Completed: ${this.formatDate(project.end_date)}` : ''}
                    </p>
                </div>
                ` : ''}

                ${project.project_value ? `
                <div class="project-detail-section">
                    <h4><i class="fas fa-dollar-sign"></i> Project Value</h4>
                    <p>$${parseFloat(project.project_value).toLocaleString()}</p>
                </div>
                ` : ''}

                ${project.short_description ? `
                <div class="project-detail-section">
                    <h4><i class="fas fa-info-circle"></i> Summary</h4>
                    <p>${this.escapeHtml(project.short_description)}</p>
                </div>
                ` : ''}

                ${project.description ? `
                <div class="project-detail-section">
                    <h4><i class="fas fa-file-text"></i> Description</h4>
                    <div class="project-description">${this.escapeHtml(project.description).replace(/\n/g, '<br>')}</div>
                </div>
                ` : ''}

                <div class="project-detail-actions">
                    <button class="btn btn-primary" onclick="window.ProjectsManager.editProject(${project.id}); window.floriAdmin.closeModal();">
                        <i class="fas fa-edit"></i> Edit Project
                    </button>
                    <button class="btn btn-ghost" onclick="window.floriAdmin.closeModal()">
                        Close
                    </button>
                </div>
            </div>
        `;

        window.floriAdmin.showModal(modalContent);
    }

    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    async deleteProject(projectId) {
        if (!confirm('Are you sure you want to delete this project?')) {
            return;
        }

        try {
            const response = await window.floriAdmin.apiRequest(`mobile.php?action=project&id=${projectId}`, {
                method: 'DELETE'
            });

            if (response && response.success) {
                window.floriAdmin.showToast('Project deleted successfully!', 'success');
                this.load(); // Reload projects list
            } else {
                throw new Error(response?.error || 'Failed to delete project');
            }

        } catch (error) {
            console.error('Failed to delete project:', error);
            window.floriAdmin.showToast('Failed to delete project: ' + error.message, 'error');
        }
    }

    // Debug method to test loading
    async testLoad() {
        console.log('ProjectsManager: Test load triggered');
        console.log('ProjectsManager: Current state:', {
            isInitialized: this.isInitialized,
            currentPage: this.currentPage,
            itemsPerPage: this.itemsPerPage,
            floriAdminAvailable: !!window.floriAdmin,
            token: window.floriAdmin?.token ? 'present' : 'missing'
        });

        await this.load();
    }
}

// Enhanced initialization with better timing and error handling
function initializeProjectsManager() {
    console.log('ProjectsManager: Initializing...');

    if (window.ProjectsManager) {
        console.log('ProjectsManager: Already initialized');
        return;
    }

    try {
        window.ProjectsManager = new ProjectsManager();
        console.log('ProjectsManager: Successfully initialized');

        // Auto-load projects if we're on the projects page
        setTimeout(() => {
            const projectsPage = document.getElementById('projects-page');
            if (projectsPage && projectsPage.classList.contains('active')) {
                console.log('ProjectsManager: Projects page is active, auto-loading...');
                window.ProjectsManager.load();
            }
        }, 200);

    } catch (error) {
        console.error('ProjectsManager: Initialization failed:', error);
    }
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', initializeProjectsManager);

// Also initialize when floriAdmin is ready (for cases where scripts load out of order)
if (window.floriAdmin) {
    console.log('ProjectsManager: floriAdmin already available');
    initializeProjectsManager();
} else {
    // Wait for floriAdmin to be available
    let attempts = 0;
    const maxAttempts = 20;

    const waitForFloriAdmin = () => {
        attempts++;
        if (window.floriAdmin) {
            console.log('ProjectsManager: floriAdmin now available');
            initializeProjectsManager();
        } else if (attempts < maxAttempts) {
            setTimeout(waitForFloriAdmin, 100);
        } else {
            console.warn('ProjectsManager: floriAdmin not available after waiting');
        }
    };

    setTimeout(waitForFloriAdmin, 100);
}

// Global function to manually trigger projects loading (for debugging)
window.loadProjects = function () {
    console.log('Global loadProjects called');
    if (window.ProjectsManager) {
        return window.ProjectsManager.load();
    } else {
        console.error('ProjectsManager not available');
        return Promise.reject(new Error('ProjectsManager not available'));
    }
};
