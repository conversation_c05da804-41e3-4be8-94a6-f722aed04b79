/* Flori Construction Admin App Styles */

/* Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    color: #333;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #e74c3c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Screen Management */
.screen {
    min-height: 100vh;
}

/* Login Screen */
.login-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 40px 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 100vh;
}

.logo {
    text-align: center;
    margin-bottom: 30px;
}

.logo img {
    height: 60px;
    width: auto;
}

.login-form h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #e74c3c;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.btn-ghost {
    background: transparent;
    color: #666;
    border: 1px solid #ddd;
}

.btn-ghost:hover {
    background: #f8f9fa;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Main App Layout - Updated for Bottom Navigation */
#main-app {
    display: grid;
    grid-template-areas:
        "header"
        "content";
    grid-template-columns: 1fr;
    grid-template-rows: 60px 1fr;
    height: 100vh;
    position: relative;
}

/* Header */
.app-header {
    grid-area: header;
    background: white;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Menu toggle removed - no longer needed */

#page-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

/* Main Content - Updated for Bottom Navigation */
.main-content {
    grid-area: content;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    padding-bottom: 90px;
    /* Space for bottom navigation and FAB */
}

/* Sidebar styles removed - replaced with bottom navigation */

/* Main content styles moved to mobile-navigation.css */

/* Pages */
.page {
    display: none;
}

.page.active {
    display: block;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-header h2 {
    color: #2c3e50;
    font-size: 24px;
}

/* Dashboard */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.stat-info h3 {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-info p {
    color: #666;
    font-size: 14px;
}

/* Filters */
.filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filters select,
.filters input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
}

/* Grids */
.projects-grid,
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.project-card,
.media-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.project-card:hover,
.media-card:hover {
    transform: translateY(-2px);
}

.project-image,
.media-image {
    height: 150px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.project-content,
.media-content {
    padding: 15px;
}

.project-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.project-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.project-type {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.project-type.completed {
    background: #d4edda;
    color: #155724;
}

.project-type.ongoing {
    background: #fff3cd;
    color: #856404;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination button:hover,
.pagination button.active {
    background: #e74c3c;
    color: white;
    border-color: #e74c3c;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-overlay.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.toast {
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #e74c3c;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left-color: #28a745;
}

.toast.error {
    border-left-color: #dc3545;
}

.toast.warning {
    border-left-color: #ffc107;
}

/* Error Messages */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 6px;
    margin-top: 15px;
    border: 1px solid #f5c6cb;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state i {
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #555;
}

.empty-state p {
    margin-bottom: 20px;
}

/* Project Form */
.project-form .form-group {
    margin-bottom: 20px;
}

.project-form textarea {
    min-height: 80px;
    resize: vertical;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* Project Cards */
.project-card {
    position: relative;
    overflow: hidden;
}

.project-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-actions {
    opacity: 1;
}

.project-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.project-date {
    font-size: 12px;
    color: #999;
}

/* Media Components */
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: #e74c3c;
    background: #fef5f5;
}

.upload-content i {
    color: #ddd;
    margin-bottom: 15px;
}

.upload-content h3 {
    margin-bottom: 10px;
    color: #555;
}

.upload-content p {
    color: #666;
    margin-bottom: 20px;
}

.selected-files {
    margin-bottom: 20px;
}

.selected-files h4 {
    margin-bottom: 15px;
    color: #555;
}

.selected-file {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 8px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-name {
    font-weight: 500;
}

.file-size {
    font-size: 12px;
    color: #666;
}

.upload-options {
    margin-bottom: 20px;
}

/* Media Grid */
.media-card {
    position: relative;
    overflow: hidden;
}

.media-preview {
    height: 150px;
    position: relative;
    overflow: hidden;
}

.media-preview img,
.media-preview video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.media-card:hover .media-overlay {
    opacity: 1;
}

.file-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
    color: #666;
}

.file-extension {
    font-size: 12px;
    font-weight: bold;
    margin-top: 5px;
}

.video-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 24px;
}

.media-info {
    padding: 15px;
}

.media-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.media-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.media-date {
    font-size: 12px;
    color: #999;
}

.media-viewer {
    text-align: center;
    max-height: 70vh;
    overflow: auto;
}

.file-preview-large {
    padding: 40px;
    color: #666;
}

.file-preview-large i {
    color: #ddd;
    margin-bottom: 20px;
}

.file-preview-large h3 {
    margin-bottom: 15px;
    color: #555;
}

/* Content Management */
.content-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e74c3c;
}

.section-items {
    display: grid;
    gap: 15px;
}

.content-item {
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 15px;
    background: #fafafa;
}

.content-form {
    margin: 0;
}

.content-form .form-group {
    margin-bottom: 15px;
}

.content-form .form-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997) !important;
    color: white !important;
}

/* Activity List */
.activity-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    gap: 15px;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    margin-bottom: 5px;
    color: #2c3e50;
    font-size: 14px;
}

.activity-content p {
    color: #666;
    font-size: 12px;
    margin-bottom: 5px;
}

.activity-time {
    font-size: 11px;
    color: #999;
}

/* Settings Sections */
.settings-sections {
    display: grid;
    gap: 20px;
}

.settings-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e74c3c;
}

/* Install Banner */
.install-banner {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    border-left: 4px solid #e74c3c;
}

.install-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.install-content span {
    flex: 1;
    font-weight: 500;
    color: #2c3e50;
}

/* Project Actions */
.project-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-actions {
    opacity: 1;
}

.btn-sm {
    padding: 6px 8px;
    font-size: 12px;
    border-radius: 4px;
}

.btn-ghost.btn-sm {
    background: rgba(255, 255, 255, 0.9);
    color: #666;
    border: none;
    backdrop-filter: blur(5px);
}

.btn-ghost.btn-sm:hover {
    background: rgba(255, 255, 255, 1);
    color: #333;
}

/* Project Details Modal */
.project-details {
    max-width: 600px;
}

.project-detail-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.project-detail-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.project-detail-section h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 20px;
}

.project-detail-section h4 {
    color: #34495e;
    margin-bottom: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.project-detail-section h4 i {
    color: #e74c3c;
    width: 16px;
}

.project-badges {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-top: 10px;
}

.featured-badge {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.project-description {
    line-height: 1.6;
    color: #555;
}

.project-detail-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Enhanced Modal Styles */
.modal-content {
    max-width: 600px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.modal-header h2 {
    color: #2c3e50;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.modal-close:hover {
    background: #f8f9fa;
}

/* Form Enhancements */
.project-form .form-group {
    margin-bottom: 15px;
}

.project-form .form-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.project-form textarea {
    resize: vertical;
    min-height: 80px;
}

.project-form input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* Empty State Enhancements */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state i {
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #555;
}

.empty-state p {
    margin-bottom: 20px;
    color: #777;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    #main-app {
        grid-template-areas:
            "header"
            "content";
        grid-template-columns: 1fr;
        grid-template-rows: 60px 1fr;
    }

    .sidebar {
        position: fixed;
        top: 60px;
        left: -250px;
        width: 250px;
        height: calc(100vh - 60px);
        z-index: 999;
        transition: left 0.3s ease;
    }

    .sidebar.active {
        left: 0;
    }

    .menu-toggle {
        display: block;
    }

    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .projects-grid,
    .media-grid {
        grid-template-columns: 1fr;
    }

    .filters {
        flex-direction: column;
    }

    .page-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .project-detail-actions {
        flex-direction: column;
    }

    .project-form .form-actions {
        flex-direction: column;
    }

    .project-actions {
        opacity: 1;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 4px;
        padding: 5px;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .service-actions {
        justify-content: center;
    }
}

/* Services Specific Styles */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.service-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.service-image {
    height: 180px;
    position: relative;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.service-content {
    padding: 20px;
}

.service-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #2c3e50;
    line-height: 1.3;
}

.service-description {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

.service-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #888;
    margin-bottom: 15px;
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
}

.service-meta span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.service-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

.btn-outline {
    background: transparent;
    border: 1px solid #ddd;
    color: #666;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #bbb;
}

.btn-danger {
    background: #dc3545;
    color: white;
    border: 1px solid #dc3545;
}

.btn-danger:hover {
    background: #c82333;
    border-color: #bd2130;
}

/* Service Form Styles */
.service-form .form-group {
    margin-bottom: 20px;
}

.service-form label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
}

.service-form input,
.service-form textarea,
.service-form select {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.service-form input:focus,
.service-form textarea:focus,
.service-form select:focus {
    outline: none;
    border-color: #e74c3c;
}

.char-count {
    font-size: 11px;
    color: #888;
    margin-top: 4px;
}

.char-count.over-limit {
    color: #dc3545;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 3px;
    position: relative;
}

.checkbox-label input[type="checkbox"]:checked+.checkmark {
    background: #e74c3c;
    border-color: #e74c3c;
}

.checkbox-label input[type="checkbox"]:checked+.checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Modal Styles */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #999;
    padding: 4px;
    border-radius: 4px;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #666;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state i {
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #999;
}

.empty-state p {
    margin-bottom: 20px;
    font-size: 14px;
}

/* Enhanced Services Features */
.page-title-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.services-stats {
    display: flex;
    gap: 15px;
    font-size: 14px;
    color: #666;
}

.services-stats span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.page-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

/* Advanced Filters */
.advanced-filters {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.search-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.search-container {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    pointer-events: none;
}

.search-container input {
    padding-left: 40px;
    padding-right: 40px;
}

.clear-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
}

.clear-btn:hover {
    background: #f0f0f0;
    color: #666;
}

.view-toggle {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
}

.view-btn {
    background: white;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    color: #666;
    transition: all 0.3s ease;
}

.view-btn.active,
.view-btn:hover {
    background: #e74c3c;
    color: white;
}

/* Bulk Selection */
.bulk-selection-bar {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.selection-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.btn-link {
    background: none;
    border: none;
    color: #e74c3c;
    cursor: pointer;
    text-decoration: underline;
    font-size: 14px;
}

.btn-link:hover {
    color: #c82333;
}

.bulk-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* Image Upload Area */
.image-upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.image-upload-area:hover,
.image-upload-area.drag-over {
    border-color: #e74c3c;
    background: #fef5f5;
}

.upload-placeholder {
    color: #666;
}

.upload-placeholder i {
    color: #ddd;
    margin-bottom: 10px;
}

.upload-placeholder p {
    margin: 10px 0 5px;
    font-weight: 500;
}

.upload-placeholder small {
    color: #999;
}

.image-preview {
    position: relative;
    display: inline-block;
}

.image-preview img {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    object-fit: cover;
}

.remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.remove-image:hover {
    background: #c82333;
}

/* Current Image Preview */
.current-image-preview {
    text-align: center;
    padding: 15px;
}

.current-image-preview img {
    display: block;
    margin: 0 auto 15px;
    border: 2px solid #e9ecef;
}

.current-image-preview .btn {
    margin: 0 5px;
}

/* Image Options Modal */
.image-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 20px 0;
}

.btn-block {
    width: 100%;
    justify-content: center;
}

/* Loading States */
.image-upload-area.loading {
    pointer-events: none;
    opacity: 0.7;
}

.image-upload-area.loading .upload-placeholder {
    background: #f8f9fa;
}

/* Enhanced drag over state */
.image-upload-area.drag-over {
    border-color: #28a745;
    background: #f8fff9;
    transform: scale(1.02);
}

.image-upload-area.drag-over .upload-placeholder i {
    color: #28a745;
    transform: scale(1.1);
}

/* Error states */
.image-upload-area.error {
    border-color: #dc3545;
    background: #fff5f5;
}

.image-upload-area.error .upload-placeholder {
    color: #dc3545;
}

/* Media Browser Grid */
.media-browser-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
}

.media-browser-item {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.media-browser-item:hover {
    border-color: #e74c3c;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.media-browser-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
}

.media-item-info {
    padding: 10px;
    text-align: center;
}

.media-name {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.media-size {
    font-size: 11px;
    color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .media-browser-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }

    .media-browser-item img {
        height: 100px;
    }

    .media-item-info {
        padding: 8px;
    }
}

/* Form Row */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

/* Service Card Enhancements */
.service-card {
    position: relative;
}

.service-card.selected {
    border: 2px solid #e74c3c;
    box-shadow: 0 4px 16px rgba(231, 76, 60, 0.2);
}

.service-checkbox {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 2;
}

.service-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.service-status {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.service-status.active {
    background: #d4edda;
    color: #155724;
}

.service-status.draft {
    background: #fff3cd;
    color: #856404;
}

.service-status.archived {
    background: #f8d7da;
    color: #721c24;
}

.service-tags {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.service-tag {
    background: #f8f9fa;
    color: #666;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    border: 1px solid #e9ecef;
}

/* List View */
.services-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.service-list-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 20px;
    align-items: center;
}

.service-list-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.service-list-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.service-list-content {
    flex: 1;
}

.service-list-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

/* Templates */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.template-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.template-card:hover {
    border-color: #e74c3c;
    background: #fef5f5;
    transform: translateY(-2px);
}

.template-card h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 16px;
}

.template-card p {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.template-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.template-tags .tag {
    background: #e74c3c;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .advanced-filters {
        padding: 15px;
    }

    .filter-row,
    .search-row {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .search-container {
        min-width: auto;
    }

    .page-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .page-actions {
        justify-content: center;
    }

    .bulk-selection-bar {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .bulk-actions {
        justify-content: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .templates-grid {
        grid-template-columns: 1fr;
    }

    .service-list-item {
        flex-direction: column;
        text-align: center;
    }

    .service-list-image {
        align-self: center;
    }
}

/* Pagination Controls */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

/* Enhanced PWA Install Banner */
.install-banner {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
    z-index: 9999;
    animation: slideUp 0.4s ease-out;
    max-width: 500px;
    margin: 0 auto;
}

.install-content {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    gap: 15px;
}

.install-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.install-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.install-text strong {
    font-size: 16px;
    font-weight: 600;
}

.install-text span {
    font-size: 14px;
    opacity: 0.9;
}

.install-actions {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

.install-actions .btn {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 6px;
    font-weight: 500;
}

.install-actions .btn-primary {
    background: white;
    color: #e74c3c;
    border: none;
}

.install-actions .btn-primary:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

.install-actions .btn-ghost {
    background: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.install-actions .btn-ghost:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Mobile responsiveness for install banner */
@media (max-width: 768px) {
    .install-banner {
        left: 10px;
        right: 10px;
        bottom: 10px;
    }

    .install-content {
        padding: 14px 16px;
        gap: 12px;
    }

    .install-text strong {
        font-size: 15px;
    }

    .install-text span {
        font-size: 13px;
    }

    .install-actions {
        flex-direction: column;
        gap: 8px;
        min-width: 80px;
    }

    .install-actions .btn {
        padding: 6px 12px;
        font-size: 13px;
    }
}