machine:
  environment:
    ANDROID_NDK_HOME: $ANDROID_NDK
    SPEC_ROOT: spec/www
    TESTS_PATH: tests/tests.js
    CORDOVA_PARAMEDIC_CMD: cordova-paramedic --platform android --plugin . --timeout 3600000

dependencies:
  pre:
    - npm install -g cordova-paramedic
    - npm install -g cordova

test:

  pre:
    - emulator -avd circleci-android21 -no-audio -no-window:
        background: true
        parallel: true
    - circle-android wait-for-boot

  override:
    - cordova-paramedic --platform android --plugin . || echo error-ignored
    - cp $SPEC_ROOT/spec/self-test.js $TESTS_PATH && $CORDOVA_PARAMEDIC_CMD
    - cp $SPEC_ROOT/spec/db-tx-string-test.js $TESTS_PATH && $CORDOVA_PARAMEDIC_CMD
    - cp $SPEC_ROOT/spec/db-tx-sql-select-value-test.js $TESTS_PATH && $CORDOVA_PARAMEDIC_CMD
    - cp $SPEC_ROOT/spec/basic-db-tx-sql-storage-results.js $TESTS_PATH && $CORDOVA_PARAMEDIC_CMD
    - cp $SPEC_ROOT/spec/db-sql-operations-test.js $TESTS_PATH && $CORDOVA_PARAMEDIC_CMD
    - cp $SPEC_ROOT/spec/sql-batch-test.js $TESTS_PATH && $CORDOVA_PARAMEDIC_CMD
