{"name": "cordova-sqlite-storage", "version": "5.1.0", "description": "Native interface to SQLite for PhoneGap/Cordova - cordova-sqlite-storage plugin version", "cordova": {"id": "cordova-sqlite-storage", "platforms": ["android", "browser", "ios", "osx", "windows"]}, "repository": {"type": "git", "url": "https://github.com/xpbrew/cordova-sqlite-storage.git"}, "keywords": ["sqlite", "ecosystem:cordova", "cordova-android", "cordova-browser", "cordova-ios", "cordova-osx", "cordova-windows"], "author": "various", "license": "MIT", "bugs": {"url": "https://github.com/xpbrew/cordova-sqlite-storage/issues"}, "homepage": "https://github.com/xpbrew/cordova-sqlite-storage", "dependencies": {"cordova-sqlite-storage-dependencies": "3.0.0"}, "scripts": {"clean-spec": "rm -rf spec/[mnp]* && git cl spec/config.xml && git status --ignored", "prepare-js": "coffee -p SQLitePlugin.coffee.md > www/SQLitePlugin.js", "prepare-spec": "node scripts/prepareSpec.js"}, "devDependencies": {"coffeescript": "1", "cross-spawn": "^6.0.5", "fs-extra": "^8.1.0"}}