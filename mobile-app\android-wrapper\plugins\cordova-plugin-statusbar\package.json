{"name": "cordova-plugin-statusbar", "version": "2.4.3", "description": "Cordova StatusBar Plugin", "types": "./types/index.d.ts", "cordova": {"id": "cordova-plugin-statusbar", "platforms": ["android", "ios", "wp7", "wp8", "windows"]}, "repository": {"type": "git", "url": "https://github.com/apache/cordova-plugin-statusbar"}, "bugs": {"url": "https://github.com/apache/cordova-plugin-statusbar/issues"}, "keywords": ["<PERSON><PERSON>", "statusbar", "ecosystem:cordova", "cordova-android", "cordova-ios", "cordova-wp7", "cordova-wp8", "cordova-windows"], "scripts": {"test": "npm run jshint", "jshint": "node node_modules/jshint/bin/jshint www && node node_modules/jshint/bin/jshint src && node node_modules/jshint/bin/jshint tests"}, "engines": {"cordovaDependencies": {"0.1.0": {"cordova": ">=3.0.0"}, "3.0.0": {"cordova": ">100"}}}, "author": "Apache Software Foundation", "license": "Apache-2.0", "devDependencies": {"jshint": "^2.6.0"}}