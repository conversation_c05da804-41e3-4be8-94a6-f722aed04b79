<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flori Construction - Admin App</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#e74c3c">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Flori Admin">

    <!-- Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="icons/icon-192x192.png">
    <link rel="apple-touch-icon" href="icons/icon-192x192.png">

    <!-- CSS -->
    <link rel="stylesheet" href="css/app.css">
    <link rel="stylesheet" href="css/android-native.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>Loading Flori Admin...</p>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="screen" style="display: none;">
        <div class="login-container">
            <div class="logo">
                <img src="../assets/images/logo.png" alt="Flori Construction"
                    onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display: none; text-align: center; font-size: 24px; font-weight: bold; color: #e74c3c;">
                    FLORI CONSTRUCTION
                </div>
            </div>
            <h1>Admin Login</h1>

            <form id="login-form" class="login-form">
                <div class="form-group">
                    <label for="username">Username or Email</label>
                    <input type="text" id="username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
            </form>

            <div id="login-error" class="error-message" style="display: none;"></div>
        </div>
    </div>

    <!-- Main App -->
    <div id="main-app" class="screen" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <button id="menu-toggle" class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 id="page-title">Dashboard</h1>
            </div>
            <div class="header-right">
                <div id="network-status" class="network-status">
                    <i class="fas fa-wifi"></i>
                </div>
                <button id="logout-btn" class="btn btn-ghost">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </header>

        <!-- Sidebar -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <img src="../assets/images/logo.png" alt="Flori Construction" onerror="this.style.display='none';">
                <span>Admin Panel</span>
            </div>

            <ul class="sidebar-menu">
                <li>
                    <a href="#dashboard" class="menu-item active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="#projects" class="menu-item" data-page="projects">
                        <i class="fas fa-building"></i>
                        <span>Projects</span>
                    </a>
                </li>
                <li>
                    <a href="#media" class="menu-item" data-page="media">
                        <i class="fas fa-images"></i>
                        <span>Media</span>
                    </a>
                </li>
                <li>
                    <a href="#services" class="menu-item" data-page="services">
                        <i class="fas fa-tools"></i>
                        <span>Services</span>
                    </a>
                </li>
            </ul>

            <!-- Native Features Section -->
            <div class="native-features">
                <h4>Native Features</h4>
                <button id="camera-btn" class="native-btn">
                    <i class="fas fa-camera"></i> Camera
                </button>
                <button id="gallery-btn" class="native-btn">
                    <i class="fas fa-images"></i> Gallery
                </button>
                <button id="device-info-btn" class="native-btn">
                    <i class="fas fa-info-circle"></i> Device Info
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page active">
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-projects">0</h3>
                            <p>Total Projects</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="completed-projects">0</h3>
                            <p>Completed</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="ongoing-projects">0</h3>
                            <p>Ongoing</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-media">0</h3>
                            <p>Media Files</p>
                        </div>
                    </div>
                </div>

                <div class="recent-activity">
                    <h2>Recent Activity</h2>
                    <div id="recent-projects" class="activity-list">
                        <p>Welcome to Flori Construction Admin App!</p>
                        <p>Native Android features are available in the sidebar.</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Cordova JavaScript -->
    <script type="text/javascript" src="cordova.js"></script>

    <!-- App JavaScript -->
    <script src="js/toast-system.js"></script>
    <script src="js/cordova-integration.js"></script>

    <!-- App Initialization -->
    <script>
        console.log('Flori Construction Admin App loaded');

        // App initialization
        function initializeApp() {
            console.log('Initializing app...');

            // Hide splash screen
            if (navigator.splashscreen) {
                navigator.splashscreen.hide();
            }

            // Hide loading screen and show login
            const loadingScreen = document.getElementById('loading-screen');
            const loginScreen = document.getElementById('login-screen');

            if (loadingScreen) {
                loadingScreen.style.display = 'none';
            }

            if (loginScreen) {
                loginScreen.style.display = 'block';
            }

            // Initialize Cordova features
            if (window.cordovaIntegration) {
                window.cordovaIntegration.init();
            }

            // Setup login form
            setupLoginForm();

            console.log('App initialized successfully');
        }

        // Login form setup
        function setupLoginForm() {
            const loginForm = document.getElementById('login-form');
            if (loginForm) {
                loginForm.addEventListener('submit', function (e) {
                    e.preventDefault();

                    // Simple demo login (replace with real authentication)
                    const username = document.getElementById('username').value;
                    const password = document.getElementById('password').value;

                    if (username && password) {
                        // Hide login screen and show main app
                        document.getElementById('login-screen').style.display = 'none';
                        document.getElementById('main-app').style.display = 'block';

                        // Show success toast
                        if (window.toast) {
                            window.toast.success('Welcome to Flori Construction Admin!');
                        }
                    } else {
                        // Show error
                        const errorDiv = document.getElementById('login-error');
                        if (errorDiv) {
                            errorDiv.textContent = 'Please enter username and password';
                            errorDiv.style.display = 'block';
                        }
                    }
                });
            }
        }

        // Setup menu toggle
        function setupMenuToggle() {
            const menuToggle = document.getElementById('menu-toggle');
            const sidebar = document.getElementById('sidebar');

            if (menuToggle && sidebar) {
                menuToggle.addEventListener('click', function () {
                    sidebar.classList.toggle('active');
                });
            }
        }

        // Initialize when Cordova is ready
        document.addEventListener('deviceready', function () {
            console.log('Device ready');
            initializeApp();
            setupMenuToggle();
        }, false);

        // Fallback for web testing
        document.addEventListener('DOMContentLoaded', function () {
            console.log('DOM ready');
            setTimeout(function () {
                initializeApp();
                setupMenuToggle();
            }, 1000);
        });
    </script>
</body>

</html>