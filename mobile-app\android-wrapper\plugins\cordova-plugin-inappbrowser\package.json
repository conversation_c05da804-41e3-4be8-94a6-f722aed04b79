{"name": "cordova-plugin-inappbrowser", "version": "4.1.0", "description": "Cordova InAppBrowser Plugin", "types": "./types/index.d.ts", "cordova": {"id": "cordova-plugin-inappbrowser", "platforms": ["android", "browser", "ios", "osx", "windows"]}, "repository": "github:apache/cordova-plugin-inappbrowser", "bugs": "https://github.com/apache/cordova-plugin-inappbrowser/issues", "keywords": ["<PERSON><PERSON>", "in", "app", "browser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ecosystem:cordova", "cordova-android", "cordova-browser", "cordova-ios", "cordova-osx", "cordova-windows"], "scripts": {"test": "npm run lint", "lint": "eslint ."}, "engines": {"cordovaDependencies": {"0.2.3": {"cordova": ">=3.1.0"}, "4.0.0": {"cordova": ">=3.1.0", "cordova-ios": ">=4.0.0"}, "5.0.0": {"cordova": ">100"}}}, "author": "Apache Software Foundation", "license": "Apache-2.0", "devDependencies": {"@cordova/eslint-config": "^3.0.0"}}