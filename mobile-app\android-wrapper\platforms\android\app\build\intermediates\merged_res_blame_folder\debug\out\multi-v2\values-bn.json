{"logs": [{"outputFile": "com.floriconstructionltd.admin.app-mergeDebugResources-27:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60c284a716caeffc9a2cb1c843b92804\\transformed\\jetified-play-services-basement-15.0.1\\res\\values-bn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "210", "endOffsets": "457"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3975", "endColumns": "210", "endOffsets": "4181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\56a603f4ce392bc972e3af0135819ac2\\transformed\\jetified-firebase-messaging-17.0.0\\res\\values-bn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "205", "endColumns": "99", "endOffsets": "304"}, "to": {"startLines": "47", "startColumns": "4", "startOffsets": "5495", "endColumns": "103", "endOffsets": "5594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\79e8455b0b361421d45210661fab4253\\transformed\\appcompat-1.6.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,5599", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,5681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d86008410acda0874d72ffb71d6307a\\transformed\\jetified-play-services-base-15.0.1\\res\\values-bn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,352,543,669,778,974,1094,1232,1361,1565,1672,1859,1985,2178,2352,2438,2531", "endColumns": "106,190,125,108,195,119,137,128,203,106,186,125,192,173,85,92,114", "endOffsets": "351,542,668,777,973,1093,1231,1360,1564,1671,1858,1984,2177,2351,2437,2530,2645"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2835,2946,3137,3267,3380,3576,3700,3842,4186,4390,4501,4688,4818,5011,5189,5279,5376", "endColumns": "110,190,129,112,195,123,141,132,203,110,186,129,192,177,89,96,118", "endOffsets": "2941,3132,3262,3375,3571,3695,3837,3970,4385,4496,4683,4813,5006,5184,5274,5371,5490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00fa6d42e7a8abf6c20b774bd480ebc9\\transformed\\core-1.9.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5686", "endColumns": "100", "endOffsets": "5782"}}]}]}