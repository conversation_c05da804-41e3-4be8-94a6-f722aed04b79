# Change Log

## [v2.2.0](https://github.com/phonegap/phonegap-plugin-push/tree/v2.2.0) (2017-10-23)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v2.1.0...v2.2.0)

* 🐛 Issue #2052: ServiceWorker.js Folder Instead of File [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/f24aed6e5ac9eb0f0fb38276648833a288fb6503)
* Issue #2051: Plugin not working on android 4.4 but working in 5,6 and 7 [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/68884671116c9f1e23de0b9cba583a2a008516ed)
* Added VoIP Notifications support for iOS (#2194) [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/69e86d98b1e05d1efcaed4be3c50c394fb782ea0)
* 🐧🐛🔏 Issue #2209: Use of an insecure Random Number Generator [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/e3a641780055b9b30380c4b1cd537d51c9353717)
* 📝 Explicit side effects for `.unregister()` [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/58e87593e749d5c6da2a45906a8c7c9309d6b09a)
* 🕸️ Issue #2109: Receive push notifications when using cordova-browser [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/f6e0dccfb2a6906c37b5f89f2d2170c3ff420d97)
* ⬆️ Issue #2097: Receiving multiple notifications although it is sent [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/3dabcb8c3c93f12e4d103f539935ce239adf1f9a)
* 📝 Issue #2087: Sending one unique push for iOS and Android [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/6c039164869612a0cee02ac66f2e846ee1aeced7)
* ⬆️ Bump default FCM version to 11.6.0 [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/9d6c388853aba8cfb6c3b193fc36154381ed585c)
* 📝 Issue #2087: Sending one unique push for iOS and Android [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/4576512d00227959e447a022e425b79114c80786)
* Update INSTALLATION.md [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/40dba0b9501a9b4c8b4d2eb492399c5723a41163)
* Fix instructions in README about google-services.json [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/a80996d06f41e370af07121ee53fb48adea2b9d0)
* 📝 Add contributor thank you [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/c6f2c91d2217c7f42bbb6405b1b538029cf0a35c)
* 🔧 Update gradle file for cordova-android 7 [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/8e28a4e6dea33b142f488cc2e336cbf0f42f6dd6)
* 🐛🍎 Issue #1826: Ionic Build Error, cannot find GoogleService-Info.plist [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/b8b38c25c03c99a5e66ca4bbeaf6db373bc071da)
* 🐛 Issue #1926: [question] FCM on ios [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/ec6f2d9be06eb7ca61c81cb0b73b7c9aa9d4818a)
* 📝 Issue #2033: A confusing part in the Android section of the install [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/9afac3882d93f445f800be681b1ac491ececd7ff)
* fixed issue #2061: Localization in message not working correctly. [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/b8454d169a004a3414f029755647715bdba39531)
* cleaned up PAYLOAD.md (#2040) [view commit](https://github.com/phonegap/phonegap-plugin-push/commit/3398120e2c6848a6e96267d083f5011f0c0f6f71)

## [v2.1.0](https://github.com/phonegap/phonegap-plugin-push/tree/v2.1.0) (2017-10-23)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v2.0.0...v2.1.0)

* 2.1.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a087ea4574b13808c8f657fed0fff543c56cfd15)
* :bookmark: Bumping plugin version to 2.1.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a8e19982068d7f0c281bc55ef32741a26749ba7a)
* :heavy_minus_sign: Remove pre-commit hook [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/be680b01110cbfff320eb404b7511df0c016ca54)
* :arrow_up: bump pluginpub to 0.0.9 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f7b739205d2291b01fe7055fe6ff86d6418b1f67)
* ✨🍎 Issue #1980: Remove confusing action button syntax and replace with events [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8c5786050a3b3d9ae58044a8c87b0f475808a221)
* ✨🐧 Issue #1950: Implement Android Oreo Notification badges [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c7d8fce6a6dfbd3d59abe0eef671cee67f5c7ffa)
* 🔨 Issue #1949: make function name more explicit [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/798a5a02b2f86754adff41b6f65aa30a50fba6bb)
* ✨ Issue #1949: add a listChannels method [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d8e70e359d586916c856f924edd42b607fd03eb4)
* 🐛 Issue #1949: Guard notification channel on older OS versions [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a301e473789b67abbe2fc16f473658ad3b16d662)
* ✨ Update to Issue #1949: Implement notification channels [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/acbebfa88171544d838c7aa5623d65cfffc9f0a2)
* ✨🐧 Issue #1314: Use icons from mipmap resources [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/****************************************)
* 🐛 Issue #1996: fix PR to use getBoolean not optBoolean [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/32ad27dc53739b2524082f59bf05eb7d12eb8da0)
* Add ongoing notifications for android (#1996) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c1e29c1a4d82b2e1ac64dfd475e7a818589b1a02)
* 🐛🐧🔥 Issue #1930: Android 8: PushNotification.hasPermission() returns true although push notifications are disabled by the system [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/28f7089d5f0f66b7b4ce71c93766315c29e49fe6)
* 🐛🐧 Issue #1718: cordova run android not working [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/468f6001c24a0e4889f64ad384f86585ff22ba9b)
* 🐛🍎 Issue #1988: iOS 11 + xcode 9: warnings on calling UI methods from a background thread [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cef862be704c74acc7abdd50899d925221ca6039)
* Merge pull request #2011 from jacquesdev/master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/073d6be83d1899f075331db87fe1e0181c11096b)
* Keep docs up to date with es6 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/34ea498c3ec3feb9107315a1e6a2222816db22a0)
* 🐛 Issue #1994: Unresolved Merge Conflict in master branch [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d39520f521325bb4a2dd1703acf70b0230f94b7e)
* ✨ #1984: Allow inline reply text to be set via push payload [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2bc01b5685ea7d90b11e1c86dc173b8d19af3296)
* Update FCMService.java (#1984) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0d667739b03f675b8959022db5e9b8ed05467a59)
* 📝 Issue #1982: Get GCM Id in Android but not FCM in iOS [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/21b4fdcd73748ed36d6283f85c77192421b80387)
* 🐧 📝 Issue #1980: Remove confusing action button syntax and replace wi… (#1981) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0cb03db294431593cba83f02e5c739bd63b6d886)
* 📝 Issue #1977: Silent Notifications failing to call push.on('notification') in the background with iOS 11 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8d0ad41fb5582bea2c021e6aee20a47a46ff87ed)
* Set the launch args correctly on windows (#1976) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/18cf3f4d5bee0582dbe90c1e660884e80aba87b8)
* :bug: Issue #1970: No custom sound plays when notification is forced in foreground. (#1971) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f936707d3d5b2e571d9756ee4b51608ce4c69980)
* Remove duplicated action to unsubscribe topic (#1974) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7742eb43fc330569a84b174dc1d25523c581e299)
* :sparkles: Issue #1949: Implement notification channels [view commit](http://github.com/phonegap/phonegap-plugin-push/commit3bce4d4c81e738cb6557f1267807fb89406378a0)
* :memo: Issue #1819: Android - app doesn't come to foreground when clicking notification [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/eda5e430bb698ecec97b3a3d105be00807c1e7fd)
* Catch the resource not found execption when 'gcm_defaultSenderId' is not defined, for whatever reason, so that the app does not crash. (#1923) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7d22655fe9de630ee75299f21135be76960c8cc2)
* :wrench: add precommit hook [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cd3cd2ba7ea37cde47410281e44693db9ffb1e7c)
* :shirt: Fixing linting errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1b109f56b1c72f9507e41eacde9a13e7e7d1561a)
* Merge pull request #1919 from TillaTheHun0/master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9998b52762ed684b57902de8f2619fac617b743a)
* Update PAYLOAD.md [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/19f07aa6c2d1bfd996ac7a9176e4029191b94fa0)
* :memo: Issue #1825: Build iOS app without Firebase [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a747ec16c0bd0c80b3fbbdc13eeec7ee3f107f60)
* Added `hasPermission` support for Windows platform. (#1908) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ddc9d2d7d4574e3cabf6146fff2faf6c8cdeb56e)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9b9bbca66d06f2a2df47c835028b45a0ced30851)

## [v2.0.0](https://github.com/* remote origin

Fetch URL: https://github.com//tree/v2.0.0) (2017-08-02)
[Full Changelog](https://github.com/* remote origin
Fetch URL: https://github.com//compare/v2.0.0-rc5...v2.0.0)

* 2.0.0 [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/d19db8cc6c845fc5504a618077d37c7ad44d3a9a)
* :bookmark: Bumping plugin version to 2.0.0 [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/44280ee917ccbee22cf62156ce34fa69aec6f110)
* :wrench: Add package-lock.json to git ignore [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/88e43f869a6469be3f1a2f50ad64fb3a4e22e1c9)
* update ShortcutBadger to 1.1.17 (#1873) [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/02c43301a2c39b516f0281dd2a30e8e813649212)
* :memo: Issue #1863: Linker error for iOS build [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/e509983a0124469765d0d693c92d8e023475b526)
* :memo: Update minimum CLI version in docs [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/f13ac0404d402a4a59267d92ac8a2ca9ca444a45)
* update engines to use cordova 7 (#1877) [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/983c9ad90934edf891ec54a7c3ca101b584710cc)
* Fix topic registration (#1855) [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/781e6f551e8e1509dd6184c84347e36aa163e092)
* Merge pull request #1827 from szh/master [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/2a8e3dff3698710b2e5fed8dea91deaa0705bbdc)
* Add clearAllNotifications() to type definitions [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/8460ecf725ea6ca62b933a2f7c5a6c82628599eb)
* Only use GCM if IS_GCM_ENABLED is true in the google plist (#1821) [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/04cb6af750271d421f5bab2e5b46f5c15faee20c)
* :apple: Issue #1340: Drop support for iOS 8 [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/d724805bfa4088ebbd00fcb8cb3ccbc36defefdd)
* V2.x Android: handle dismissed notifications (#1816) [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/118b1a454eebc9e5b053f40be3f1a3af253d783e)
* :fire: Removing RegistrationIntentService class [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/3dcc7ff67295aee9a6a9b3554061284cb6db3332)
* :sparkles::apple::penguin: Issue #1787: Pass token type during registration event [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/1ade39a5fc618173bcc12448a86f3fe9702319a3)
* :memo: Issue #1811: [DOCS] Update additional-resources section of INSTALLATION.md [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/c350ce2c0cffb763eb2b1a84295cd701bf33693a)
* Updating CHANGELOG [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/0264f2f5edf577053bcd436977a4f54ecd5e13b7)

## [v2.0.0-rc5](https://github.com/phonegap/phonegap-plugin-push/tree/v2.0.0-rc5) (2017-06-20)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v2.0.0-rc4...v2.0.0-rc5)

* 2.0.0-rc5 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a5dfcb4e364a1b31d98d4b9683d9e4fe2e0e5d2d)
* :bookmark: Bumping plugin version to 2.0.0-rc5 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4bb16bea611e9c472f34c3ede9a1a6d00a8e4c4d)
* :arrow_up: Bump FCM to 11.0.1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/576135bf576be6e2cd183101d3d7219b4d297030)
* :penguin: Issue #1796: Remove hook from 2.0.0 version [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/464636b9ae257afc712cfebcc1ed11425590c509)
* :memo: Issue #1552: library not found for -lPods-Appname [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/95b23cdf5ae68faf714d63ac69cfe71ce7034dba)
* :checkered_flag: Issue #1670: Subscribe and Unsubscribe for windows [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/228cdb4062f6019f878c456b89ef0685955ac7ed)
* :memo: Issue #1760: fcmSandbox vs gcmSandbox [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/64562177a6d32f34b29f2a97fcc8846db9926a1c)
* :bug: Issue #1785: plugin fails to install on windows using plugman [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/156adfc70e85a21cb9f3134de72781db405c5e07)
* :memo: Issue #1767: Ionic 2 Android build fails with error (Can't find google-services.json file) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/fa9625e7530f4c2280804fc0fcd52a7b7476e5eb)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6f98be137f997c2bbd32e5c192c1e928e4e1f2c9)

## [v2.0.0-rc4](https://github.com/phonegap/phonegap-plugin-push/tree/v2.0.0-rc4) (2017-06-01)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v2.0.0-rc3...v2.0.0-rc4)

* 2.0.0-rc4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/65f2e4c56b0ed440b8668986114c2c84b49e9c68)
* :bookmark: Bumping plugin version to 2.0.0-rc4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4e887023484cb5d6dd92524e05a6490d22eb3974)
* Update push.js [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a7cc5bdb8d149a3f6e7e9d918ecd9bb27c34009f)
* :memo: Issue #1679: Please clarify the use of content-available in the payload doc [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/23f7965e26d48b43c09921138b1290a658a41528)
* Update INSTALLATION.md (#1745) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f3e94bbc1dd79bf830a2342ce3d6ca0d0e1cf9a2)
* Fixing 404 link to definition file [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9df6b4de82047a30b8322eb635c581de6c7252aa)
* :bug::wrench: Issue #1744: The plugin installs npm in my local node_modules [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/06bb2b207848e2dc993c28052f3ad29bf84d88c4)
* :bug: Issue #1725: Provide default for applicationServerKey [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d63bbe949edd21e86d886baf9aa00aab30f0ca95)
* :wrench: use Node 4 on Travis [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2da999beffe2ef043767c8c979ca4e7ae7a069c6)
* Fix INSTALLATION.md docs for v2.0.0 and Firebase Cloud Messaging (#1741) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/32ff975ea1318f6194ac01477ca563cfe4a0218a)
* update FirebaseMessaging podspec (#1742) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1842f7ade4643ad733c18f80b0daa211147ab72c)
* :art: Fix merge conflicts [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3efba512317119ea2d54473c7e164a6c24db3ca3)
* Merge v2.0.x into master (#1736) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d07fdf031e052f9c457319e6aaa9d7bfb72d1224)
* Fixes #1716 - Incomplete CocoaPods installation steps (#1738) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/525816cb0fd591b49f51b2844ac04e55397b6b8b)
* docs updated (#1727) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4234bfe9dab6e9f72b2ff52c3a94287154d39229)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e7cbe6ea8c863b0096425836473f2fa05a0da048)
* 1.10.4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ec7c3ea1fafbf3fafe502d278af218715aebb909)
* :bookmark: Bumping plugin version to 1.10.4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b1efe1fa069c034aaf465e040300fd2884d46075)
* :penguin::bug: Issue #1710: Notification message key overwritten [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/911a1d4fe5d3a05e0012ee8121464cfb8974ce23)
* :penguin::bug: Issue #1710: Notification message key overwritten [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4eadccd93d6daa81a05396a93fdc2033a4c90b12)
* :penguin: Fix issue #1663 by allowing message as the data payload key (#1666) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f8ac07399905fb9c6b0ab48139fa76066c5e190f)
* [typo] Small fix to payload doc [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5a4a04b1be5a7dd30a9c577441b241767ec20500)
* 2.0.0-rc3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b393a9d932aee66da277b404c2475cb77195d8d8)
* :bookmark: Bumping plugin version to 2.0.0-rc3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8003110d0757107ad211cd0e0b9c175e60dcd7ed)
* :package: update www/push.js [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c0a3a4c6d578b2ee14a82f167934ecc8a7672557)
* :wrench: Update cordovaDependencies [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/969d546c3dc96efb59a226ee5df38de6e66e4de4)
* :memo: update readme [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0cbb7a92946ac9febb5ac5960291928d539c0fe6)
* :memo: Fix PAYLOAD.md regarding "drawable" directory name (#1711) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a27a62d6e346b1e61ffe2ffaae482461cf970c03)
* Bump requirements [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/85e6419cbad124eaa3eac1c1c515aa684ccf4393)
* Add resource-file way of copying google services files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/46ba2745f0ebc08cc3b12f9c51177247130b592c)
* Merge branch 'v2.0.x' of https://github.com/phonegap/phonegap-plugin-push into v2.0.x [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a5c9e456ce333b3387b2e4412248cdf45d26334c)
* Fixing package.json from failed merge/rebase. [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/974d2e218bcc8ab8ebf779ef18df74622702eb0e)
* Remove hook and use resource-file tag to copy google services file [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/86763c7f2cc15eae39972e4137813edb5cd8b838)
* Bumping plugin version to 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9850b1b180c3b66f64edc1680d358a7b1df58b95)
* :bookmark: Bumping plugin version to 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/15a68ed070d611f0569c90adaa062099120a7817)
* :wrench: add tern to gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/784b7355a62490a5ce6229292213e22e671873c8)
* :hammer::wrench::arrow_up: Use Babel to transpile ES2015 code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9b9df30cf65a275c0d4727a2854b12792ec905a2)
* :bug: Issue #1188: Strings.xml google_app_id conflict with google-services.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/620af90cdda85f64aa65cc0fc0de051cb13980ba)
* Issue #689: Remove sender id from PushNotification init iOS options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ee9ecc957d8961e2a0ae884f55ee5abc71652885)
* Issue #689: Remove sender id from PushNotification init Android options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c7545607bb23ea801f7a167d3408b112e4a6e812)
* Fix rebase errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2ac7442de047ecedf4a6d2c66591889e14bdafc4)
* Use CocoaPods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5c2fc51055343467e51f6facbd690e71273cbac2)
* fixed registration and removed unused code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d1b4f51fb2cd6e9ee8447fee0ce4a5e9eba0a52d)
* Added hook and resource file to copy GoogleService-Info.plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0169587f198d0b44a06f408d803bbdac0fb1d079)
* Changed code to work with FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/38bd3581663ccd85b2daed7b9833571a4adbc99e)
* Removed GCM files and added FCM files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9d5dbb196763e399d5177c0d4802ecc043cfc270)
* Added partial Android FCM support (#975) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/24f0a77bac10e0d8e4ff837a165a3d69c3447601)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2535a189caddba2a41f9e002930541c55360047d)
* 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8aa6f717db7dd534c97ad559307d391788e13e47)
* Bumping plugin version to 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/740cfe2bda63548a0e9cbf62b1833a4945eb432f)
* :apple::bug: Issue #1497: App crashes after refreshing when using FCM (v2) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/704b3635764700d5fc06f2e9c8c6a657ea4b7f29)
* :pencil2: fixing error in CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/68b045e6e40cdf1d5b9ed84136bebdf5f7874b8a)
* :memo: Using a newer version on the examples [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d8c4d002e4b6c6b399e5c5e5456012a121ee7b20)
* :penguin::memo::bug: Issue #1470: Cannot install phonegap-plugin-push on master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/632a4f1d87ff306bbc8920133e96b84125e44468)
* :bug::apple::wrench: Fixing a merge issue where aps-environment was accidentally removed [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4fb78566b5ebbbf2f04268b91f9c8cbc7193601e)
* :bug::penguin::memo::arrow_up: #1460: Build Issue Android [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e93f04a87763a762d581a18439b2de808fc81a2c)
* :bug::apple: Issue #1461: App crashes when initialising with topics (v2.0.0-rc1) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8f827a9678dac02887872bd7374fde1c40caeb90)
* Fixing my merge error for PR #1378 Optional event emit instead of function call for action buttons [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/64044115de796c52132e60719d5e93fc16594002)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d1f8f8e55af40a16129cdc362e179f0c7ef60bd0)
* 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/05c52e22945c0405b76a6d10d837ae6b015e661b)
* :bookmark: Bumping plugin version to 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/600f6050a2c3899559148579c5bc32c1e56449fa)
* :wrench: add tern to gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2660a8620874d520e2f3f3217cf20ea369affada)
* :hammer::wrench::arrow_up: Use Babel to transpile ES2015 code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3f043c21a608fdea4436409b68fbcf2c822d6c0a)
* :wrench: Add browser platform back for FCM branch [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/97a1615a79ab0317a5dae27b7124dbaec7bc71c7)
* Pin FCM to 9.8.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e04b175c72d0680b3b618fdbcce80f9f9d470055)
* Add empty google services plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d21632fd2acf90847c67b0c70f0b740ed3d367a7)
* :bug: Issue #1188: Strings.xml google_app_id conflict with google-services.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b41580f7476a34014ceb03976f25989f98cbde6a)
* :memo: Update to using fcm-node [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f84574aeffc958b6ec152ca36c2ea595418e901c)
* Issue #689: Remove sender id from PushNotification init iOS options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/326943dadc29fe662ce4925b8141960e31d78dd3)
* Issue #689: Remove sender id from PushNotification init Android options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b5c0156bb13a22e11cf6f174a7307e404ed6d154)
* Refactor GCMIntentService to FCMService [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/006ca17a7482490fbbfc67faa88a4c60d81a49f4)
* Fix topic subscription and unsubscription on FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/102934b167c4e69c9a6a5c7b41d0ca3fba0cfd25)
* Fix rebase errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/bccc1ba887603b8577c01ee5b885bffa0d406028)
* Use CocoaPods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7fe0379c6be007817c21355044bcecdd05bcc301)
* fixed registration and removed unused code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6c1a5572c420c88772bd3fc89d81fac21027a5be)
* Added hook and resource file to copy GoogleService-Info.plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/98c100b12613a2885ed1afd417b1e678883d37ee)
* Changed code to work with FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7861fd27cd052c7e80736b8da9cba8cfae442e73)
* Added .framework files as custom frameworks [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b3dc189600842c6a8e318b032bdf215ea1c9b81f)
* Removed GCM files and added FCM files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ff2dbe0cc5a2dce0a92b6c16f35b1a67ce6a6f18)
* Fixed empty token on android first run (#1008) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/50285f9193f5e6b33e63c2540b8f3c7a257a5221)
* Added partial Android FCM support (#975) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f67e30d41f5ea94a60a53b00ed51b995960f89c9)
* Merge branch 'v2.0.x' of https://github.com/phonegap/phonegap-plugin-push into v2.0.x [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/96af63840f28a57d29e21a48cc52533fa0830bea)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7371f3b31ea7a672ec6d43da7ef9475916b6c5c5)
* 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5099c8fe435bda7d8bc7b2648d078a3b63cf19ea)
* Bumping plugin version to 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/72d29bd4372e6043d38835d07839346ea204390a)
* :apple::bug: Issue #1497: App crashes after refreshing when using FCM (v2) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c44be0628bb42ba7ee07456524e9b1fffae64aea)
* :memo: Using a newer version on the examples [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/95cdee193d2977a17e778bf91ca1bcfc240dc266)
* :penguin::memo::bug: Issue #1470: Cannot install phonegap-plugin-push on master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a1fa7cce1168145c77fef2632ad64f8926e71d27)
* :bug::apple::wrench: Fixing a merge issue where aps-environment was accidentally removed [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ef4c7e187f2c5f386d9c844ee7211c8ff1cc214c)
* :bug::penguin::memo::arrow_up: #1460: Build Issue Android [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5d52fdaf28f0535ef6dab315abd67141b0fcd0f7)
* :bug::apple: Issue #1461: App crashes when initialising with topics (v2.0.0-rc1) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5d9dfdf179b39312a6382a48dd99d675e642a533)
* Fixing my merge error for PR #1378 Optional event emit instead of function call for action buttons [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/057cbd16f10581cd601079a9a10b9338df3c23eb)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/83238fce85413cdd192b5ff33139ba9a0bcd080f)
* 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9ff3929bed6bff911027bef21168e527ff61fd2c)
* :bookmark: Bumping plugin version to 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/94d8cc7ec8ba3a4d466758ffb2c27104c2cc1ca3)
* :wrench: add tern to gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a837c875d8a30fc4175693dff43139569974ec22)
* :hammer::wrench::arrow_up: Use Babel to transpile ES2015 code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/49da4ea30af1a2fb479110074ddf5f67e2ba370c)
* :wrench: Add browser platform back for FCM branch [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ebc5d9353bab3917f91c775cf13aec47c5ca1e04)
* Pin FCM to 9.8.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/505c8da8253cb8562ed16659b5ec2a8e73ec1c23)
* Add empty google services plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/46eafb1e5688ade278086a1644d06de4e36849b5)
* :bug: Issue #1188: Strings.xml google_app_id conflict with google-services.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f3b01f155f6300dc05c625116e0c374af61d6388)
* :memo: Update to using fcm-node [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/453561c8f2dfc4ed09b6a182f6998c90408a3d44)
* Issue #689: Remove sender id from PushNotification init iOS options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ba0020349337160f83271b683195693ef0b4f440)
* Issue #689: Remove sender id from PushNotification init Android options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/aee7b93a4d0b0bb0b9c987704c1cde82612e4445)
* Refactor GCMIntentService to FCMService [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5e0e7d107b2c9f4aeeda3a7757213e41ac573798)
* Fix topic subscription and unsubscription on FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/172528ea548174fd7be62d0ab2f9816566447a61)
* Fix rebase errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2727d274ef650fb0b4d25786d42f0ee5f72e9730)
* Use CocoaPods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/697e592d8225f4e0853a0ea72a598d10a18c832d)
* fixed registration and removed unused code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c2987cbee54f04e7d44eec421b2417be5e7d716e)
* Added hook and resource file to copy GoogleService-Info.plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f358dba691fb39757003326209a232cefce53adf)
* Changed code to work with FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7b7de65c2718eccc1ff0eeb2800973cd440c85a7)
* Added .framework files as custom frameworks [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8bf4343369e6b12f6ec28a104512cb3ab392e834)
* Removed GCM files and added FCM files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3de63bdf9e5a365e3b3e295f2a305ad66b512917)
* Fixed empty token on android first run (#1008) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b9ad4ebdeee6700d9edf95bf051dabda5923e01a)
* Added partial Android FCM support (#975) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c119bf08869d51d5fe23f9a0fb78fd8325b35248)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3a1806aaec5d3c76f7fcd30ddfd85d576fb6d197)
* 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/471b2aa829cb0ecc93a9a788891602ad17319a47)
* Bumping plugin version to 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7ad471ff45724828fb21630fdfbc244ba037d9d8)
* :apple::bug: Issue #1497: App crashes after refreshing when using FCM (v2) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b80dfb12b5053184936a4c6c881f1af55459348f)
* :pencil2: fixing error in CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9796292910f2d600b22d4846c128196cfb54ba7c)
* :memo: Using a newer version on the examples [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/297b8d28f2d7bf04420744c445e59a527c52d502)
* :penguin::memo::bug: Issue #1470: Cannot install phonegap-plugin-push on master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/41c8e66483fc0c5f21da7477d2522a2212a8017a)
* :bug::apple::wrench: Fixing a merge issue where aps-environment was accidentally removed [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/08e496fffc7fa082410f7b16e73e6afe12da194c)
* :bug::penguin::memo::arrow_up: #1460: Build Issue Android [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/156718a5800dfe4b87593e0732f4258c7c148bea)
* :bug::apple: Issue #1461: App crashes when initialising with topics (v2.0.0-rc1) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0add758d08657e22501612ed258033e31c394e6a)
* Fixing my merge error for PR #1378 Optional event emit instead of function call for action buttons [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1f2fd671ae734201b1260bc3d7878ae9ef28673c)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/70c6e04a3ba072b91b3752173ca2287d4e448b8f)
* 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7e07d81dbb47babe161f3204cdd06222a1e2ab3c)
* :bookmark: Bumping plugin version to 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f3dffdcf63c3d19b4717eed89eb911b8aecdd25a)
* :wrench: add tern to gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f5182503b241f519c03c872ae12f3489383f2b83)
* :hammer::wrench::arrow_up: Use Babel to transpile ES2015 code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/27a101f1d35217e0a1b7f0be9ad0607d31ea6c57)
* :wrench: Add browser platform back for FCM branch [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c25a47bd3b5c2437ce3108656449658568f2c053)
* Pin FCM to 9.8.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/000bc36a8ce2a00e96212b66d69f1597dac68554)
* Add empty google services plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/aa1cc247fcf8b94c4d60d26b18c9229c112e8185)
* :bug: Issue #1188: Strings.xml google_app_id conflict with google-services.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7f4bcafafe56be18f6dc64f3e634a6de594bc034)
* :memo: Update to using fcm-node [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a3f0eee774c7f9791f55f85816b0aeadc5c4fb4c)
* Issue #689: Remove sender id from PushNotification init iOS options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6c796a49c3a44b47f58237d7e7760f5ebc34c371)
* Issue #689: Remove sender id from PushNotification init Android options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/10c5153672dc478dd072274c220200526c313604)
* Refactor GCMIntentService to FCMService [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/aa6e3ce5449accd5397b4eda8a950fd5cebc4f0e)
* Fix topic subscription and unsubscription on FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b65fe745b5eabbb7437a46b46e747be4aaf5116a)
* Fix rebase errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3b5f3c71657d8af3e4407ac9ea6c36e00988b1cf)
* Use CocoaPods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/89040874c530c7f86c2acbcce5c3b88b351e80fb)
* fixed registration and removed unused code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a1e90013f8ca8497acc2513f4ce3df1358293d51)
* Added hook and resource file to copy GoogleService-Info.plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0bc1ca736546242772516334f47fd4ea4f8f5e5f)
* Changed code to work with FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f7acf338d5bff2bc25b5c1e2fa681b7e20254cc9)
* Added .framework files as custom frameworks [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a65be290d4176c1c0b51b700d673550ae22cf777)
* Removed GCM files and added FCM files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ed630d481b9035eb9df48738c0e77029937fafe9)
* Fixed empty token on android first run (#1008) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c9c53761fa3d6fc99acaa96601e9abc673a62c23)
* Added partial Android FCM support (#975) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6c46880b57aa1dee2bbcfeb5a86b497035f46ebe)

## [v1.10.4](https://github.com/phonegap/phonegap-plugin-push/tree/v1.10.4) (2017-05-17)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.10.3...v1.10.4)

* 1.10.4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ec7c3ea1fafbf3fafe502d278af218715aebb909)
* :bookmark: Bumping plugin version to 1.10.4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b1efe1fa069c034aaf465e040300fd2884d46075)
* :penguin::bug: Issue #1710: Notification message key overwritten [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/911a1d4fe5d3a05e0012ee8121464cfb8974ce23)
* :penguin::bug: Issue #1710: Notification message key overwritten [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4eadccd93d6daa81a05396a93fdc2033a4c90b12)
* :penguin: Fix issue #1663 by allowing message as the data payload key (#1666) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f8ac07399905fb9c6b0ab48139fa76066c5e190f)
* [typo] Small fix to payload doc [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5a4a04b1be5a7dd30a9c577441b241767ec20500)
* :memo: update readme [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0cbb7a92946ac9febb5ac5960291928d539c0fe6)
* :memo: Fix PAYLOAD.md regarding "drawable" directory name (#1711) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a27a62d6e346b1e61ffe2ffaae482461cf970c03)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cfad83fa420df5ba4ac12f2f3f7fa68fdb22cc03)

## [v2.0.0-rc3](https://github.com/phonegap/phonegap-plugin-push/tree/v2.0.0-rc3) (2017-05-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v2.0.0-rc2...v2.0.0-rc3)

* 2.0.0-rc3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b393a9d932aee66da277b404c2475cb77195d8d8)
* :bookmark: Bumping plugin version to 2.0.0-rc3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8003110d0757107ad211cd0e0b9c175e60dcd7ed)
* :package: update www/push.js [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c0a3a4c6d578b2ee14a82f167934ecc8a7672557)
* :wrench: Update cordovaDependencies [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/969d546c3dc96efb59a226ee5df38de6e66e4de4)
* Bump requirements [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/85e6419cbad124eaa3eac1c1c515aa684ccf4393)
* Add resource-file way of copying google services files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/46ba2745f0ebc08cc3b12f9c51177247130b592c)
* Merge branch 'v2.0.x' of https://github.com/phonegap/phonegap-plugin-push into v2.0.x [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a5c9e456ce333b3387b2e4412248cdf45d26334c)
* Fixing package.json from failed merge/rebase. [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/974d2e218bcc8ab8ebf779ef18df74622702eb0e)
* Remove hook and use resource-file tag to copy google services file [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/86763c7f2cc15eae39972e4137813edb5cd8b838)
* Bumping plugin version to 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9850b1b180c3b66f64edc1680d358a7b1df58b95)
* :bookmark: Bumping plugin version to 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/15a68ed070d611f0569c90adaa062099120a7817)
* :wrench: add tern to gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/784b7355a62490a5ce6229292213e22e671873c8)
* :hammer::wrench::arrow_up: Use Babel to transpile ES2015 code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9b9df30cf65a275c0d4727a2854b12792ec905a2)
* :bug: Issue #1188: Strings.xml google_app_id conflict with google-services.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/620af90cdda85f64aa65cc0fc0de051cb13980ba)
* Issue #689: Remove sender id from PushNotification init iOS options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ee9ecc957d8961e2a0ae884f55ee5abc71652885)
* Issue #689: Remove sender id from PushNotification init Android options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c7545607bb23ea801f7a167d3408b112e4a6e812)
* Fix rebase errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2ac7442de047ecedf4a6d2c66591889e14bdafc4)
* Use CocoaPods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5c2fc51055343467e51f6facbd690e71273cbac2)
* fixed registration and removed unused code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d1b4f51fb2cd6e9ee8447fee0ce4a5e9eba0a52d)
* Added hook and resource file to copy GoogleService-Info.plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0169587f198d0b44a06f408d803bbdac0fb1d079)
* Changed code to work with FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/38bd3581663ccd85b2daed7b9833571a4adbc99e)
* Removed GCM files and added FCM files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9d5dbb196763e399d5177c0d4802ecc043cfc270)
* Added partial Android FCM support (#975) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/24f0a77bac10e0d8e4ff837a165a3d69c3447601)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2535a189caddba2a41f9e002930541c55360047d)
* 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8aa6f717db7dd534c97ad559307d391788e13e47)
* Bumping plugin version to 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/740cfe2bda63548a0e9cbf62b1833a4945eb432f)
* :apple::bug: Issue #1497: App crashes after refreshing when using FCM (v2) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/704b3635764700d5fc06f2e9c8c6a657ea4b7f29)
* :pencil2: fixing error in CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/68b045e6e40cdf1d5b9ed84136bebdf5f7874b8a)
* :memo: Using a newer version on the examples [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d8c4d002e4b6c6b399e5c5e5456012a121ee7b20)
* :penguin::memo::bug: Issue #1470: Cannot install phonegap-plugin-push on master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/632a4f1d87ff306bbc8920133e96b84125e44468)
* :bug::apple::wrench: Fixing a merge issue where aps-environment was accidentally removed [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4fb78566b5ebbbf2f04268b91f9c8cbc7193601e)
* :bug::penguin::memo::arrow_up: #1460: Build Issue Android [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e93f04a87763a762d581a18439b2de808fc81a2c)
* :bug::apple: Issue #1461: App crashes when initialising with topics (v2.0.0-rc1) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8f827a9678dac02887872bd7374fde1c40caeb90)
* Fixing my merge error for PR #1378 Optional event emit instead of function call for action buttons [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/64044115de796c52132e60719d5e93fc16594002)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d1f8f8e55af40a16129cdc362e179f0c7ef60bd0)
* 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/05c52e22945c0405b76a6d10d837ae6b015e661b)
* :bookmark: Bumping plugin version to 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/600f6050a2c3899559148579c5bc32c1e56449fa)
* :wrench: add tern to gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2660a8620874d520e2f3f3217cf20ea369affada)
* :hammer::wrench::arrow_up: Use Babel to transpile ES2015 code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3f043c21a608fdea4436409b68fbcf2c822d6c0a)
* :wrench: Add browser platform back for FCM branch [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/97a1615a79ab0317a5dae27b7124dbaec7bc71c7)
* Pin FCM to 9.8.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e04b175c72d0680b3b618fdbcce80f9f9d470055)
* Add empty google services plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d21632fd2acf90847c67b0c70f0b740ed3d367a7)
* :bug: Issue #1188: Strings.xml google_app_id conflict with google-services.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b41580f7476a34014ceb03976f25989f98cbde6a)
* :memo: Update to using fcm-node [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f84574aeffc958b6ec152ca36c2ea595418e901c)
* Issue #689: Remove sender id from PushNotification init iOS options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/326943dadc29fe662ce4925b8141960e31d78dd3)
* Issue #689: Remove sender id from PushNotification init Android options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b5c0156bb13a22e11cf6f174a7307e404ed6d154)
* Refactor GCMIntentService to FCMService [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/006ca17a7482490fbbfc67faa88a4c60d81a49f4)
* Fix topic subscription and unsubscription on FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/102934b167c4e69c9a6a5c7b41d0ca3fba0cfd25)
* Fix rebase errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/bccc1ba887603b8577c01ee5b885bffa0d406028)
* Use CocoaPods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7fe0379c6be007817c21355044bcecdd05bcc301)
* fixed registration and removed unused code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6c1a5572c420c88772bd3fc89d81fac21027a5be)
* Added hook and resource file to copy GoogleService-Info.plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/98c100b12613a2885ed1afd417b1e678883d37ee)
* Changed code to work with FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7861fd27cd052c7e80736b8da9cba8cfae442e73)
* Added .framework files as custom frameworks [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b3dc189600842c6a8e318b032bdf215ea1c9b81f)
* Removed GCM files and added FCM files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ff2dbe0cc5a2dce0a92b6c16f35b1a67ce6a6f18)
* Fixed empty token on android first run (#1008) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/50285f9193f5e6b33e63c2540b8f3c7a257a5221)
* Added partial Android FCM support (#975) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f67e30d41f5ea94a60a53b00ed51b995960f89c9)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cfad83fa420df5ba4ac12f2f3f7fa68fdb22cc03)
* 1.10.3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1e072b351056d453fd1c1d40d5fcac310f3e107c)
* :bookmark: Bumping plugin version to 1.10.3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/29df66eae54e773925e25bd92299957e4d654723)
* :bug: Handle null in getCircleBitmap (#1705) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3acdfa338d7a8b56ec4dc73c50aa9917ecb3be7c)
* :shirt: Issue #1702: The logging tag can be at most 23 characters, was 40 (PushPlugin_BackgroundActionButtonHandler) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2045873acda2e81d54b2da87cd2d10f056bd90f9)
* Shortened log tag PushPlugin_BackgroundActionButtonHandler to bring u… (#1703) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/dd707122b7aa78649fa6f5f73ba9b05436799926)
* Merge branch 'v2.0.x' of https://github.com/phonegap/phonegap-plugin-push into v2.0.x [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/96af63840f28a57d29e21a48cc52533fa0830bea)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7371f3b31ea7a672ec6d43da7ef9475916b6c5c5)
* 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5099c8fe435bda7d8bc7b2648d078a3b63cf19ea)
* Bumping plugin version to 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/72d29bd4372e6043d38835d07839346ea204390a)
* :apple::bug: Issue #1497: App crashes after refreshing when using FCM (v2) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c44be0628bb42ba7ee07456524e9b1fffae64aea)
* :memo: Using a newer version on the examples [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/95cdee193d2977a17e778bf91ca1bcfc240dc266)
* :penguin::memo::bug: Issue #1470: Cannot install phonegap-plugin-push on master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a1fa7cce1168145c77fef2632ad64f8926e71d27)
* :bug::apple::wrench: Fixing a merge issue where aps-environment was accidentally removed [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ef4c7e187f2c5f386d9c844ee7211c8ff1cc214c)
* :bug::penguin::memo::arrow_up: #1460: Build Issue Android [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5d52fdaf28f0535ef6dab315abd67141b0fcd0f7)
* :bug::apple: Issue #1461: App crashes when initialising with topics (v2.0.0-rc1) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5d9dfdf179b39312a6382a48dd99d675e642a533)
* Fixing my merge error for PR #1378 Optional event emit instead of function call for action buttons [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/057cbd16f10581cd601079a9a10b9338df3c23eb)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/83238fce85413cdd192b5ff33139ba9a0bcd080f)
* 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9ff3929bed6bff911027bef21168e527ff61fd2c)
* :bookmark: Bumping plugin version to 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/94d8cc7ec8ba3a4d466758ffb2c27104c2cc1ca3)
* :wrench: add tern to gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a837c875d8a30fc4175693dff43139569974ec22)
* :hammer::wrench::arrow_up: Use Babel to transpile ES2015 code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/49da4ea30af1a2fb479110074ddf5f67e2ba370c)
* :wrench: Add browser platform back for FCM branch [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ebc5d9353bab3917f91c775cf13aec47c5ca1e04)
* Pin FCM to 9.8.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/505c8da8253cb8562ed16659b5ec2a8e73ec1c23)
* Add empty google services plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/46eafb1e5688ade278086a1644d06de4e36849b5)
* :bug: Issue #1188: Strings.xml google_app_id conflict with google-services.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f3b01f155f6300dc05c625116e0c374af61d6388)
* :memo: Update to using fcm-node [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/453561c8f2dfc4ed09b6a182f6998c90408a3d44)
* Issue #689: Remove sender id from PushNotification init iOS options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ba0020349337160f83271b683195693ef0b4f440)
* Issue #689: Remove sender id from PushNotification init Android options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/aee7b93a4d0b0bb0b9c987704c1cde82612e4445)
* Refactor GCMIntentService to FCMService [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5e0e7d107b2c9f4aeeda3a7757213e41ac573798)
* Fix topic subscription and unsubscription on FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/172528ea548174fd7be62d0ab2f9816566447a61)
* Fix rebase errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2727d274ef650fb0b4d25786d42f0ee5f72e9730)
* Use CocoaPods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/697e592d8225f4e0853a0ea72a598d10a18c832d)
* fixed registration and removed unused code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c2987cbee54f04e7d44eec421b2417be5e7d716e)
* Added hook and resource file to copy GoogleService-Info.plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f358dba691fb39757003326209a232cefce53adf)
* Changed code to work with FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7b7de65c2718eccc1ff0eeb2800973cd440c85a7)
* Added .framework files as custom frameworks [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8bf4343369e6b12f6ec28a104512cb3ab392e834)
* Removed GCM files and added FCM files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3de63bdf9e5a365e3b3e295f2a305ad66b512917)
* Fixed empty token on android first run (#1008) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b9ad4ebdeee6700d9edf95bf051dabda5923e01a)
* Added partial Android FCM support (#975) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c119bf08869d51d5fe23f9a0fb78fd8325b35248)
* :memo: Docs for interoperability with Firebase (#1693) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cebb6403143047192462f30c22b9510a8c6dbe21)
* fix headline (#1685) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e2e7993544eed544eb7286868b60e2e3efb6275d)
* Fix changelog date [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3140b2b00388785f8056632376b50a1d1ef67b96)
* :arrow_up: update pluginpub to 0.0.8 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cfab7d91556a38ee81550fe47e13f2662ae810db)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/027ea2c17e4b96b848ab29046efea243e6e2da27)
* 1.10.2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b5a7d4ec6e64abaed65de00be3e9bac9ab25791e)
* :bookmark: Bumping plugin version to 1.10.2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5b700ad9927c401081a5de49f2a6a27ba0dfaa9a)
* :arrow_up: update pluginpub to 0.0.7 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/38564ce55e53e018c91f4063d680eedd2631b825)
* Fix the dates on the CHANGELOG (the last 2 version) (#1676) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c0f26192c906bfc1a60390333c96d5dc07433978)
* Vapid Support (#1675) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/384b60bade628035b21d23f07e284eb6e1557a10)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/97d618fb53084cfae78f397def48df791131358f)
* 1.10.1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6211e8c3df8881a90d19b111a0e63f890d435df6)
* :bookmark: Bumping plugin version to 1.10.1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6d710a06681ad84c13273fe5d20feb3033ac67b6)
* :bug: Issue #1655: App opens on clearing notification (Android) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ff417925f6d6678f0fcd8315d5f4b4b08fbb9085)
* :memo: Issue #1118: Problem with notifications stacking [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d669c44a863d86d0bb73b5ae086bc2fe6f8113a9)
* :memo: Issue #1220: [Question] the hook setting seem not work [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4ef137eea370da7c225ab2a5cf63b1e97a68f4a4)
* Set get badge count android (#1644) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/630907cf3d8802bcd5d91b6bd768c989f6ef897a)
* Add plugin typings from DefinitelyTyped (#1654) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6ea70d9984e176a75602e72e1d26f5404c519e29)
* :arrow_up: Issue #1560: setApplicationIconBadgeNumber not working on Android but firing success function [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/700701fd2151667905d860228cf954301186721a)
* :memo: Issue #1618: No notification when app is closed on Android, not at all on iOS [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7f64bf0ffbddf3ef20de2fe540ec2718be5d0c23)
* :arrow_down: Issue #1560: setApplicationIconBadgeNumber not working on Android but firing success function [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e450fd7b623f27f27858a7537ec7950aa0f618b5)
* :arrow_up: Issue #1560: setApplicationIconBadgeNumber not working on Android but firing success function [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4af3472263a5125cab6f08bbb59b83bf957144ec)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ed25a3e3ad9fb6f2af63fd07957944f974eafaa1)
* 1.10.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6b9b862da8566c7717dfc79dd6b32d8a7e6774d8)
* :bookmark: Bumping plugin version to 1.10.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/626e9615fbff6ea225569ab58353ac7f58aef495)
* :heavy_plus_sign: update pluginpub version [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c3c8058ffee888447017eb5d8c0f4f30cbcd090f)
* Issue #1464: Create round bitmap icon for large icon image passed in from local resource or url (#1635) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0dd0d468868f0b13c8d840c78dd89fca5920cd32)
* Receive notification only from SENDER_ID (#1484) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a0d51e54aac39b8c58e4c67080f174c0228947c9)
* Add no-cache flag to payload (#1620) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/42f8cefbd187c36534e6ab59b6611fb7f15b91f0)
* Add dismiss key to on notification data (#1621) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4259bb3cacfe2561ed44e9f16bd74f8d5ae45ae2)
* return true for old android versions, since AppOpsManager is available starting at 19 (#1634) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/945aa2ad6d266e82683e0bee86f53d258f2b31b6)
* :art: remove reference to unused String [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/75f2191632a7a51eda7510a172ae6cc9d477acb9)
* Ability to use custom keys to find message title and text on Android (#1604) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/fd366296773906c91d5f8dfa3e8ba813c7c71b85)
* :memo: Fixed URL of apples custom sound documentation (#1600) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8c93f8622eb1c453cb0c681158a07deca32bf200)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/409020c90edf04e0a37232cff8aadb070d4ccaa9)
* 1.9.4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2241c7431cd60a68d2f35e7b4a5bfd797d5161b6)
* Bumping plugin version to 1.9.4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ec47932bf8fdf9cd96db076fe56306ab230048d9)
* Issue #1591: App crashes with the latest updates of Android SDK with plugin v-1.9.2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/514c21366ab37001ca323bec58261e023edaefd7)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3a218e4c68ebc1088461c2cfec966e57eaa24089)
* 1.9.3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/42723d6609862aa950abce67b2a637736bdd9e99)
* :bookmark: Bumping plugin version to 1.9.3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8f7b7dc003a2bc5bab7a316b0e2b0cf475c5a449)
* :wrench: Add valid SPDX license expression [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4274d0759acd7110ed2592ed1d2ce3bf692711d3)
* :memo: Issue #1587: v2.0.0-rc2: .on('notification') event not fired when background notification in Android [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7e90853a09a2c49f29e12eed03977b08b1295387)
* :arrow_up: Issue #1560: setApplicationIconBadgeNumber not working on Android but firing success function [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3188a3907f50dcf625b7663ecf74ea9a9209d437)
* :memo: Issue #1557: push.on('notification') not triggered - Ionic [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b91e9420fb68c4efc943cc5f8ecbd81274ffcbec)
* :memo: Issue #1407: Uncaught (in promise): Error: Push plugin not found! [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/adf3eabb8871980d2dead7ecb8185ad0da1d6b46)
* Corrected merges usage to prevent possible conflicts with other plugins (#1538) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/00c67cb2c85e97dfe4f7020f28ad4d954458599f)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3a1806aaec5d3c76f7fcd30ddfd85d576fb6d197)
* 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/471b2aa829cb0ecc93a9a788891602ad17319a47)
* Bumping plugin version to 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7ad471ff45724828fb21630fdfbc244ba037d9d8)
* :apple::bug: Issue #1497: App crashes after refreshing when using FCM (v2) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b80dfb12b5053184936a4c6c881f1af55459348f)
* :pencil2: fixing error in CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/9796292910f2d600b22d4846c128196cfb54ba7c)
* :memo: Using a newer version on the examples [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/297b8d28f2d7bf04420744c445e59a527c52d502)
* :penguin::memo::bug: Issue #1470: Cannot install phonegap-plugin-push on master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/41c8e66483fc0c5f21da7477d2522a2212a8017a)
* :bug::apple::wrench: Fixing a merge issue where aps-environment was accidentally removed [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/08e496fffc7fa082410f7b16e73e6afe12da194c)
* :bug::penguin::memo::arrow_up: #1460: Build Issue Android [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/156718a5800dfe4b87593e0732f4258c7c148bea)
* :bug::apple: Issue #1461: App crashes when initialising with topics (v2.0.0-rc1) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0add758d08657e22501612ed258033e31c394e6a)
* Fixing my merge error for PR #1378 Optional event emit instead of function call for action buttons [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1f2fd671ae734201b1260bc3d7878ae9ef28673c)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/70c6e04a3ba072b91b3752173ca2287d4e448b8f)
* 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7e07d81dbb47babe161f3204cdd06222a1e2ab3c)
* :bookmark: Bumping plugin version to 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f3dffdcf63c3d19b4717eed89eb911b8aecdd25a)
* :wrench: add tern to gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f5182503b241f519c03c872ae12f3489383f2b83)
* :hammer::wrench::arrow_up: Use Babel to transpile ES2015 code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/27a101f1d35217e0a1b7f0be9ad0607d31ea6c57)
* :wrench: Add browser platform back for FCM branch [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c25a47bd3b5c2437ce3108656449658568f2c053)
* Pin FCM to 9.8.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/000bc36a8ce2a00e96212b66d69f1597dac68554)
* Add empty google services plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/aa1cc247fcf8b94c4d60d26b18c9229c112e8185)
* :bug: Issue #1188: Strings.xml google_app_id conflict with google-services.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7f4bcafafe56be18f6dc64f3e634a6de594bc034)
* :memo: Update to using fcm-node [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a3f0eee774c7f9791f55f85816b0aeadc5c4fb4c)
* Issue #689: Remove sender id from PushNotification init iOS options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6c796a49c3a44b47f58237d7e7760f5ebc34c371)
* Issue #689: Remove sender id from PushNotification init Android options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/10c5153672dc478dd072274c220200526c313604)
* Refactor GCMIntentService to FCMService [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/aa6e3ce5449accd5397b4eda8a950fd5cebc4f0e)
* Fix topic subscription and unsubscription on FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b65fe745b5eabbb7437a46b46e747be4aaf5116a)
* Fix rebase errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3b5f3c71657d8af3e4407ac9ea6c36e00988b1cf)
* Use CocoaPods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/89040874c530c7f86c2acbcce5c3b88b351e80fb)
* fixed registration and removed unused code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a1e90013f8ca8497acc2513f4ce3df1358293d51)
* Added hook and resource file to copy GoogleService-Info.plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0bc1ca736546242772516334f47fd4ea4f8f5e5f)
* Changed code to work with FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f7acf338d5bff2bc25b5c1e2fa681b7e20254cc9)
* Added .framework files as custom frameworks [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a65be290d4176c1c0b51b700d673550ae22cf777)
* Removed GCM files and added FCM files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ed630d481b9035eb9df48738c0e77029937fafe9)
* Fixed empty token on android first run (#1008) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c9c53761fa3d6fc99acaa96601e9abc673a62c23)
* Added partial Android FCM support (#975) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6c46880b57aa1dee2bbcfeb5a86b497035f46ebe)
* :penguin: android mixpanel pushnotification suport added (#1523) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/951cb6921a717d847c279ad6896c28772c70103f)
* :memo: Making string replacement clearer [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/abdb656315bd4457c7ae43aaa52e2357df85d139)
* :bug::penguin::memo: Issue #1433: Cordova Push V5 register () crashes App when initialized with topicList [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1d5723c81ad7a1e9d76fdce22161e8e8aa8da262)
* :bug::penguin: Issue #1421: Notification delay caused by icon bitmap timeout [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/fb4b533f2b31daebc7ed57c16228458def3d2af9)
* :memo: Issue #1442: CocoaPods support vs requirement [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5912b1ea911fbe3b45a3a47ed005b7048a487ba6)
* 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/67041a994d70fd3a04149003607b88947e8cc994)
* Bumping plugin version to 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7dea689ba17ebb901ee12da62801f051a99cc368)
* :apple::bug: Issue #1497: App crashes after refreshing when using FCM (v2) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ea92e039b1d7640b70ca94e5f8748e7d2abbf13a)
* :pencil2: fixing error in CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/498bb038799bd687d8c492154bd3b34d72edd322)
* :memo: Using a newer version on the examples [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/74aba315b4cbb1e06c902e76891bce5582cbe690)
* :penguin::memo::bug: Issue #1470: Cannot install phonegap-plugin-push on master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e2f8a62c431af26c4d2fa487daa704067a088643)
* :bug::apple::wrench: Fixing a merge issue where aps-environment was accidentally removed [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cb9839740c4cbff5711224eed4b91b55aba77612)
* :bug::penguin::memo::arrow_up: #1460: Build Issue Android [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1bef8b37ef7bc017571924edc9e05fe09cd25e29)
* :bug::apple: Issue #1461: App crashes when initialising with topics (v2.0.0-rc1) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ca5c281897c664b0bd98097ec2fc8c19c33b2c63)
* Fixing my merge error for PR #1378 Optional event emit instead of function call for action buttons [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/170d7dd43fe047c6caf84ec0f59da6c2c0cdeb6f)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/fa4e36606c965504dba609940a2acf24f74ed978)
* 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/af6c31933e3daedf6e5a7f046e971efcf65cc1ea)
* :bookmark: Bumping plugin version to 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/99742021c8c6c2cd860c40b01db6a3dc18095dbb)
* :wrench: add tern to gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ff14a71b5e365f5c93159e759f989a6bbe89b40a)
* :hammer::wrench::arrow_up: Use Babel to transpile ES2015 code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/92dca439e1b0a0750a7e466bace2c4cb3acd19d8)
* :wrench: Add browser platform back for FCM branch [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4cbc1cb69203c5a0fab250bd49b99a398ce86558)
* Pin FCM to 9.8.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7075bc8206641aa6459cf6acc4fe447fb1d57f77)
* Add empty google services plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d043c5f9d7872dc4340151c0645a24716391f58f)
* :bug: Issue #1188: Strings.xml google_app_id conflict with google-services.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f7a7c5c172190acab17fdfd54ad726a7a2fad701)
* :memo: Update to using fcm-node [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/15e68b03956a1dd292fda87e0da4f1ad9700d9c0)
* Issue #689: Remove sender id from PushNotification init iOS options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/54aa482fb8af6ac15a60fb06090077e1d68dee6c)
* Issue #689: Remove sender id from PushNotification init Android options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1276d538b80106dd2f3d67996a531e64e7aa4937)
* Refactor GCMIntentService to FCMService [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/504f2dcb5b158e0b2e151b255aad28a659bc2c4d)
* Fix topic subscription and unsubscription on FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/97f0aeaa1099b89076b28282b2d2daac7ec62b33)
* Fix rebase errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e11d89f46572d1e4430f1f6a63945d74b56e574c)
* Use CocoaPods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/fa66ac99685f1f2b580597a45b16315ab7748028)
* fixed registration and removed unused code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c539b041cbe2a2b6e9a360a91ee2a9bfdfd16b03)
* Added hook and resource file to copy GoogleService-Info.plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/dce5e9fb70e4df45a10fd8348a7def64864fdd24)
* Changed code to work with FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6f0182aff86b04c22630d1586d6ac6ca617c7e61)
* Added .framework files as custom frameworks [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/562a22f45bdafa4e2887996e9c0fa295b8bac886)
* Removed GCM files and added FCM files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f1d14b2615d7c8330afbdbf0faa1d2438473a45d)
* Fixed empty token on android first run (#1008) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7e4c47b5c5647866f24e6c2f47e4c98a1f8e2442)
* Added partial Android FCM support (#975) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a02580e19d470919ec61ec489cdf4ee6ca2f0d8c)

## [v1.10.3](https://github.com/phonegap/phonegap-plugin-push/tree/v1.10.3) (2017-20-04)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.10.2...v1.10.3)

* 1.10.3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1e072b351056d453fd1c1d40d5fcac310f3e107c)
* :bookmark: Bumping plugin version to 1.10.3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/29df66eae54e773925e25bd92299957e4d654723)
* :bug: Handle null in getCircleBitmap (#1705) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3acdfa338d7a8b56ec4dc73c50aa9917ecb3be7c)
* :shirt: Issue #1702: The logging tag can be at most 23 characters, was 40 (PushPlugin_BackgroundActionButtonHandler) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2045873acda2e81d54b2da87cd2d10f056bd90f9)
* Shortened log tag PushPlugin_BackgroundActionButtonHandler to bring u… (#1703) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/dd707122b7aa78649fa6f5f73ba9b05436799926)
* :memo: Docs for interoperability with Firebase (#1693) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cebb6403143047192462f30c22b9510a8c6dbe21)
* fix headline (#1685) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e2e7993544eed544eb7286868b60e2e3efb6275d)
* Fix changelog date [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3140b2b00388785f8056632376b50a1d1ef67b96)
* :arrow_up: update pluginpub to 0.0.8 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cfab7d91556a38ee81550fe47e13f2662ae810db)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/027ea2c17e4b96b848ab29046efea243e6e2da27)

## [v1.10.2](https://github.com/phonegap/phonegap-plugin-push/tree/v1.10.2) (2017-04-12)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.10.1...v1.10.2)

* 1.10.2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b5a7d4ec6e64abaed65de00be3e9bac9ab25791e)
* :bookmark: Bumping plugin version to 1.10.2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5b700ad9927c401081a5de49f2a6a27ba0dfaa9a)
* :arrow_up: update pluginpub to 0.0.7 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/38564ce55e53e018c91f4063d680eedd2631b825)
* Fix the dates on the CHANGELOG (the last 2 version) (#1676) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c0f26192c906bfc1a60390333c96d5dc07433978)
* Vapid Support (#1675) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/384b60bade628035b21d23f07e284eb6e1557a10)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/97d618fb53084cfae78f397def48df791131358f)

## [v1.10.1](https://github.com/phonegap/phonegap-plugin-push/tree/v1.10.1) (2017-04-07)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.10.0...v1.10.1)

* 1.10.1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6211e8c3df8881a90d19b111a0e63f890d435df6)
* :bookmark: Bumping plugin version to 1.10.1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6d710a06681ad84c13273fe5d20feb3033ac67b6)
* :bug: Issue #1655: App opens on clearing notification (Android) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ff417925f6d6678f0fcd8315d5f4b4b08fbb9085)
* :memo: Issue #1118: Problem with notifications stacking [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d669c44a863d86d0bb73b5ae086bc2fe6f8113a9)
* :memo: Issue #1220: [Question] the hook setting seem not work [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4ef137eea370da7c225ab2a5cf63b1e97a68f4a4)
* Set get badge count android (#1644) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/630907cf3d8802bcd5d91b6bd768c989f6ef897a)
* Add plugin typings from DefinitelyTyped (#1654) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6ea70d9984e176a75602e72e1d26f5404c519e29)
* :arrow_up: Issue #1560: setApplicationIconBadgeNumber not working on Android but firing success function [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/700701fd2151667905d860228cf954301186721a)
* :memo: Issue #1618: No notification when app is closed on Android, not at all on iOS [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7f64bf0ffbddf3ef20de2fe540ec2718be5d0c23)
* :arrow_down: Issue #1560: setApplicationIconBadgeNumber not working on Android but firing success function [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e450fd7b623f27f27858a7537ec7950aa0f618b5)
* :arrow_up: Issue #1560: setApplicationIconBadgeNumber not working on Android but firing success function [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4af3472263a5125cab6f08bbb59b83bf957144ec)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ed25a3e3ad9fb6f2af63fd07957944f974eafaa1)

## [v1.10.0](https://github.com/phonegap/phonegap-plugin-push/tree/v1.10.0) (2017-03-10)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.9.4...v1.10.0)

* 1.10.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6b9b862da8566c7717dfc79dd6b32d8a7e6774d8)
* :bookmark: Bumping plugin version to 1.10.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/626e9615fbff6ea225569ab58353ac7f58aef495)
* :heavy_plus_sign: update pluginpub version [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c3c8058ffee888447017eb5d8c0f4f30cbcd090f)
* Issue #1464: Create round bitmap icon for large icon image passed in from local resource or url (#1635) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0dd0d468868f0b13c8d840c78dd89fca5920cd32)
* Receive notification only from SENDER_ID (#1484) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a0d51e54aac39b8c58e4c67080f174c0228947c9)
* Add no-cache flag to payload (#1620) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/42f8cefbd187c36534e6ab59b6611fb7f15b91f0)
* Add dismiss key to on notification data (#1621) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4259bb3cacfe2561ed44e9f16bd74f8d5ae45ae2)
* return true for old android versions, since AppOpsManager is available starting at 19 (#1634) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/945aa2ad6d266e82683e0bee86f53d258f2b31b6)
* :art: remove reference to unused String [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/75f2191632a7a51eda7510a172ae6cc9d477acb9)
* Ability to use custom keys to find message title and text on Android (#1604) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/fd366296773906c91d5f8dfa3e8ba813c7c71b85)
* :memo: Fixed URL of apples custom sound documentation (#1600) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8c93f8622eb1c453cb0c681158a07deca32bf200)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/409020c90edf04e0a37232cff8aadb070d4ccaa9)

## [v1.9.4](https://github.com/phonegap/phonegap-plugin-push/tree/v1.9.4) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.9.3...v1.9.4)

* 1.9.4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2241c7431cd60a68d2f35e7b4a5bfd797d5161b6)
* Bumping plugin version to 1.9.4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ec47932bf8fdf9cd96db076fe56306ab230048d9)
* Issue #1591: App crashes with the latest updates of Android SDK with plugin v-1.9.2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/514c21366ab37001ca323bec58261e023edaefd7)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3a218e4c68ebc1088461c2cfec966e57eaa24089)

## [v1.9.3](https://github.com/phonegap/phonegap-plugin-push/tree/v1.9.3) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.9.2...v1.9.3)

* 1.9.3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/42723d6609862aa950abce67b2a637736bdd9e99)
* :bookmark: Bumping plugin version to 1.9.3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8f7b7dc003a2bc5bab7a316b0e2b0cf475c5a449)
* :wrench: Add valid SPDX license expression [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4274d0759acd7110ed2592ed1d2ce3bf692711d3)
* :memo: Issue #1587: v2.0.0-rc2: .on('notification') event not fired when background notification in Android [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7e90853a09a2c49f29e12eed03977b08b1295387)
* :arrow_up: Issue #1560: setApplicationIconBadgeNumber not working on Android but firing success function [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3188a3907f50dcf625b7663ecf74ea9a9209d437)
* :memo: Issue #1557: push.on('notification') not triggered - Ionic [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b91e9420fb68c4efc943cc5f8ecbd81274ffcbec)
* :memo: Issue #1407: Uncaught (in promise): Error: Push plugin not found! [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/adf3eabb8871980d2dead7ecb8185ad0da1d6b46)
* Corrected merges usage to prevent possible conflicts with other plugins (#1538) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/00c67cb2c85e97dfe4f7020f28ad4d954458599f)
* :penguin: android mixpanel pushnotification suport added (#1523) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/951cb6921a717d847c279ad6896c28772c70103f)
* :memo: Making string replacement clearer [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/abdb656315bd4457c7ae43aaa52e2357df85d139)
* :bug::penguin::memo: Issue #1433: Cordova Push V5 register () crashes App when initialized with topicList [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1d5723c81ad7a1e9d76fdce22161e8e8aa8da262)
* :bug::penguin: Issue #1421: Notification delay caused by icon bitmap timeout [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/fb4b533f2b31daebc7ed57c16228458def3d2af9)
* :memo: Issue #1442: CocoaPods support vs requirement [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5912b1ea911fbe3b45a3a47ed005b7048a487ba6)
* make google_app_id non translatable string (#1485) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d9890fa52ecdb41b344f06f1dd081d05ea784bea)
* :penguin::bug: Issue #1474: Android: force-start starts the app in Foreground instead of Background [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2bb5f53a8478353ed1f5f97756adff336fb9a710)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e02ff6703a53cb18e53060e2d7f6f64ebc8588b6)

## [v2.0.0-rc2](https://github.com/phonegap/phonegap-plugin-push/tree/v2.0.0-rc2) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v2.0.0-rc1...v2.0.0-rc2)

* 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/67041a994d70fd3a04149003607b88947e8cc994)
* Bumping plugin version to 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7dea689ba17ebb901ee12da62801f051a99cc368)
* :apple::bug: Issue #1497: App crashes after refreshing when using FCM (v2) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ea92e039b1d7640b70ca94e5f8748e7d2abbf13a)
* :pencil2: fixing error in CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/498bb038799bd687d8c492154bd3b34d72edd322)
* :memo: Using a newer version on the examples [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/74aba315b4cbb1e06c902e76891bce5582cbe690)
* :penguin::memo::bug: Issue #1470: Cannot install phonegap-plugin-push on master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e2f8a62c431af26c4d2fa487daa704067a088643)
* :bug::apple::wrench: Fixing a merge issue where aps-environment was accidentally removed [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cb9839740c4cbff5711224eed4b91b55aba77612)
* :bug::penguin::memo::arrow_up: #1460: Build Issue Android [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1bef8b37ef7bc017571924edc9e05fe09cd25e29)
* :bug::apple: Issue #1461: App crashes when initialising with topics (v2.0.0-rc1) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ca5c281897c664b0bd98097ec2fc8c19c33b2c63)
* Fixing my merge error for PR #1378 Optional event emit instead of function call for action buttons [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/170d7dd43fe047c6caf84ec0f59da6c2c0cdeb6f)

## [v2.0.0-rc1](https://github.com/phonegap/phonegap-plugin-push/tree/v2.0.0-rc1) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.9.1...v2.0.0-rc1)

* 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/46a44f92ca8f94c991a564a5a8ff1e424c4b7f7f)
* :bookmark: Bumping plugin version to 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b952330124ab76d6a8ec88ebdb7eac0a614f8c38)
* :wrench: add tern to gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6ec1beb2ab13d6333122b76122ae4eb2e60dfb55)
* :hammer::wrench::arrow_up: Use Babel to transpile ES2015 code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/fab033596c66ee1c9594d404fec8473a4dd41e77)
* :wrench: Add browser platform back for FCM branch [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/07f28d8dbc477faeb2a8dd8997fde0d088dd191a)
* Pin FCM to 9.8.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ba16ce363a198edd0d190e9603a5e1363289a893)
* Add empty google services plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a47af30d32c6921b484c1c129cd60582e1b3047b)
* :bug: Issue #1188: Strings.xml google_app_id conflict with google-services.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c9f4a5d6d6f18082ae9e5a533a700cf3662c2739)
* :memo: Update to using fcm-node [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/368f6cbb8095fd742bb39308e02fac7f89379f18)
* Issue #689: Remove sender id from PushNotification init iOS options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a3e0eefe09359612d6757d4598eba69e3d68a96b)
* Issue #689: Remove sender id from PushNotification init Android options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d6d527628f8811ab6781591b3c186ce2732c9f37)
* Refactor GCMIntentService to FCMService [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/26b0369f148976e4227f73f5883658a726f825dd)
* Fix topic subscription and unsubscription on FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b4d850028b088c38bbd11c1899e28ea69b1c391e)
* Fix rebase errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/417be691c8131f006a7f1bd49bc171faa36ee872)
* Use CocoaPods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e34ddc03b01e676382a6d70e1e750a4e64ca6d62)
* fixed registration and removed unused code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/732a2bd8efbfbea696db6951439e2472d6dc8e6f)
* Added hook and resource file to copy GoogleService-Info.plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8d44ccfdfe91831140e4e972d6879b6330a1c613)
* Changed code to work with FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a18d50324ab96945db382539ce2ed7a287bed840)
* Added .framework files as custom frameworks [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/72875ac0aef0f9d00de6413e0dea4d7533c5eaef)
* Removed GCM files and added FCM files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2bd37d1b31ca0b2c76c89a04a803b22186d1f8ad)
* Fixed empty token on android first run (#1008) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/fda1f905c4364a3ac100486dc639fdd5c3bae9ca)
* Added partial Android FCM support (#975) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b6acbfeacf851bab256962616ba2bd93150982ba)
* :memo: Issue #1235: SecurityError: Only secure origins are allowed (see: https://goo.gl/Y0ZkNV) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5241a6f3868b4b15f79c6d5c0b5b5ea45e6301f9)
* :memo: Issue #1415: What is the id in push.finish? [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2660b51da66e791ff342d027ea6afa4313281e28)
* :memo: Issue #1420: Update PLATFORM_SUPPORT.md [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/60ad23affaf2dc4c9c2bf48b6cbb702b0217aeb7)
* :memo: Adding more emoji for commit messages [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/eb6b7b7d52770769719392b9b5226ee9a7caef75)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f38fd3d4d9f5f4f8de602b6aa07089b706884ca5)

## [v2.0.0-rc2](https://github.com/phonegap/phonegap-plugin-push/tree/v2.0.0-rc2) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v2.0.0-rc1...v2.0.0-rc2)

* 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/67041a994d70fd3a04149003607b88947e8cc994)
* Bumping plugin version to 2.0.0-rc2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7dea689ba17ebb901ee12da62801f051a99cc368)
* :apple::bug: Issue #1497: App crashes after refreshing when using FCM (v2) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ea92e039b1d7640b70ca94e5f8748e7d2abbf13a)
* :pencil2: fixing error in CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/498bb038799bd687d8c492154bd3b34d72edd322)
* :memo: Using a newer version on the examples [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/74aba315b4cbb1e06c902e76891bce5582cbe690)
* :penguin::memo::bug: Issue #1470: Cannot install phonegap-plugin-push on master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e2f8a62c431af26c4d2fa487daa704067a088643)
* :bug::apple::wrench: Fixing a merge issue where aps-environment was accidentally removed [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cb9839740c4cbff5711224eed4b91b55aba77612)
* :bug::penguin::memo::arrow_up: #1460: Build Issue Android [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1bef8b37ef7bc017571924edc9e05fe09cd25e29)
* :bug::apple: Issue #1461: App crashes when initialising with topics (v2.0.0-rc1) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ca5c281897c664b0bd98097ec2fc8c19c33b2c63)
* Fixing my merge error for PR #1378 Optional event emit instead of function call for action buttons [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/170d7dd43fe047c6caf84ec0f59da6c2c0cdeb6f)

## [v2.0.0-rc1](https://github.com/phonegap/phonegap-plugin-push/tree/v2.0.0-rc1) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.9.1...v2.0.0-rc1)

* 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/46a44f92ca8f94c991a564a5a8ff1e424c4b7f7f)
* :bookmark: Bumping plugin version to 2.0.0-rc1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b952330124ab76d6a8ec88ebdb7eac0a614f8c38)
* :wrench: add tern to gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6ec1beb2ab13d6333122b76122ae4eb2e60dfb55)
* :hammer::wrench::arrow_up: Use Babel to transpile ES2015 code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/fab033596c66ee1c9594d404fec8473a4dd41e77)
* :wrench: Add browser platform back for FCM branch [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/07f28d8dbc477faeb2a8dd8997fde0d088dd191a)
* Pin FCM to 9.8.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ba16ce363a198edd0d190e9603a5e1363289a893)
* Add empty google services plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a47af30d32c6921b484c1c129cd60582e1b3047b)
* :bug: Issue #1188: Strings.xml google_app_id conflict with google-services.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c9f4a5d6d6f18082ae9e5a533a700cf3662c2739)
* :memo: Update to using fcm-node [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/368f6cbb8095fd742bb39308e02fac7f89379f18)
* Issue #689: Remove sender id from PushNotification init iOS options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a3e0eefe09359612d6757d4598eba69e3d68a96b)
* Issue #689: Remove sender id from PushNotification init Android options [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d6d527628f8811ab6781591b3c186ce2732c9f37)
* Refactor GCMIntentService to FCMService [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/26b0369f148976e4227f73f5883658a726f825dd)
* Fix topic subscription and unsubscription on FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b4d850028b088c38bbd11c1899e28ea69b1c391e)
* Fix rebase errors [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/417be691c8131f006a7f1bd49bc171faa36ee872)
* Use CocoaPods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e34ddc03b01e676382a6d70e1e750a4e64ca6d62)
* fixed registration and removed unused code [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/732a2bd8efbfbea696db6951439e2472d6dc8e6f)
* Added hook and resource file to copy GoogleService-Info.plist [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8d44ccfdfe91831140e4e972d6879b6330a1c613)
* Changed code to work with FCM [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a18d50324ab96945db382539ce2ed7a287bed840)
* Added .framework files as custom frameworks [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/72875ac0aef0f9d00de6413e0dea4d7533c5eaef)
* Removed GCM files and added FCM files [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2bd37d1b31ca0b2c76c89a04a803b22186d1f8ad)
* Fixed empty token on android first run (#1008) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/fda1f905c4364a3ac100486dc639fdd5c3bae9ca)
* Added partial Android FCM support (#975) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b6acbfeacf851bab256962616ba2bd93150982ba)
* :memo: Issue #1235: SecurityError: Only secure origins are allowed (see: https://goo.gl/Y0ZkNV) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/5241a6f3868b4b15f79c6d5c0b5b5ea45e6301f9)
* :memo: Issue #1415: What is the id in push.finish? [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2660b51da66e791ff342d027ea6afa4313281e28)
* :memo: Issue #1420: Update PLATFORM_SUPPORT.md [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/60ad23affaf2dc4c9c2bf48b6cbb702b0217aeb7)
* :memo: Adding more emoji for commit messages [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/eb6b7b7d52770769719392b9b5226ee9a7caef75)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f38fd3d4d9f5f4f8de602b6aa07089b706884ca5)

## [v1.9.2](https://github.com/phonegap/phonegap-plugin-push/tree/v1.9.2) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.9.1...v1.9.2)

* 1.9.2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ef3de3281205b1fd56c57c71db31dc06a95da7a9)
* :bookmark: Bumping plugin version to 1.9.2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/24650bad4db49525505e9a2624ff1b5500e6b3ef)
* Optional event emit instead of function call for action buttons (#1378) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e92e951e759fe64d17d01e152575b6262973380a)
* Ensures foreground is true when inline is set to true and Android version is earlier than N (#1459) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/dd61ec34c0ca5c3fadf6797a8e192b9343324f68)

## [v1.9.1](https://github.com/phonegap/phonegap-plugin-push/tree/v1.9.1) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.9.0...v1.9.1)

* 1.9.1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6178688d10fc9fd5770795e5caa3f402d3fec574)
* Bumping plugin version to 1.9.1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4d0d2d0c0f19305189bbc7db11c56abfcb0e629a)
* :bug: Issue #1412: push.subscribe 'not a function' error [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1c0c0bcac7ae4a0fe8a7c54e2f00fdba90ff5207)
* :memo: Add emoji guide to CONTRIBUTING [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/dce845fde55429c36a885a4711e04a73904ab9c0)
* Issue #1342: Fail to add 1.9.0 to ios platform [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/7481e286607dacf01327c9f6d5a7c21acc5eeba1)
* Issue #1402: force-start:1 brings a killed application to foreground, not background [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/256f28fa08da4aab9691d628a3150022b67da02d)
* Issue #1400: Version 1.9.0 on PGB not working [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/c45228a892c602b8616d165ccf142eac0ff2f7f2)
* Merge pull request #1398 from getlarky/android-badge-documentation [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6884df95602a15c219846bbcdb01ce1285d660bb)
* Warn about android badge support in documentation [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2842de212b11a76580865c6ee0d6a1b7b42d030e)
* Enahancement : Add custom permission for PushHandlerActivity (#1362) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e15fdc442030648daabc79a44ae72b2d14b7c1d2)
* Issue #1248: upgrade ShortcutBadger version to latest [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4ab1cf0f0747b1f122f1f43c4af67db37dd0e443)
* Merge pull request #1361 from pataar/master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/29ec1e241036611c6a3a2c71d2a61860427be5d0)
* Fix minor typos in INSTALLATION.md [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/840b0e06cb2643635cfb882de9a35202799f3953)
* Add mimimum cordova versions to installation docs [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/dcb528384dfd7f65410545b842e16ef89277ff7b)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e268a0dceff248b703ae75ff7923b7b3d673aeee)

## [v1.9.0](https://github.com/phonegap/phonegap-plugin-push/tree/v1.9.0) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.8.4...v1.9.0)

* 1.9.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e5b7f22299d900a37064a783da43905ad73c58bf)
* Bumping plugin version to 1.9.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/dc6a11db4157e1070e48e073a8a78401f185d324)
* Prepare for 1.9.0 release [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0f889bfb5e612ef3ffbc1466deabfe9eb99b760b)
* Update gitignore [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d70bad64564444c01e59ff494b8ba09d190d3dbb)
* Bumping plugin version to 1.9.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/600993e7739a0a84ef77b60c4a1457f8aea084b6)
* Issue #1154: Register fail iOS 10 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e6013d49ecf0025be10fb6bb87152ee4025b5df4)
* Issue #1337: Build failed, invalid package.json [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8631666e4654fd6acafa6cf160cc59424e912ceb)
* Set default SENDER_ID [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/82ca365f4d6d91b18fc28c338a647a2622e60f6e)
* Issue #158: Notification Event Not Firing When Closed Through App Launcher [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ca18653d6ff332db41f48824a2d65bd2699ed8bc)
* Merge branch 'master' of https://github.com/hanicker/phonegap-plugin-push into hanicker-master [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/43402909d3b2d5c6ff518cc69e401dc918b585aa)
* Update plugin to use GCM Cocoapods <framework> reference in plugin.xml (#1183) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b639d83fe125d5b77720d130ccec53af3a5f3d91)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e4779de2a5996703ba70656630f35d79415d1af8)
* feat(forced restart, notify javascript) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8c03beff9a5a83927b7020ee04c3ed541de04edd)
* restart application after force close (#158 #333) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8b7c972dbf617f22218c178d74368b35521eecb9)

## [v1.8.4](https://github.com/phonegap/phonegap-plugin-push/tree/v1.8.4) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.8.3...v1.8.4)

* 1.8.4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4b18505a2c30e17564c0e80060f0524968aa0d40)
* Bumping plugin version to 1.8.4 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/046c727f58a6fa675a5f49c10334095cd4282884)
* Issue #1251: [Android] deviceId persists between uninstalls, but is invalid after an uninstall [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8a7bbe5cba186d9685f31adc07e25bd908409498)
* Merge pull request #1323 from hung-doan/issue-1319 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/021f0abd9a49fb83d19faca3ffb7d142759bb01a)
* Update GCM to 9.8+ issue #1319 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/450e0e01b9bcd747a49081c4a0d6ce998c37478a)
* Support Twilio Notify (#1306) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a9bb3bf0a2ca57f68eafc39070fc125746bbbb23)
* Cache multiple Android action button pushes if app is not running (#1272) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d0547bab04c292024dc6ed41939590fba01115ff)
* Add sub/unsub tests [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3bf9ff5f04e10622d7d9ff47a9bd57a829ee9eef)
* Add features: push.subscribe, push.unsubscribe (issue #1040) (#1227) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/bb0d3ed087e13e24af57e682776930cea2f577a8)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cf7ce8e716fe60de121634abc164b509100a9d15)

## [v1.8.3](https://github.com/phonegap/phonegap-plugin-push/tree/v1.8.3) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.8.2...v1.8.3)

* 1.8.3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a0cfba9f85b7d7dfa3c244c9e78a03872ff938f9)
* Bumping plugin version to 1.8.3 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/cc7d5abe55957cebfdd3b39ba670f8093bdac564)
* Update pluginpub version [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0d8548d3166b7b4d34b32944b800c96e4aadf70a)
* Issue#1282 Show app name if title is empty (#1285) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/3d7a3a39fb931aafa86be6b1568a682133c36de7)
* Note about background app refresh (#1267) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4f13533deb0c1b927dab9d9cddb13c53fdefd9b0)
* Issue #1213: XDK instructions link for www template is incorrect (#1283) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d2ede2bab4cd6462f7dffc4e2bc733d585107e89)
* Merge pull request #1277 from dannywillems/patch-1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ea5ae88f3fcfa8fc605cf2c974b52d793bd2c4c9)
* Unused variable app. Caused warnings. [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/09dedb43481d207d0b17de4eb26c5a1904d08f65)
* Issue #1254: [Question] Is it possible to get more than 3 action buttons on Android notifications? [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d8ab6665d878c8dec3ff9914c11a9329c8a415e7)
* Use unique pending intent request code to enable multiple... (#1225) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/475883833556eb001e2e0adb986bd96b78bdcb2f)
* Add `cordovaDependencies` section to package.json (#1232) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/6b343e78e9bfca921cef78eb504755477ecaeff9)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/14afb94126acb51a6f10a2094f7f391f2f17dee5)

## [v1.8.2](https://github.com/phonegap/phonegap-plugin-push/tree/v1.8.2) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.8.1...v1.8.2)

* 1.8.2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b47d519abff667400c4863fe90a27ae88e3c0671)
* Bumping plugin version to 1.8.2 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/738735ddcd9b60014beb4207c3ccdcd30ba7a803)
* Localization from resources (#1196) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/2de9fc2827a18941ae6040e90ea0da5dc97652d8)
* Issue #1199: iOS 10 is not firing the 'notification' event after clicking on a notification when the app is in hibernate [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e711b09b15a8e85156e9158026509522859c7900)
* Use unique pending intent request code to enable multiple simultaneous notifications with action buttons (#1216) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1fe640637fd67ec0ce2cf50ee1cab793ed01cfb7)
* (doc) Fixing `ios.catetories` type in api reference. [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0a93fab9cec5ac2a2b813090958054e8e7b15f9a)
* Issue #1155: [doc] Explain usage of the top level "priority" in GCM notifications [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ddaf48e089dc3cb46f9bd9f5e4678a2118b14a48)
* Issue #1121: Notification is not shown on ios device [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/432761202c43a1f2a4ba9c643df78b81b87d66fa)
* Issue #1160: data.additionalData.[Object] as 'undefined' in iOS [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/946052a895268155f56a7a3a1006d019599b46f9)
* Few edits (#1179) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f90b4bbf0e78adaa029737cf70b146ec97d09015)
* Adding workshop tutorial link to the README (#1169) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/59800a930557bd864e527404e1035c2d8ac149c9)
* added Azure server-side example (#1124) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/0bd4d0e612f6f1c62e33d147564b7e495436f15e)
* Update appxmanifests with ToastCapable=true after plugin install (#1158) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/eeefc036a8600bea80135b6b14241509853444ab)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/30d3d5dc733c80bc1687e5eff28d0614a0f38e51)

## [v1.8.1](https://github.com/phonegap/phonegap-plugin-push/tree/v1.8.1) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.8.0...v1.8.1)

* 1.8.1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/95c27d29ef37bfd750972561022db53de256840d)
* Bumping plugin version to 1.8.1 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e4ba55ff310d4d931503de2547738cc169b6f968)
* [Windows] Added a check on activation context existence (#1129) [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/151c8cd97aa71742798e969dd9e6c2208b8c1f15)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4cd4ee00e2eb081d0d29d001e580009fba5c341a)

## [v1.8.0](https://github.com/phonegap/phonegap-plugin-push/tree/v1.8.0) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/v1.7.4...v1.8.0)

* 1.8.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d5a8480e6e230c959d8079554a6366f3605cb97e)
* Bumping plugin version to 1.8.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/f8c784536e40ce6dddf14edeff7ad2a9ee944156)
* Check that serviceWorker exists before unregistering [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/ef004002d60d8028ed6aad2cef79d4d8ac6aed49)
* Populate additionalData on browser platform [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/63a29cdbbc0cd0374552d51e0e2d2217f5361f79)
* Issue #683: Support Android N inline reply actions [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e835ca31ac02a3455ece8c96938260935e2e7100)
* Issue #1109: Installation.MD has a mistake [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/89834b78a63ad6c927295eb5699204a0ccb49a73)
* Use push server DELETE route to remove browser keys [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4615ea694bd5858bfdb8553c9a9390e1e30c2c36)
* [chore] fix mis-spelling [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/efdef52deec4f7e8b6b4ee87460bd87cc0479c74)
* Browser: always call success on unimplemented methods [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/1b8bf267674508361c68fb03b12f97608e87456b)
* Update push url [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e1d8774f760d7a72a547d68e1b2ac367572e2b6d)
* Merge branch 'browser' [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/a2ad8da9a25a02a1b674e6adf0a37e18cde185ab)
* Add browser platform support [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e8e0fe47001e461cf7fb7274d4c2b3dc687cd90e)
* Issue #1080: clearAllNotifications not working on iOS [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/4bea2a7469ab2bd677e12d440b457e1d5383b1f0)
* [whoops] revert accidental commit of a pluginpub test [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/da301e8a43a0a659947294c9d94f7fd09f5ac4b2)
* [doc] remove deprecation notice on hasPermission [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/d4b79a81c5ad85a4b566e5a23a19b10fa78dcf17)
* Updating CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/8512a6cb7be3c0bb2b5db813c7aaff4c49fc52a2)
* Bumping plugin version to 1.9.0 [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/b015543b8a06526b7b70357407e635c180c473ee)
* Update CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-push/commit/e6f87a50d9f9cb3bd7c9ba599b3d3afbc0fd7aaf)

## [1.7.4](https://github.com/phonegap/phonegap-plugin-push/tree/v1.7.4) (2016-07-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.7.3...v1.7.4)

**Closed issues:**

* FYI: Resumed iOS 10 Notifications results in an error (fix problem on iOS) [\#1002](https://github.com/phonegap/phonegap-plugin-push/issues/1002)

## [1.7.3](https://github.com/phonegap/phonegap-plugin-push/tree/1.7.3) (2016-07-06)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.7.2...1.7.3)

**Closed issues:**

* Implemented clearBadge for Android [\#1030](https://github.com/phonegap/phonegap-plugin-push/issues/1030)
* update badge number even if the app is totally closed [\#1027](https://github.com/phonegap/phonegap-plugin-push/issues/1027)
* Documentation issue of Android pictures push [\#1028](https://github.com/phonegap/phonegap-plugin-push/issues/1028)
* [iOS] unregister for a topic: parameter is not consistent between ios/android [\#1029](https://github.com/phonegap/phonegap-plugin-push/issues/1029)
* [doc] Error installing in IOS, version requirement: >=4.1.0 [\#1047](https://github.com/phonegap/phonegap-plugin-push/issues/1047)

## [1.7.2](https://github.com/phonegap/phonegap-plugin-push/tree/1.7.2) (2016-06-24)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.7.1...1.7.2)

**Closed issues:**

* FYI: Resumed iOS 10 Notifications results in an error [\#1002](https://github.com/phonegap/phonegap-plugin-push/issues/1002)

## [1.7.1](https://github.com/phonegap/phonegap-plugin-push/tree/1.7.1) (2016-06-17)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.7.0...1.7.1)

**Closed issues:**

* Update docs for Android badges [\#982](https://github.com/phonegap/phonegap-plugin-push/issues/982)
* visibility not working [\#987](https://github.com/phonegap/phonegap-plugin-push/issues/982)
* Revert pinning of support-v13 [\#983](https://github.com/phonegap/phonegap-plugin-push/issues/983)

## [1.7.0](https://github.com/phonegap/phonegap-plugin-push/tree/1.7.0) (2016-06-06)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.6.4...1.7.0)

**Closed issues:**

* disable notification's sounds on a per notification basis [\#885](https://github.com/phonegap/phonegap-plugin-push/issues/885)
* Android GCM Action Buttons lack of documentation [\#884](https://github.com/phonegap/phonegap-plugin-push/issues/884)
* Android double on('notification') fired [\#828](https://github.com/phonegap/phonegap-plugin-push/issues/828)
* Device should register, Push Notification should receive on IOS (IntelXDK) [\#926](https://github.com/phonegap/phonegap-plugin-push/issues/926)
* Use cordova-ios 4.1.0 in Milestone 1.7.0 [\#751](https://github.com/phonegap/phonegap-plugin-push/issues/751)
* Badge on android [\#190](https://github.com/phonegap/phonegap-plugin-push/issues/190)
* JS error in Success callbackId: PushNotifiation###.. whenever a notification is sent to the device [\#824](https://github.com/phonegap/phonegap-plugin-push/issues/824)
* coldstart flag always set to true if the app has been opened through an alert, on ios 9.3.1, plugin version 1.6.2 cordova 6.1.0 cordova ios 4.1.1 [\#795](https://github.com/phonegap/phonegap-plugin-push/issues/795)
* Is there any way to clear notifications out from the app? [\#346](https://github.com/phonegap/phonegap-plugin-push/issues/346)
* Show contents of notification when phone is locked [\#750](https://github.com/phonegap/phonegap-plugin-push/issues/750)
* PushPlugin.m init() should send pending notification when js side is ready [\#658](https://github.com/phonegap/phonegap-plugin-push/issues/658)

## [1.6.4](https://github.com/phonegap/phonegap-plugin-push/tree/1.6.4) (2016-05-24)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.6.3...1.6.4)

**Closed issues:**

* hasPermission() for windows [\#874](https://github.com/phonegap/phonegap-plugin-push/issues/874)
* Latest Play store service breaks phonegap-plugin-push [\#909](https://github.com/phonegap/phonegap-plugin-push/issues/909)

## [1.6.3](https://github.com/phonegap/phonegap-plugin-push/tree/1.6.3) (2016-04-27)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.6.2...1.6.3)

**Fixed bugs:**

* \[Android\] Can't install multiple apps using this plugin \(v1.6.x\) [\#768](https://github.com/phonegap/phonegap-plugin-push/issues/768)
* JS error in "Success callbackId: PushNotifiation\#\#\#.." whenever a notification is sent to the device [\#824](https://github.com/phonegap/phonegap-plugin-push/issues/824)

**Closed issues:**

* Move example directory to a phonegap template [\#832](https://github.com/phonegap/phonegap-plugin-push/issues/832)
* va [\#830](https://github.com/phonegap/phonegap-plugin-push/issues/830)
* does not create the notification bar [\#821](https://github.com/phonegap/phonegap-plugin-push/issues/821)
* Did not show notification in status bar for Xiomi Redmi Note 3 [\#790](https://github.com/phonegap/phonegap-plugin-push/issues/790)
* PushNotification.hasPermission not working as expected [\#789](https://github.com/phonegap/phonegap-plugin-push/issues/789)

## [1.6.2](https://github.com/phonegap/phonegap-plugin-push/tree/1.6.2) (2016-04-06)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.6.1...1.6.2)

**Fixed bugs:**

* Unexpected / Broken Android Behavior [\#767](https://github.com/phonegap/phonegap-plugin-push/issues/767)

**Closed issues:**

* Android: JSON Exception No Value Found for Sender [\#781](https://github.com/phonegap/phonegap-plugin-push/issues/781)
* Should I call init\(\) every time the app is launched?? [\#777](https://github.com/phonegap/phonegap-plugin-push/issues/777)
* Request: extend action buttons to wearables [\#776](https://github.com/phonegap/phonegap-plugin-push/issues/776)
* After upgrade: 601 duplicate symbols for architecture i386 [\#769](https://github.com/phonegap/phonegap-plugin-push/issues/769)
* push plugin [\#766](https://github.com/phonegap/phonegap-plugin-push/issues/766)
* Documentation mentions old name for dependency [\#763](https://github.com/phonegap/phonegap-plugin-push/issues/763)
* push.on\('notification'\) callback is not called on coldstart on iOS [\#758](https://github.com/phonegap/phonegap-plugin-push/issues/758)
* plugin doesn't work in background or when app not running on kitkat 4.4.2 [\#754](https://github.com/phonegap/phonegap-plugin-push/issues/754)
* App Icon not displayed in tray using build.phonegap and cli-6.0.0 \[ios\] [\#753](https://github.com/phonegap/phonegap-plugin-push/issues/753)
* push.on\('registration'\) event not called on IOS. [\#752](https://github.com/phonegap/phonegap-plugin-push/issues/752)
* catch 22 when trying to use this plug [\#741](https://github.com/phonegap/phonegap-plugin-push/issues/741)
* push.setApplicationIconBadgeNumber not working in background [\#736](https://github.com/phonegap/phonegap-plugin-push/issues/736)
* Strange issue while debugging in Safari Inspector [\#733](https://github.com/phonegap/phonegap-plugin-push/issues/733)

## [1.6.1](https://github.com/phonegap/phonegap-plugin-push/tree/1.6.1) (2016-03-23)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.6.0...1.6.1)

**Fixed bugs:**

* App crashes on notification when in background [\#715](https://github.com/phonegap/phonegap-plugin-push/issues/715)

**Closed issues:**

* Memory Leak when call `push.on\('registration'`. so `Push Plugin register called` not called [\#743](https://github.com/phonegap/phonegap-plugin-push/issues/743)
* l [\#739](https://github.com/phonegap/phonegap-plugin-push/issues/739)
* \[testing issue template\] ignore me [\#717](https://github.com/phonegap/phonegap-plugin-push/issues/717)
* Incorrect init option "vibration" in a few ios samples on PAYLOAD.md [\#713](https://github.com/phonegap/phonegap-plugin-push/issues/713)
* android M wear case [\#691](https://github.com/phonegap/phonegap-plugin-push/issues/691)
* Incorrect document detailing \(PHONEGAP_BUILD.md\) [\#686](https://github.com/phonegap/phonegap-plugin-push/issues/686)
* Windows Phone 8.1, not fired plugin methods [\#526](https://github.com/phonegap/phonegap-plugin-push/issues/526)

## [1.6.0](https://github.com/phonegap/phonegap-plugin-push/tree/1.6.0) (2016-03-09)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.5.3...1.6.0)

**Implemented enhancements:**

* Background processing with coldstart on iOS [\#583](https://github.com/phonegap/phonegap-plugin-push/issues/583)

**Fixed bugs:**

* Android: notification does not dismiss after selection button \(1.6.x dev\) [\#610](https://github.com/phonegap/phonegap-plugin-push/issues/610)

**Closed issues:**

* XDK doesn't work with v1.5.x. Are you going to have a non-gradle version for v1.5.x [\#675](https://github.com/phonegap/phonegap-plugin-push/issues/675)
* emoji support [\#668](https://github.com/phonegap/phonegap-plugin-push/issues/668)
* iOS sound not found \[edited with new debug info\] [\#667](https://github.com/phonegap/phonegap-plugin-push/issues/667)
* Push notification register APN to GCM Problem [\#665](https://github.com/phonegap/phonegap-plugin-push/issues/665)
* IOS Never fire the registration event [\#659](https://github.com/phonegap/phonegap-plugin-push/issues/659)
* Badge count inaccurate [\#651](https://github.com/phonegap/phonegap-plugin-push/issues/651)
* Android Icon options in phonegap-plugin-push@1.2.3 [\#648](https://github.com/phonegap/phonegap-plugin-push/issues/648)
* Getting same registration id when re-register [\#641](https://github.com/phonegap/phonegap-plugin-push/issues/641)
* Callback not called unless you register to GCM everytime you open the app [\#626](https://github.com/phonegap/phonegap-plugin-push/issues/626)
* How to make GCM show alert automatically ios [\#602](https://github.com/phonegap/phonegap-plugin-push/issues/602)
* Shoddy image for notification icon,status bar icon? [\#587](https://github.com/phonegap/phonegap-plugin-push/issues/587)
* ERROR: Plugin 'PushNotification' not found [\#568](https://github.com/phonegap/phonegap-plugin-push/issues/568)
* ar [\#533](https://github.com/phonegap/phonegap-plugin-push/issues/533)
* No sound and vibration for GCM when built with Cordova but Ok with PhoneBuild [\#520](https://github.com/phonegap/phonegap-plugin-push/issues/520)

## [1.5.3](https://github.com/phonegap/phonegap-plugin-push/tree/1.5.3) (2016-01-14)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.5.2...1.5.3)

**Fixed bugs:**

* Android 4.1 and AppOpsManager [\#495](https://github.com/phonegap/phonegap-plugin-push/issues/495)

**Closed issues:**

* iOS Building Error [\#507](https://github.com/phonegap/phonegap-plugin-push/issues/507)
* \[FIX\] notification not fired when app is in background/killed ? Check this out [\#502](https://github.com/phonegap/phonegap-plugin-push/issues/502)
* Which Sender ID to use [\#485](https://github.com/phonegap/phonegap-plugin-push/issues/485)
* coldstart not documented [\#483](https://github.com/phonegap/phonegap-plugin-push/issues/483)
* Android: iconColor doesn't work in hexadecimal \(\#RRGGBB\) format [\#480](https://github.com/phonegap/phonegap-plugin-push/issues/480)
* Cannot find symbol variable INSTANCE_ID_SCOPE [\#477](https://github.com/phonegap/phonegap-plugin-push/issues/477)
* notification event not fired on cold start on Android 5 [\#469](https://github.com/phonegap/phonegap-plugin-push/issues/469)
* Push notifications not working with iPhone6 + ios 9.2? [\#462](https://github.com/phonegap/phonegap-plugin-push/issues/462)
* UTF8 support on android: the notification text \(on android only\) is shown with "???" [\#461](https://github.com/phonegap/phonegap-plugin-push/issues/461)
* example application? [\#460](https://github.com/phonegap/phonegap-plugin-push/issues/460)
* build fail in ios - version 1.5.2 [\#458](https://github.com/phonegap/phonegap-plugin-push/issues/458)
* Getting NotRegistered error when sending GCM push notification, but the device never unregistered [\#419](https://github.com/phonegap/phonegap-plugin-push/issues/419)
* Changelog neglected [\#412](https://github.com/phonegap/phonegap-plugin-push/issues/412)

## [1.5.2](https://github.com/phonegap/phonegap-plugin-push/tree/1.5.1) (2015-12-21)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.5.1...1.5.2)

**Fixed bugs:**

* notification_applicationDidBecomeActive method [\#447](https://github.com/phonegap/phonegap-plugin-push/issues/447)

## [1.5.1](https://github.com/phonegap/phonegap-plugin-push/tree/1.5.1) (2015-12-18)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.5.0...1.5.1)

**Closed issues:**

* Unsubscribing to topics should not clear event handlers [\#443](https://github.com/phonegap/phonegap-plugin-push/issues/443)

## [1.5.0](https://github.com/phonegap/phonegap-plugin-push/tree/1.5.0) (2015-12-18)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.4.5...1.5.0)

**Fixed bugs:**

* Make sure iOS/Android can receive the same GCM payload [\#401](https://github.com/phonegap/phonegap-plugin-push/issues/401)

**Closed issues:**

* Using stacking notifications but only able to process one [\#435](https://github.com/phonegap/phonegap-plugin-push/issues/435)
* Cant find stacking docs [\#430](https://github.com/phonegap/phonegap-plugin-push/issues/430)
* Android Marshmallow 6.0 push icon [\#422](https://github.com/phonegap/phonegap-plugin-push/issues/422)
* Distinguish between 'android' or 'ios' on registration event [\#418](https://github.com/phonegap/phonegap-plugin-push/issues/418)
* Phonegap Build Issues for 1.4.X [\#417](https://github.com/phonegap/phonegap-plugin-push/issues/417)
* using in Ionic [\#416](https://github.com/phonegap/phonegap-plugin-push/issues/416)
* notification event not called on ios [\#414](https://github.com/phonegap/phonegap-plugin-push/issues/414)
* \[docs\] detail on deviceready dependency [\#410](https://github.com/phonegap/phonegap-plugin-push/issues/410)
* iOS: Plugin does not start [\#404](https://github.com/phonegap/phonegap-plugin-push/issues/404)
* Process notifications in background [\#398](https://github.com/phonegap/phonegap-plugin-push/issues/398)
* On iOS 9 the badge does not clear [\#395](https://github.com/phonegap/phonegap-plugin-push/issues/395)
* Background notification OK, but event "notification" never called [\#387](https://github.com/phonegap/phonegap-plugin-push/issues/387)
* Android action button callback not triggered [\#298](https://github.com/phonegap/phonegap-plugin-push/issues/298)

## [1.4.5](https://github.com/phonegap/phonegap-plugin-push/tree/1.4.5) (2015-12-03)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.4.4...1.4.5)

**Fixed bugs:**

* register -\> unregister lifecycle [\#368](https://github.com/phonegap/phonegap-plugin-push/issues/368)

**Closed issues:**

* jshint support [\#380](https://github.com/phonegap/phonegap-plugin-push/issues/380)
* Platform support [\#379](https://github.com/phonegap/phonegap-plugin-push/issues/379)
* Ionic implemenation [\#364](https://github.com/phonegap/phonegap-plugin-push/issues/364)
* on registration callback getting called repeatedly. [\#353](https://github.com/phonegap/phonegap-plugin-push/issues/353)
* on\("notification"\) dont fire when the app its open [\#351](https://github.com/phonegap/phonegap-plugin-push/issues/351)

## [1.4.4](https://github.com/phonegap/phonegap-plugin-push/tree/1.4.4) (2015-11-20)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.4.3...1.4.4)

**Fixed bugs:**

* InstanceID.getToken\(\) not called in an Intent [\#354](https://github.com/phonegap/phonegap-plugin-push/issues/354)
* Handle both data and notification payloads in the same GCM push [\#343](https://github.com/phonegap/phonegap-plugin-push/issues/343)
* \[INSTALL_FAILED_CONFLICTING_PROVIDER\] [\#320](https://github.com/phonegap/phonegap-plugin-push/issues/320)
* Getting "Error : Empty registration ID received from GCM" [\#315](https://github.com/phonegap/phonegap-plugin-push/issues/315)

**Closed issues:**

* xcode build fails with phonegap-plugin-push [\#358](https://github.com/phonegap/phonegap-plugin-push/issues/358)
* App crashes after adding push plugin and wikipedia app installed [\#357](https://github.com/phonegap/phonegap-plugin-push/issues/357)
* Conflict when compiling with com.google.maps [\#355](https://github.com/phonegap/phonegap-plugin-push/issues/355)

## [1.4.3](https://github.com/phonegap/phonegap-plugin-push/tree/1.4.3) (2015-11-18)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.4.2...1.4.3)

**Closed issues:**

* \[Question\] about the action's icon color [\#348](https://github.com/phonegap/phonegap-plugin-push/issues/348)
* Can I access Web API's from 'notification' handler? [\#335](https://github.com/phonegap/phonegap-plugin-push/issues/335)
* Init Error: Missing Instance ID Service [\#334](https://github.com/phonegap/phonegap-plugin-push/issues/334)
* Install issue [\#332](https://github.com/phonegap/phonegap-plugin-push/issues/332)
* Multiple Apps gets error [\#330](https://github.com/phonegap/phonegap-plugin-push/issues/330)
* Can't find variable PushNotification [\#328](https://github.com/phonegap/phonegap-plugin-push/issues/328)
* When app is in background, clicking on Android notification in shade opens app but does not trigger the on\('notification'\) event handler [\#326](https://github.com/phonegap/phonegap-plugin-push/issues/326)
* "Missing Command Error" when running in browser [\#318](https://github.com/phonegap/phonegap-plugin-push/issues/318)
* Small icon not working [\#316](https://github.com/phonegap/phonegap-plugin-push/issues/316)
* Duplicate push notifications happening on iOS when phone is unlocked [\#309](https://github.com/phonegap/phonegap-plugin-push/issues/309)
* Is it possible to add badge on Android portion. [\#308](https://github.com/phonegap/phonegap-plugin-push/issues/308)
* \[just a question\] about the priority [\#306](https://github.com/phonegap/phonegap-plugin-push/issues/306)
* Android - After unregister and reregister no notifications [\#304](https://github.com/phonegap/phonegap-plugin-push/issues/304)
* Amazon-Fireos and Blackberry 10 support [\#300](https://github.com/phonegap/phonegap-plugin-push/issues/300)
* 1.4.x not showing notification on Android [\#299](https://github.com/phonegap/phonegap-plugin-push/issues/299)
* "push.unregister" not really work in iOS [\#296](https://github.com/phonegap/phonegap-plugin-push/issues/296)

## [1.4.2](https://github.com/phonegap/phonegap-plugin-push/tree/1.4.2) (2015-11-03)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.4.1...1.4.2)

**Closed issues:**

* data.registrationId is empty string "" on register event callback [\#295](https://github.com/phonegap/phonegap-plugin-push/issues/295)

## [1.4.1](https://github.com/phonegap/phonegap-plugin-push/tree/1.4.1) (2015-11-02)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.4.0...1.4.1)

## [1.4.0](https://github.com/phonegap/phonegap-plugin-push/tree/1.4.0) (2015-10-27)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.3.0...1.4.0)

**Implemented enhancements:**

* Use Google's InstanceID API [\#188](https://github.com/phonegap/phonegap-plugin-push/issues/188)

**Closed issues:**

* How to handle a re-installed app? [\#203](https://github.com/phonegap/phonegap-plugin-push/issues/203)
* interactive push notifications? [\#266](https://github.com/phonegap/phonegap-plugin-push/issues/266)
* Empty registrationId Android [\#265](https://github.com/phonegap/phonegap-plugin-push/issues/265)
* Run callback when clicking of notification body [\#261](https://github.com/phonegap/phonegap-plugin-push/issues/261)
* Android BUILD FAILED [\#251](https://github.com/phonegap/phonegap-plugin-push/issues/251)
* Re-register [\#250](https://github.com/phonegap/phonegap-plugin-push/issues/250)
* how to work in background ? [\#249](https://github.com/phonegap/phonegap-plugin-push/issues/249)
* installing plugin [\#244](https://github.com/phonegap/phonegap-plugin-push/issues/244)
* No Sound and vibration [\#242](https://github.com/phonegap/phonegap-plugin-push/issues/242)
* Unable to build apk [\#241](https://github.com/phonegap/phonegap-plugin-push/issues/241)
* still having problems with build. [\#239](https://github.com/phonegap/phonegap-plugin-push/issues/239)
* Registering on iOS 9 [\#238](https://github.com/phonegap/phonegap-plugin-push/issues/238)
* Custom sound repeated multiple times on Android [\#237](https://github.com/phonegap/phonegap-plugin-push/issues/237)
* Android: status bar notification is not shown [\#236](https://github.com/phonegap/phonegap-plugin-push/issues/236)
* Multiple Push Notifications - phonegap build [\#234](https://github.com/phonegap/phonegap-plugin-push/issues/234)
* error: cannot find symbol String token = InstanceID.getInstance\(getApplicationContext\(\)\).getToken\(senderID, GCM\); [\#231](https://github.com/phonegap/phonegap-plugin-push/issues/231)
* Problem using "ledColor" and "VibrationPattern" [\#229](https://github.com/phonegap/phonegap-plugin-push/issues/229)
* Notificaction event receive, but not notification showing on android [\#228](https://github.com/phonegap/phonegap-plugin-push/issues/228)
* Events for registration not being fired [\#227](https://github.com/phonegap/phonegap-plugin-push/issues/227)
* 'registration' event not firing on windows phone [\#224](https://github.com/phonegap/phonegap-plugin-push/issues/224)
* Can i subscribe to a topic in using plugin? [\#219](https://github.com/phonegap/phonegap-plugin-push/issues/219)
* GCMIntentService.java:472: error: cannot find symbol iconColor [\#217](https://github.com/phonegap/phonegap-plugin-push/issues/217)
* Push Plugin registering on iOS 9 Devices but not showing Notification [\#216](https://github.com/phonegap/phonegap-plugin-push/issues/216)
* Receiving a notification "outside app" while in it? [\#213](https://github.com/phonegap/phonegap-plugin-push/issues/213)
* iOS push not working for device tokens when spaces removed [\#212](https://github.com/phonegap/phonegap-plugin-push/issues/212)
* Error: Plugin PushPlugin failed to install. [\#210](https://github.com/phonegap/phonegap-plugin-push/issues/210)
* Build error [\#205](https://github.com/phonegap/phonegap-plugin-push/issues/205)
* Android push.on\('registration', cb\) fires correctly on device, but not in emulator. [\#204](https://github.com/phonegap/phonegap-plugin-push/issues/204)
* 1.3.0 version not compatible with "crosswalk" by PGB [\#199](https://github.com/phonegap/phonegap-plugin-push/issues/199)
* How to get data on didReceiveNotification Background Process [\#198](https://github.com/phonegap/phonegap-plugin-push/issues/198)
* PushNotification is not defined in some devices [\#196](https://github.com/phonegap/phonegap-plugin-push/issues/196)
* not getting notifications on the Android device [\#195](https://github.com/phonegap/phonegap-plugin-push/issues/195)
* Installation Errors [\#186](https://github.com/phonegap/phonegap-plugin-push/issues/186)
* IOS: on registration fired twice [\#185](https://github.com/phonegap/phonegap-plugin-push/issues/185)
* Build failed with exit code 8 [\#184](https://github.com/phonegap/phonegap-plugin-push/issues/184)
* iOS: Not able to schedule local notification after adding the plugin [\#183](https://github.com/phonegap/phonegap-plugin-push/issues/183)
* How to show multiple notifications individually in android? [\#181](https://github.com/phonegap/phonegap-plugin-push/issues/181)
* iOS init option type [\#180](https://github.com/phonegap/phonegap-plugin-push/issues/180)
* Building for Android is a quest [\#179](https://github.com/phonegap/phonegap-plugin-push/issues/179)
* How do i tell if the user open the app by tapping the notification? [\#176](https://github.com/phonegap/phonegap-plugin-push/issues/176)
* IOS custom push sound when app is in background [\#175](https://github.com/phonegap/phonegap-plugin-push/issues/175)
* Hi guys please post full working procedure, I'm not able to get registration id also. Please help [\#174](https://github.com/phonegap/phonegap-plugin-push/issues/174)
* Has anyone tested this plugin on windows? [\#173](https://github.com/phonegap/phonegap-plugin-push/issues/173)

## [1.3.0](https://github.com/phonegap/phonegap-plugin-push/tree/1.3.0) (2015-09-21)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.2.3...1.3.0)

**Implemented enhancements:**

* How to use GCM 3.0 with this plugin? [\#127](https://github.com/phonegap/phonegap-plugin-push/issues/127)
* Android: possibility to send a notification with a title and without message [\#122](https://github.com/phonegap/phonegap-plugin-push/issues/122)
* Enhancement - Led, Vibration Pattern, Priority on Android [\#105](https://github.com/phonegap/phonegap-plugin-push/issues/105)

**Fixed bugs:**

* It is using in gcm data.additionalData ? [\#126](https://github.com/phonegap/phonegap-plugin-push/issues/126)
* iOS notification from cold boot [\#117](https://github.com/phonegap/phonegap-plugin-push/issues/117)
* Notification LED is not working [\#97](https://github.com/phonegap/phonegap-plugin-push/issues/97)

**Closed issues:**

* Know which version is used in build service [\#151](https://github.com/phonegap/phonegap-plugin-push/issues/151)
* Registration is not working in IOS9 [\#150](https://github.com/phonegap/phonegap-plugin-push/issues/150)
* build fail on android [\#149](https://github.com/phonegap/phonegap-plugin-push/issues/149)
* iconColor does not set icon background on Android [\#146](https://github.com/phonegap/phonegap-plugin-push/issues/146)
* Prevent windows toast notification when in foreground [\#145](https://github.com/phonegap/phonegap-plugin-push/issues/145)
* How to implement push notification for ios with this plug-in? [\#143](https://github.com/phonegap/phonegap-plugin-push/issues/143)
* After installing this plugin I can't build on Android [\#141](https://github.com/phonegap/phonegap-plugin-push/issues/141)
* version 1.2.3 [\#134](https://github.com/phonegap/phonegap-plugin-push/issues/134)
* New inbox style on android [\#131](https://github.com/phonegap/phonegap-plugin-push/issues/131)
* impossible to install the phonegap-plugin-push Error [\#130](https://github.com/phonegap/phonegap-plugin-push/issues/130)
* Hello, i am developing a cordova app which requires push notifications to be sent to users android phone, so i tried using this new phonegap push plugin as old one is deprecated, and it keeps giving me an error in console: Uncaught ReferenceError: module is not defined --- Line 154 Push.js and i dont have much experience with cordova, so can anyone assist me ? [\#128](https://github.com/phonegap/phonegap-plugin-push/issues/128)
* INVALID_REGISTRATION when http post request with to IOS [\#123](https://github.com/phonegap/phonegap-plugin-push/issues/123)
* Andriod :More than 2 notifications in status bar it is not works. [\#121](https://github.com/phonegap/phonegap-plugin-push/issues/121)
* Release notes for 1.2.x [\#119](https://github.com/phonegap/phonegap-plugin-push/issues/119)
* Google cloud messaging GCM - Push Notification not being sent \(Server Side\) [\#110](https://github.com/phonegap/phonegap-plugin-push/issues/110)

## [1.2.3](https://github.com/phonegap/phonegap-plugin-push/tree/1.2.3) (2015-09-08)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.2.2...1.2.3)

**Fixed bugs:**

* Notification not showing..... [\#101](https://github.com/phonegap/phonegap-plugin-push/issues/101)
* Same data payload for messages with action buttons [\#90](https://github.com/phonegap/phonegap-plugin-push/issues/90)

**Closed issues:**

* Notification doesn't show the app icon [\#112](https://github.com/phonegap/phonegap-plugin-push/issues/112)
* Notification doesn't show the app icon [\#111](https://github.com/phonegap/phonegap-plugin-push/issues/111)
* Issue with plugin facebook connect [\#107](https://github.com/phonegap/phonegap-plugin-push/issues/107)
* Cordova Support [\#99](https://github.com/phonegap/phonegap-plugin-push/issues/99)
* Uncaught ReferenceError: cordova is not defined, http://localhost:8100/lib/push.js, Line: 7 [\#98](https://github.com/phonegap/phonegap-plugin-push/issues/98)
* Notifications never received on Android [\#96](https://github.com/phonegap/phonegap-plugin-push/issues/96)
* How know the way the app was launched [\#95](https://github.com/phonegap/phonegap-plugin-push/issues/95)
* Android, example doesn't work when it goes into background [\#94](https://github.com/phonegap/phonegap-plugin-push/issues/94)
* Utilizing push plugin [\#91](https://github.com/phonegap/phonegap-plugin-push/issues/91)

## [1.2.2](https://github.com/phonegap/phonegap-plugin-push/tree/1.2.2) (2015-08-31)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.2.1...1.2.2)

**Closed issues:**

* PushPlugin notification icon is too big [\#88](https://github.com/phonegap/phonegap-plugin-push/issues/88)

## [1.2.1](https://github.com/phonegap/phonegap-plugin-push/tree/1.2.1) (2015-08-31)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.2.0...1.2.1)

**Implemented enhancements:**

* Question about GCM Notifications and data in the message payload [\#87](https://github.com/phonegap/phonegap-plugin-push/issues/87)

**Fixed bugs:**

* Notification callback for pushes without a message [\#80](https://github.com/phonegap/phonegap-plugin-push/issues/80)

**Closed issues:**

* Android: No notification displayed on device. Notification event never called. [\#86](https://github.com/phonegap/phonegap-plugin-push/issues/86)
* it seem no wp8 version for now [\#56](https://github.com/phonegap/phonegap-plugin-push/issues/56)

## [1.2.0](https://github.com/phonegap/phonegap-plugin-push/tree/1.2.0) (2015-08-25)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.1.1...1.2.0)

**Implemented enhancements:**

* Implement Inbox style for Android [\#74](https://github.com/phonegap/phonegap-plugin-push/issues/74)
* multi-line text support [\#63](https://github.com/phonegap/phonegap-plugin-push/issues/63)

**Fixed bugs:**

* Pushes being deleted from notification bar when cold start [\#67](https://github.com/phonegap/phonegap-plugin-push/issues/67)

**Closed issues:**

* oficial push plugin and windows and wp8 compatibility [\#71](https://github.com/phonegap/phonegap-plugin-push/issues/71)
* On Android, GCMIntentService.onError\(\) doesn't get passed to the JavaScript "error" event [\#65](https://github.com/phonegap/phonegap-plugin-push/issues/65)
* Android: add property to vibrate phone on received notification [\#61](https://github.com/phonegap/phonegap-plugin-push/issues/61)
* push.on =\> "registration" will trigger twice times that only in iOS [\#57](https://github.com/phonegap/phonegap-plugin-push/issues/57)

## [1.1.1](https://github.com/phonegap/phonegap-plugin-push/tree/1.1.1) (2015-07-27)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.1.0...1.1.1)

## [1.1.0](https://github.com/phonegap/phonegap-plugin-push/tree/1.1.0) (2015-07-27)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.0.1...1.1.0)

**Implemented enhancements:**

* iOS doesn't add foreground key [\#41](https://github.com/phonegap/phonegap-plugin-push/issues/41)
* Android: Notification icon problem [\#20](https://github.com/phonegap/phonegap-plugin-push/issues/20)
* iOS badge number [\#18](https://github.com/phonegap/phonegap-plugin-push/issues/18)
* How i can set icons for push notifications in status bar and push view in android [\#14](https://github.com/phonegap/phonegap-plugin-push/issues/14)
* Support Win8.1 + Phone 8.1 Universal Apps \(WNS\), drop support for WP8.0 \(MPNS\) [\#13](https://github.com/phonegap/phonegap-plugin-push/issues/13)

**Fixed bugs:**

* iOS only reads out "aps" payload [\#29](https://github.com/phonegap/phonegap-plugin-push/issues/29)
* Event not fired when in background [\#24](https://github.com/phonegap/phonegap-plugin-push/issues/24)
* Custom notification sound in background mode? [\#17](https://github.com/phonegap/phonegap-plugin-push/issues/17)

**Closed issues:**

* iOS only receives first notification in foreground [\#42](https://github.com/phonegap/phonegap-plugin-push/issues/42)
* Cannot register on iOS [\#30](https://github.com/phonegap/phonegap-plugin-push/issues/30)
* Fix Android paths in src folder [\#23](https://github.com/phonegap/phonegap-plugin-push/issues/23)
* PushNotification not defined [\#21](https://github.com/phonegap/phonegap-plugin-push/issues/21)
* Error trying to remove the plugin [\#19](https://github.com/phonegap/phonegap-plugin-push/issues/19)
* Handling multiple notifications on Android devices [\#12](https://github.com/phonegap/phonegap-plugin-push/issues/12)
* PGB \(build.phonegap.com\) problem [\#11](https://github.com/phonegap/phonegap-plugin-push/issues/11)
* reporting location via gcm [\#6](https://github.com/phonegap/phonegap-plugin-push/issues/6)

**Merged pull requests:**

* Updating Readme to document toast capable setting [\#47](https://github.com/phonegap/phonegap-plugin-push/pull/47) ([rakatyal](https://github.com/rakatyal))
* fix issue \#41 [\#44](https://github.com/phonegap/phonegap-plugin-push/pull/44) ([Deminetix](https://github.com/Deminetix))
* fix issue \#42 [\#43](https://github.com/phonegap/phonegap-plugin-push/pull/43) ([Deminetix](https://github.com/Deminetix))
* Adding hyperlinks to README [\#40](https://github.com/phonegap/phonegap-plugin-push/pull/40) ([rakatyal](https://github.com/rakatyal))
* Updating Readme [\#37](https://github.com/phonegap/phonegap-plugin-push/pull/37) ([rakatyal](https://github.com/rakatyal))
* Adding windows support to plugin [\#36](https://github.com/phonegap/phonegap-plugin-push/pull/36) ([rakatyal](https://github.com/rakatyal))
* Raghav/update [\#35](https://github.com/phonegap/phonegap-plugin-push/pull/35) ([rakatyal](https://github.com/rakatyal))
* Adding behavior for different notification types [\#28](https://github.com/phonegap/phonegap-plugin-push/pull/28) ([rakatyal](https://github.com/rakatyal))
* Initial commit to add support for windows universal platform [\#15](https://github.com/phonegap/phonegap-plugin-push/pull/15) ([rakatyal](https://github.com/rakatyal))

## [1.0.1](https://github.com/phonegap/phonegap-plugin-push/tree/1.0.1) (2015-06-08)

[Full Changelog](https://github.com/phonegap/phonegap-plugin-push/compare/1.0.0...1.0.1)

**Closed issues:**

* documentation "senderId" correction [\#10](https://github.com/phonegap/phonegap-plugin-push/issues/10)
* add to our ci page [\#9](https://github.com/phonegap/phonegap-plugin-push/issues/9)
* Update installation instructions [\#7](https://github.com/phonegap/phonegap-plugin-push/issues/7)

## [1.0.0](https://github.com/phonegap/phonegap-plugin-push/tree/1.0.0) (2015-06-05)

**Closed issues:**

* Update code using enabledRemoteNotificationTypes because it is “not supported in iOS 8” [\#8](https://github.com/phonegap/phonegap-plugin-push/issues/8)
* Register method not working [\#4](https://github.com/phonegap/phonegap-plugin-push/issues/4)
* Publish plugin to npm [\#3](https://github.com/phonegap/phonegap-plugin-push/issues/3)
* Update example to use new API [\#2](https://github.com/phonegap/phonegap-plugin-push/issues/2)
* Lowercase Example/ directory [\#1](https://github.com/phonegap/phonegap-plugin-push/issues/1)

\* _This Change Log was automatically generated by [github_changelog_generator](https://github.com/skywinder/Github-Changelog-Generator)_
