{"prepare_queue": {"installed": [], "uninstalled": []}, "config_munge": {"files": {}}, "installed_plugins": {"cordova-plugin-device": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-plugin-splashscreen": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-plugin-network-information": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-plugin-file": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-plugin-file-transfer": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-sqlite-storage": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-plugin-camera": {"ANDROID_SUPPORT_V4_VERSION": "27.+", "PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-plugin-media-capture": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "phonegap-plugin-push": {"ANDROID_SUPPORT_V13_VERSION": "27.+", "FCM_VERSION": "17.+", "PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-plugin-dialogs": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-plugin-vibration": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-plugin-inappbrowser": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-plugin-app-version": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "cordova-plugin-statusbar": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}}, "dependent_plugins": {"cordova-support-google-services": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}, "phonegap-plugin-multidex": {"PACKAGE_NAME": "com.floriconstructionltd.admin"}}}