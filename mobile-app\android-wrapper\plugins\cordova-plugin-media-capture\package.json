{"name": "cordova-plugin-media-capture", "version": "3.0.3", "description": "Cordova Media Capture Plugin", "types": "./types/index.d.ts", "cordova": {"id": "cordova-plugin-media-capture", "platforms": ["android", "ios", "windows", "browser"]}, "repository": {"type": "git", "url": "https://github.com/apache/cordova-plugin-media-capture"}, "bugs": {"url": "https://github.com/apache/cordova-plugin-media-capture/issues"}, "keywords": ["<PERSON><PERSON>", "media", "capture", "ecosystem:cordova", "cordova-android", "cordova-ios", "cordova-windows"], "scripts": {"test": "npm run eslint", "eslint": "eslint www && eslint src && eslint tests"}, "author": "Apache Software Foundation", "license": "Apache-2.0", "engines": {"cordovaDependencies": {">=1.4.4": {"cordova-ios": ">=4.0.0"}, "2.0.0": {"cordova-android": ">=6.3.0"}, "4.0.0": {"cordova": ">100"}}}, "devDependencies": {"eslint": "^4.0.0", "eslint-config-semistandard": "^11.0.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.3.0", "eslint-plugin-node": "^5.0.0", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1"}}