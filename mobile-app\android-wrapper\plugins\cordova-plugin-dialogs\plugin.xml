<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

<plugin xmlns="http://apache.org/cordova/ns/plugins/1.0"
           id="cordova-plugin-dialogs"
      version="2.0.2">

    <name>Notification</name>
    <description>Cordova Notification Plugin</description>
    <license>Apache 2.0</license>
    <keywords>cordova,notification</keywords>
    <repo>https://github.com/apache/cordova-plugin-dialogs</repo>
    <issue>https://github.com/apache/cordova-plugin-dialogs/issues</issue>

    <js-module src="www/notification.js" name="notification">
        <merges target="navigator.notification" />
    </js-module>

    <!-- android -->
    <platform name="android">
        <config-file target="res/xml/config.xml" parent="/*">
            <feature name="Notification">
                <param name="android-package" value="org.apache.cordova.dialogs.Notification"/>
            </feature>
        </config-file>

        <source-file src="src/android/Notification.java" target-dir="src/org/apache/cordova/dialogs" />

        <!-- android specific notification apis -->
        <js-module src="www/android/notification.js" name="notification_android">
            <merges target="navigator.notification" />
        </js-module>

    </platform>

    <!-- browser -->
    <platform name="browser">
        <js-module src="www/browser/notification.js" name="notification_browser">
            <merges target="navigator.notification" />
        </js-module>

    </platform>

     <!-- ios -->
    <platform name="ios">
        <config-file target="config.xml" parent="/*">
            <feature name="Notification">
                <param name="ios-package" value="CDVNotification"/>
            </feature>
        </config-file>
        <header-file src="src/ios/CDVNotification.h" />
        <source-file src="src/ios/CDVNotification.m" />
        <resource-file src="src/ios/CDVNotification.bundle" />
        <framework src="AudioToolbox.framework" weak="true" />
    </platform>

    <!-- windows -->
    <platform name="windows">
        <js-module src="src/windows/NotificationProxy.js" name="NotificationProxy">
            <runs />
        </js-module>

        <asset src="www/windows/notification.css" target="css/notification.css" />
    </platform>
</plugin>
