/**
 * Push Notifications Manager for Flori Construction Mobile App
 * Handles push notification registration, display, and management
 */

class NotificationManager {
    constructor() {
        this.isSupported = 'serviceWorker' in navigator && 'PushManager' in window;
        this.permission = Notification.permission;
        this.subscription = null;
        this.apiUrl = '../api';
        
        this.init();
    }

    async init() {
        if (!this.isSupported) {
            console.warn('Push notifications are not supported in this browser');
            return;
        }

        // Register service worker
        try {
            const registration = await navigator.serviceWorker.register('/erdevwe/mobile-app/sw.js');
            console.log('Service Worker registered:', registration);
            
            // Check for existing subscription
            this.subscription = await registration.pushManager.getSubscription();
            
            if (this.subscription) {
                console.log('Existing push subscription found');
                this.updateSubscriptionOnServer();
            }
            
        } catch (error) {
            console.error('Service Worker registration failed:', error);
        }
    }

    async requestPermission() {
        if (!this.isSupported) {
            throw new Error('Push notifications are not supported');
        }

        if (this.permission === 'granted') {
            return true;
        }

        if (this.permission === 'denied') {
            throw new Error('Push notifications are blocked. Please enable them in browser settings.');
        }

        const permission = await Notification.requestPermission();
        this.permission = permission;

        if (permission === 'granted') {
            await this.subscribe();
            return true;
        } else {
            throw new Error('Push notification permission denied');
        }
    }

    async subscribe() {
        if (!this.isSupported || this.permission !== 'granted') {
            throw new Error('Cannot subscribe: notifications not supported or permission denied');
        }

        try {
            const registration = await navigator.serviceWorker.ready;
            
            // VAPID public key (you'll need to generate this for production)
            const vapidPublicKey = 'BEl62iUYgUivxIkv69yViEuiBIa40HI0DLLat3eAALZ-4_aqBnETQjfHVYfNFC6rUkYwPP2O6BTjHiLmGAUDR5U';
            
            const subscription = await registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array(vapidPublicKey)
            });

            this.subscription = subscription;
            console.log('Push subscription successful:', subscription);

            // Send subscription to server
            await this.updateSubscriptionOnServer();
            
            return subscription;
            
        } catch (error) {
            console.error('Push subscription failed:', error);
            throw error;
        }
    }

    async unsubscribe() {
        if (!this.subscription) {
            return true;
        }

        try {
            const success = await this.subscription.unsubscribe();
            
            if (success) {
                this.subscription = null;
                // Remove subscription from server
                await this.removeSubscriptionFromServer();
                console.log('Push subscription removed');
            }
            
            return success;
            
        } catch (error) {
            console.error('Push unsubscription failed:', error);
            throw error;
        }
    }

    async updateSubscriptionOnServer() {
        if (!this.subscription) {
            return;
        }

        try {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                console.warn('No auth token found, cannot update subscription');
                return;
            }

            const response = await fetch(`${this.apiUrl}/notifications.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    action: 'subscribe',
                    subscription: this.subscription.toJSON()
                })
            });

            if (!response.ok) {
                throw new Error('Failed to update subscription on server');
            }

            const result = await response.json();
            console.log('Subscription updated on server:', result);
            
        } catch (error) {
            console.error('Failed to update subscription on server:', error);
        }
    }

    async removeSubscriptionFromServer() {
        try {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                return;
            }

            const response = await fetch(`${this.apiUrl}/notifications.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    action: 'unsubscribe'
                })
            });

            if (response.ok) {
                console.log('Subscription removed from server');
            }
            
        } catch (error) {
            console.error('Failed to remove subscription from server:', error);
        }
    }

    showLocalNotification(title, options = {}) {
        if (this.permission !== 'granted') {
            console.warn('Cannot show notification: permission not granted');
            return;
        }

        const defaultOptions = {
            icon: '/erdevwe/assets/images/logo-192.png',
            badge: '/erdevwe/assets/images/logo-72.png',
            vibrate: [200, 100, 200],
            tag: 'flori-construction',
            renotify: true,
            requireInteraction: false,
            ...options
        };

        return new Notification(title, defaultOptions);
    }

    async getNotificationHistory() {
        try {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                throw new Error('Authentication required');
            }

            const response = await fetch(`${this.apiUrl}/notifications.php?action=history`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch notification history');
            }

            return await response.json();
            
        } catch (error) {
            console.error('Failed to get notification history:', error);
            throw error;
        }
    }

    async markNotificationAsRead(notificationId) {
        try {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                throw new Error('Authentication required');
            }

            const response = await fetch(`${this.apiUrl}/notifications.php`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    action: 'mark_read',
                    notification_id: notificationId
                })
            });

            if (!response.ok) {
                throw new Error('Failed to mark notification as read');
            }

            return await response.json();
            
        } catch (error) {
            console.error('Failed to mark notification as read:', error);
            throw error;
        }
    }

    // Utility function to convert VAPID key
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }

    // Check if notifications are enabled
    isEnabled() {
        return this.permission === 'granted' && this.subscription !== null;
    }

    // Get subscription status
    getStatus() {
        return {
            supported: this.isSupported,
            permission: this.permission,
            subscribed: this.subscription !== null,
            subscription: this.subscription
        };
    }
}

// Initialize notification manager
const notificationManager = new NotificationManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
} else {
    window.NotificationManager = NotificationManager;
    window.notificationManager = notificationManager;
}

// Auto-request permission on user interaction (if not already granted)
document.addEventListener('DOMContentLoaded', function() {
    // Add notification permission request to settings or appropriate UI
    const notificationToggle = document.getElementById('notification-toggle');
    if (notificationToggle) {
        notificationToggle.addEventListener('change', async function() {
            if (this.checked) {
                try {
                    await notificationManager.requestPermission();
                    this.checked = notificationManager.isEnabled();
                } catch (error) {
                    console.error('Failed to enable notifications:', error);
                    this.checked = false;
                    alert('Failed to enable notifications: ' + error.message);
                }
            } else {
                try {
                    await notificationManager.unsubscribe();
                    this.checked = false;
                } catch (error) {
                    console.error('Failed to disable notifications:', error);
                }
            }
        });

        // Set initial state
        notificationToggle.checked = notificationManager.isEnabled();
    }
});

// Listen for messages from service worker
navigator.serviceWorker?.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'NOTIFICATION_CLICK') {
        console.log('Notification clicked:', event.data.notification);
        
        // Handle notification click (e.g., navigate to specific page)
        if (event.data.notification.data && event.data.notification.data.url) {
            window.location.href = event.data.notification.data.url;
        }
    }
});
