/**
 * Immersive 3D Hero Section Styles
 * Advanced 3D effects with Flori Construction Ltd brand colors
 * Professional, elegant aesthetic with sophisticated animations
 * Colors extracted from official logo and branding
 */

/* Brand Color Variables - Extracted from Flori Construction Ltd Logo */
:root {
    /* Primary Brand Gold Colors - From Logo Analysis */
    --flori-gold-primary: #C8860D;      /* Main logo gold */
    --flori-gold-light: #E6A635;       /* Lighter gold accent */
    --flori-gold-dark: #A67C0A;        /* Darker gold shadow */
    --flori-gold-bright: #F4C430;      /* Bright gold highlight */

    /* Secondary Bronze Colors - Complementary Palette */
    --flori-bronze-primary: #B8860B;   /* Primary bronze */
    --flori-bronze-light: #CD853F;     /* Light bronze */
    --flori-bronze-dark: #8B6914;      /* Dark bronze */
    --flori-bronze-copper: #B87333;    /* Copper bronze */

    /* Professional Accent Colors */
    --flori-charcoal: #2C3E50;         /* Professional dark */
    --flori-slate: #34495E;            /* Slate blue-gray */
    --flori-cream: #FDF6E3;            /* Warm cream */
    --flori-white: #FFFFFF;            /* Pure white */

    /* Transparency Variations */
    --flori-gold-alpha-10: rgba(200, 134, 13, 0.1);
    --flori-gold-alpha-20: rgba(200, 134, 13, 0.2);
    --flori-gold-alpha-30: rgba(200, 134, 13, 0.3);
    --flori-gold-alpha-40: rgba(200, 134, 13, 0.4);
    --flori-gold-alpha-50: rgba(200, 134, 13, 0.5);
    --flori-gold-alpha-60: rgba(200, 134, 13, 0.6);
    --flori-gold-alpha-70: rgba(200, 134, 13, 0.7);
    --flori-gold-alpha-80: rgba(200, 134, 13, 0.8);

    --flori-bronze-alpha-10: rgba(184, 134, 11, 0.1);
    --flori-bronze-alpha-20: rgba(184, 134, 11, 0.2);
    --flori-bronze-alpha-30: rgba(184, 134, 11, 0.3);
    --flori-bronze-alpha-40: rgba(184, 134, 11, 0.4);
    --flori-bronze-alpha-50: rgba(184, 134, 11, 0.5);
    --flori-bronze-alpha-60: rgba(184, 134, 11, 0.6);
    --flori-bronze-alpha-70: rgba(184, 134, 11, 0.7);
    --flori-bronze-alpha-80: rgba(184, 134, 11, 0.8);
}

/* 3D Hero Container */
.hero-3d {
    transform-style: preserve-3d;
    perspective: 1000px;
    perspective-origin: 50% 50%;
    position: relative;
    overflow: hidden;
}

/* 3D Background Layers */
.hero-background-3d {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    z-index: 1;
}

.hero-image-container-3d {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
}

.hero-image-3d {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transform: translateZ(-50px) scale(1.05);
    transition: transform 0.3s ease;
}

.hero-overlay-3d {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(26, 26, 26, 0.8) 0%,
        rgba(26, 26, 26, 0.4) 50%,
        rgba(26, 26, 26, 0.7) 100%
    );
    transform: translateZ(10px);
}

.hero-gradient-3d {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        var(--flori-gold-alpha-10) 0%,
        var(--flori-bronze-alpha-10) 30%,
        var(--flori-gold-alpha-20) 60%,
        var(--flori-bronze-alpha-30) 100%
    );
    transform: translateZ(20px);
    mix-blend-mode: overlay;
}

/* 3D Geometric Background Elements */
.hero-3d-geometry {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    pointer-events: none;
    z-index: 1;
}

.geometry-layer {
    position: absolute;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.geo-shape {
    position: absolute;
    background: linear-gradient(45deg, var(--flori-gold-alpha-30), var(--flori-bronze-alpha-20));
    border: 1px solid var(--flori-gold-alpha-40);
    backdrop-filter: blur(10px);
    animation: geoFloat 20s ease-in-out infinite;
    box-shadow:
        0 4px 15px var(--flori-gold-alpha-20),
        inset 0 1px 0 var(--flori-gold-alpha-30);
}

/* Geometric Shape Variations */
.geo-cube {
    width: 40px;
    height: 40px;
    top: 20%;
    left: 15%;
    transform: rotateX(45deg) rotateY(45deg);
    animation-delay: 0s;
}

.geo-pyramid {
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-bottom: 35px solid var(--flori-gold-alpha-40);
    top: 60%;
    right: 20%;
    background: none;
    border-top: none;
    animation-delay: 3s;
    filter: drop-shadow(0 2px 8px var(--flori-gold-alpha-30));
}

.geo-sphere {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    top: 40%;
    right: 10%;
    animation-delay: 6s;
}

.geo-hexagon {
    width: 35px;
    height: 35px;
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    top: 30%;
    left: 70%;
    animation-delay: 2s;
}

.geo-diamond {
    width: 25px;
    height: 25px;
    transform: rotate(45deg);
    top: 70%;
    left: 30%;
    animation-delay: 4s;
}

.geo-octagon {
    width: 32px;
    height: 32px;
    clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
    top: 15%;
    right: 40%;
    animation-delay: 5s;
}

.geo-triangle {
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 26px solid var(--flori-bronze-alpha-40);
    top: 80%;
    right: 60%;
    background: none;
    animation-delay: 1s;
    filter: drop-shadow(0 2px 6px var(--flori-bronze-alpha-30));
}

.geo-pentagon {
    width: 28px;
    height: 28px;
    clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
    top: 50%;
    left: 5%;
    animation-delay: 7s;
}

/* 3D Lighting Effects */
.hero-3d-lighting {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
    transform-style: preserve-3d;
}

.light-beam {
    position: absolute;
    width: 2px;
    height: 100%;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        var(--flori-gold-alpha-60) 20%,
        var(--flori-gold-alpha-30) 50%,
        var(--flori-gold-alpha-60) 80%,
        transparent 100%
    );
    animation: lightBeamMove 8s ease-in-out infinite;
    transform: translateZ(30px);
    box-shadow: 0 0 10px var(--flori-gold-alpha-40);
}

.light-primary {
    left: 20%;
    animation-delay: 0s;
}

.light-secondary {
    right: 25%;
    animation-delay: 4s;
}

.light-ambient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
        ellipse at center,
        var(--flori-gold-alpha-20) 0%,
        var(--flori-bronze-alpha-10) 40%,
        transparent 70%
    );
    animation: ambientPulse 6s ease-in-out infinite;
    transform: translateZ(15px);
}

.light-spotlight {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
        circle at 50% 50%,
        var(--flori-gold-alpha-30) 0%,
        var(--flori-gold-alpha-20) 30%,
        transparent 60%
    );
    transform: translateZ(25px);
    transition: background 0.1s ease;
}

/* 3D Content Styling */
.hero-content-3d {
    transform-style: preserve-3d;
    position: relative;
    z-index: 3;
}

/* 3D Badge Effects */
.hero-badge-3d {
    transform-style: preserve-3d;
    position: relative;
}

.badge-3d-container {
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.badge-3d-container:hover {
    transform: translateZ(10px) rotateX(5deg) rotateY(5deg);
}

.badge-text {
    position: relative;
    z-index: 2;
    display: block;
}

.badge-3d-glow {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, var(--flori-gold-primary), var(--flori-bronze-primary), var(--flori-gold-primary));
    border-radius: 25px;
    opacity: 0;
    animation: badge3DGlow 4s ease-in-out infinite;
    transform: translateZ(-5px);
}

.badge-3d-reflection {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.3) 0%,
        transparent 50%
    );
    border-radius: 20px;
    transform: translateZ(2px);
}

/* 3D Title Effects */
.hero-title-3d {
    transform-style: preserve-3d;
}

.title-line-3d {
    transform-style: preserve-3d;
    position: relative;
    transition: transform 0.3s ease;
}

.title-text {
    position: relative;
    z-index: 2;
    display: block;
}

.title-shadow {
    position: absolute;
    top: 3px;
    left: 3px;
    color: var(--flori-gold-alpha-40);
    transform: translateZ(-10px);
    z-index: 1;
}

.highlight-3d .title-text {
    background: linear-gradient(45deg, var(--flori-gold-primary), var(--flori-bronze-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px var(--flori-gold-alpha-30);
}

.highlight-3d .title-shadow {
    color: var(--flori-bronze-alpha-50);
}

/* 3D Subtitle Effects */
.hero-subtitle-3d {
    transform-style: preserve-3d;
    position: relative;
}

.subtitle-text {
    transform: translateZ(5px);
    position: relative;
    z-index: 2;
}

.subtitle-3d-accent {
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--flori-gold-primary), var(--flori-bronze-primary));
    transform: translateZ(8px);
    animation: accentGlow 3s ease-in-out infinite;
    box-shadow: 0 2px 8px var(--flori-gold-alpha-40);
}

/* 3D Particle System */
.hero-particles-3d {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    pointer-events: none;
    z-index: 2;
}

.particle-layer {
    position: absolute;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.particle-3d {
    position: absolute;
    border-radius: 50%;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
    will-change: transform;
    animation: particle3DFloat linear infinite;
}

/* Enhanced 3D Particle Sizes */
.particle-3d.particle-small {
    width: 4px;
    height: 4px;
    animation-duration: 18s;
    box-shadow: 0 0 10px var(--flori-gold-alpha-50);
}

.particle-3d.particle-medium {
    width: 8px;
    height: 8px;
    animation-duration: 24s;
    box-shadow: 0 0 15px var(--flori-gold-alpha-60);
}

.particle-3d.particle-large {
    width: 12px;
    height: 12px;
    animation-duration: 30s;
    box-shadow: 0 0 20px var(--flori-gold-alpha-70);
}

/* 3D Particle Colors with Depth */
.particle-3d.particle-gold {
    background: radial-gradient(circle, var(--flori-gold-primary) 0%, var(--flori-gold-dark) 70%, var(--flori-gold-alpha-40) 100%);
    box-shadow:
        0 0 15px var(--flori-gold-alpha-60),
        0 0 30px var(--flori-gold-alpha-30),
        inset 0 0 10px rgba(255, 255, 255, 0.2);
}

.particle-3d.particle-bronze {
    background: radial-gradient(circle, var(--flori-bronze-primary) 0%, var(--flori-bronze-dark) 70%, var(--flori-bronze-alpha-40) 100%);
    box-shadow:
        0 0 12px var(--flori-bronze-alpha-60),
        0 0 25px var(--flori-bronze-alpha-30),
        inset 0 0 8px rgba(255, 255, 255, 0.15);
}

/* 3D Geometric Particles */
.particle-3d.particle-diamond {
    border-radius: 0;
    transform: rotate(45deg);
    clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    animation-duration: 22s;
}

.particle-3d.particle-hexagon {
    border-radius: 0;
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    animation-duration: 26s;
}

/* Interactive Elements */
.particle-interactive {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    pointer-events: auto;
    z-index: 3;
}

.interactive-orb {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
    animation: orbFloat 8s ease-in-out infinite;
}

.orb-gold {
    background: radial-gradient(circle at 30% 30%,
        rgba(255, 255, 255, 0.3) 0%,
        var(--flori-gold-alpha-80) 30%,
        var(--flori-bronze-alpha-80) 70%,
        var(--flori-gold-dark) 100%);
    box-shadow:
        0 10px 30px var(--flori-gold-alpha-40),
        inset 0 0 20px rgba(255, 255, 255, 0.2);
    top: 30%;
    right: 15%;
}

.orb-bronze {
    background: radial-gradient(circle at 30% 30%,
        rgba(255, 255, 255, 0.25) 0%,
        var(--flori-bronze-alpha-80) 30%,
        var(--flori-bronze-dark) 70%,
        var(--flori-bronze-copper) 100%);
    box-shadow:
        0 10px 30px var(--flori-bronze-alpha-40),
        inset 0 0 20px rgba(255, 255, 255, 0.15);
    bottom: 25%;
    left: 20%;
    animation-delay: 4s;
}

.interactive-ring {
    position: absolute;
    width: 80px;
    height: 80px;
    border: 3px solid var(--flori-gold-alpha-60);
    border-radius: 50%;
    transform-style: preserve-3d;
    animation: ring3DRotate 12s linear infinite;
}

.ring-gold {
    top: 20%;
    left: 60%;
    border-color: var(--flori-gold-alpha-70);
    box-shadow:
        0 0 20px var(--flori-gold-alpha-30),
        inset 0 0 20px var(--flori-gold-alpha-10);
}

.ring-bronze {
    bottom: 30%;
    right: 25%;
    border-color: var(--flori-bronze-alpha-70);
    box-shadow:
        0 0 20px var(--flori-bronze-alpha-30),
        inset 0 0 20px var(--flori-bronze-alpha-10);
    animation-delay: 6s;
}

/* 3D Animations */
@keyframes geoFloat {
    0%, 100% {
        transform: translateY(0px) rotateX(0deg) rotateY(0deg) translateZ(0px);
    }
    25% {
        transform: translateY(-20px) rotateX(90deg) rotateY(45deg) translateZ(10px);
    }
    50% {
        transform: translateY(-10px) rotateX(180deg) rotateY(90deg) translateZ(20px);
    }
    75% {
        transform: translateY(-25px) rotateX(270deg) rotateY(135deg) translateZ(15px);
    }
}

@keyframes lightBeamMove {
    0%, 100% {
        opacity: 0.3;
        transform: translateX(0px) translateZ(30px) scaleY(1);
    }
    50% {
        opacity: 0.8;
        transform: translateX(10px) translateZ(40px) scaleY(1.1);
    }
}

@keyframes ambientPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1) translateZ(15px);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.05) translateZ(20px);
    }
}

@keyframes badge3DGlow {
    0%, 100% {
        opacity: 0;
        transform: translateZ(-5px) scale(1);
    }
    50% {
        opacity: 0.4;
        transform: translateZ(-3px) scale(1.02);
    }
}

@keyframes accentGlow {
    0%, 100% {
        box-shadow: 0 0 10px var(--flori-gold-alpha-50);
        transform: translateZ(8px) scaleX(1);
    }
    50% {
        box-shadow: 0 0 20px var(--flori-gold-alpha-80);
        transform: translateZ(12px) scaleX(1.1);
    }
}

@keyframes particle3DFloat {
    0% {
        transform: translateY(100vh) translateX(0px) translateZ(0px) rotateX(0deg) rotateY(0deg) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
        transform: translateY(90vh) translateX(15px) translateZ(10px) rotateX(45deg) rotateY(45deg) scale(0.7);
    }
    25% {
        opacity: 1;
        transform: translateY(75vh) translateX(-10px) translateZ(20px) rotateX(90deg) rotateY(90deg) scale(1);
    }
    50% {
        opacity: 0.9;
        transform: translateY(50vh) translateX(20px) translateZ(30px) rotateX(180deg) rotateY(180deg) scale(1.1);
    }
    75% {
        opacity: 0.7;
        transform: translateY(25vh) translateX(-15px) translateZ(20px) rotateX(270deg) rotateY(270deg) scale(0.8);
    }
    90% {
        opacity: 0.3;
        transform: translateY(10vh) translateX(5px) translateZ(10px) rotateX(315deg) rotateY(315deg) scale(0.5);
    }
    100% {
        transform: translateY(-10vh) translateX(0px) translateZ(0px) rotateX(360deg) rotateY(360deg) scale(0);
        opacity: 0;
    }
}

@keyframes orbFloat {
    0%, 100% {
        transform: translateY(0px) translateZ(0px) rotateX(0deg) rotateY(0deg);
    }
    25% {
        transform: translateY(-15px) translateZ(10px) rotateX(5deg) rotateY(5deg);
    }
    50% {
        transform: translateY(-8px) translateZ(20px) rotateX(10deg) rotateY(10deg);
    }
    75% {
        transform: translateY(-20px) translateZ(15px) rotateX(5deg) rotateY(15deg);
    }
}

@keyframes ring3DRotate {
    0% {
        transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px);
    }
    25% {
        transform: rotateX(90deg) rotateY(90deg) rotateZ(90deg) translateZ(10px);
    }
    50% {
        transform: rotateX(180deg) rotateY(180deg) rotateZ(180deg) translateZ(20px);
    }
    75% {
        transform: rotateX(270deg) rotateY(270deg) rotateZ(270deg) translateZ(10px);
    }
    100% {
        transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg) translateZ(0px);
    }
}

/* Responsive 3D Design */
@media (max-width: 1024px) {
    .hero-3d {
        perspective: 800px;
    }

    .geo-shape {
        width: 30px;
        height: 30px;
    }

    .interactive-orb {
        width: 45px;
        height: 45px;
    }

    .interactive-ring {
        width: 60px;
        height: 60px;
    }

    .particle-3d.particle-large {
        width: 10px;
        height: 10px;
    }

    .particle-3d.particle-medium {
        width: 6px;
        height: 6px;
    }

    .particle-3d.particle-small {
        width: 3px;
        height: 3px;
    }
}

@media (max-width: 768px) {
    .hero-3d {
        perspective: 600px;
    }

    .hero-3d-geometry {
        opacity: 0.7;
    }

    .hero-3d-lighting {
        opacity: 0.5;
    }

    .particle-interactive {
        opacity: 0.8;
    }

    .geo-shape {
        width: 20px;
        height: 20px;
    }

    .interactive-orb {
        width: 35px;
        height: 35px;
    }

    .interactive-ring {
        width: 45px;
        height: 45px;
    }

    /* Reduce 3D complexity on mobile */
    .particle-layer.layer-back {
        display: none;
    }

    .geometry-layer.layer-3 {
        display: none;
    }
}

@media (max-width: 480px) {
    .hero-3d {
        perspective: 400px;
    }

    .hero-3d-geometry {
        opacity: 0.5;
    }

    .hero-3d-lighting {
        opacity: 0.3;
    }

    .particle-interactive {
        opacity: 0.6;
    }

    /* Further simplify for small screens */
    .geometry-layer.layer-2 {
        opacity: 0.5;
    }

    .particle-layer.layer-middle {
        opacity: 0.7;
    }

    .interactive-orb {
        width: 25px;
        height: 25px;
    }

    .interactive-ring {
        width: 35px;
        height: 35px;
    }
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
    .hero-3d * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .hero-3d {
        perspective: none;
    }

    .particle-3d,
    .geo-shape,
    .interactive-orb,
    .interactive-ring {
        transform: none !important;
    }
}

/* Hardware Acceleration */
.hero-3d,
.hero-background-3d,
.hero-3d-geometry,
.hero-3d-lighting,
.hero-particles-3d,
.particle-layer,
.particle-3d,
.geo-shape,
.interactive-orb,
.interactive-ring {
    will-change: transform;
    backface-visibility: hidden;
    transform-style: preserve-3d;
}
