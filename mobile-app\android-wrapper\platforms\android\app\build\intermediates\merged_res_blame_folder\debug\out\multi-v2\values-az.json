{"logs": [{"outputFile": "com.floriconstructionltd.admin.app-mergeDebugResources-27:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d86008410acda0874d72ffb71d6307a\\transformed\\jetified-play-services-base-15.0.1\\res\\values-az\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,352,539,670,778,983,1114,1262,1393,1618,1726,1906,2041,2244,2426,2516,2607", "endColumns": "106,186,130,107,204,130,147,130,224,107,179,134,202,181,89,90,105", "endOffsets": "351,538,669,777,982,1113,1261,1392,1617,1725,1905,2040,2243,2425,2515,2606,2712"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2813,2924,3111,3246,3358,3563,3698,3850,4206,4431,4543,4723,4862,5065,5251,5345,5440", "endColumns": "110,186,134,111,204,134,151,134,224,111,179,138,202,185,93,94,109", "endOffsets": "2919,3106,3241,3353,3558,3693,3845,3980,4426,4538,4718,4857,5060,5246,5340,5435,5545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00fa6d42e7a8abf6c20b774bd480ebc9\\transformed\\core-1.9.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5741", "endColumns": "100", "endOffsets": "5837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60c284a716caeffc9a2cb1c843b92804\\transformed\\jetified-play-services-basement-15.0.1\\res\\values-az\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "220", "endOffsets": "467"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3985", "endColumns": "220", "endOffsets": "4201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\56a603f4ce392bc972e3af0135819ac2\\transformed\\jetified-firebase-messaging-17.0.0\\res\\values-az\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "205", "endColumns": "102", "endOffsets": "307"}, "to": {"startLines": "47", "startColumns": "4", "startOffsets": "5550", "endColumns": "106", "endOffsets": "5652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\79e8455b0b361421d45210661fab4253\\transformed\\appcompat-1.6.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,5657", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,5736"}}]}]}