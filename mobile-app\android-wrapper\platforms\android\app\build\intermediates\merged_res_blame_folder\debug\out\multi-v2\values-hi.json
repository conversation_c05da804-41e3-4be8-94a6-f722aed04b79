{"logs": [{"outputFile": "com.floriconstructionltd.admin.app-mergeDebugResources-27:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d86008410acda0874d72ffb71d6307a\\transformed\\jetified-play-services-base-15.0.1\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,351,543,667,777,987,1115,1253,1390,1606,1713,1910,2038,2238,2417,2506,2599", "endColumns": "105,191,123,109,209,127,137,136,215,106,196,127,199,178,88,92,112", "endOffsets": "350,542,666,776,986,1114,1252,1389,1605,1712,1909,2037,2237,2416,2505,2598,2711"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2787,2897,3089,3217,3331,3541,3673,3815,4161,4377,4488,4685,4817,5017,5200,5293,5390", "endColumns": "109,191,127,113,209,131,141,140,215,110,196,131,199,182,92,96,116", "endOffsets": "2892,3084,3212,3326,3536,3668,3810,3951,4372,4483,4680,4812,5012,5195,5288,5385,5502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60c284a716caeffc9a2cb1c843b92804\\transformed\\jetified-play-services-basement-15.0.1\\res\\values-hi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "204", "endOffsets": "451"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3956", "endColumns": "204", "endOffsets": "4156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\56a603f4ce392bc972e3af0135819ac2\\transformed\\jetified-firebase-messaging-17.0.0\\res\\values-hi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "205", "endColumns": "98", "endOffsets": "303"}, "to": {"startLines": "47", "startColumns": "4", "startOffsets": "5507", "endColumns": "102", "endOffsets": "5605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00fa6d42e7a8abf6c20b774bd480ebc9\\transformed\\core-1.9.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5692", "endColumns": "100", "endOffsets": "5788"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\79e8455b0b361421d45210661fab4253\\transformed\\appcompat-1.6.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,5610", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,5687"}}]}]}