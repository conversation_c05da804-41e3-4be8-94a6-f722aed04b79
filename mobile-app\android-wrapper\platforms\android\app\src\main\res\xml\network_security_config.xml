<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Allow cleartext traffic for development -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Local development -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********</domain>
        
        <!-- Add your local network IP ranges here -->
        <domain includeSubdomains="true">***********/24</domain>
        <domain includeSubdomains="true">***********/24</domain>
        <domain includeSubdomains="true">10.0.0.0/8</domain>
        
        <!-- Production domain -->
        <domain includeSubdomains="true">floriconstructionltd.com</domain>
    </domain-config>
    
    <!-- Base configuration for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- Trust system CAs -->
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
    
    <!-- Debug overrides (remove in production) -->
    <debug-overrides>
        <trust-anchors>
            <!-- Trust user added CAs while debuggable only -->
            <certificates src="user"/>
            <certificates src="system"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>
